package yyy.xxx.simpfw.module.uis.service.impl;

import org.springframework.stereotype.Service;

import yyy.xxx.simpfw.module.pacs.constants.Constants;
import yyy.xxx.simpfw.module.pacs.service.IRemoteQueueService;

@Service("UisIRemoteQueueService")
public class RemoteQueueServiceImpl implements IRemoteQueueService {

	@Override
	public String getQueueServiceBaseUrlKey() {
		return Constants.PARAM_UIS_QUEUE_SERVICE_BASEURL;
	}

}
