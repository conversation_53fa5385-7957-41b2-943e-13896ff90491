package yyy.xxx.simpfw.framework.auth.web;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import yyy.xxx.simpfw.common.core.domain.AjaxResult;

import yyy.xxx.simpfw.common.core.domain.model.LoginUser;
import yyy.xxx.simpfw.framework.auth.QRAuthConst;
import yyy.xxx.simpfw.framework.auth.bo.QRAuthResult;
import yyy.xxx.simpfw.framework.auth.service.QRAuthService;

import javax.servlet.http.HttpServletRequest;

/**
 * `医信签鉴权接口
 * @version 
 */
@RestController
@RequestMapping(value = "/qrauth")
public class QRAuthController implements QRAuthConst {
    
    private static Logger logger = LoggerFactory.getLogger(QRAuthController.class);

    @Autowired
    private QRAuthService service;

    private static final String paramSessionId = "sessionId";

    /**
     * `获取签名二维码
     * @param request
     */
    @RequestMapping(value = "/code")
    public AjaxResult code(HttpServletRequest request) {//if(System.currentTimeMillis() > 0) { return ;}

    	String sessionId = request.getParameter(paramSessionId);

        try {
            QRAuthResult authResultData = service.code(sessionId);

            AjaxResult res = AjaxResult.success();
            String codeData = authResultData.getOauthMPCode();
            res.put(AjaxResult.DATA_TAG, Base64.decodeBase64(codeData));

            res.put(AjaxResult.MSG_TAG, authResultData.getOauthWindowURL());

            return res;
        } catch (Exception err) {
            logger.error(err.getMessage(), err);
            return AjaxResult.error(err.getMessage());
        }
    }

    /**
     * `给定sessionId授权状态
     * @param request
     * @return
     */
    @RequestMapping(value = "/authcheck")
    public AjaxResult authcheck(HttpServletRequest request) {

    	String sessionId = request.getParameter(paramSessionId);
        String forRel = request.getParameter("forRel");

        LoginUser luser;
        try {
            luser = service.authcheck(sessionId, "true".equals(forRel));
        } catch (Exception err) {
            logger.error(err.getMessage(), err);
            return AjaxResult.error(err.getMessage());
        }

    	AjaxResult ret = AjaxResult.success();
        if(null != luser) {
            ret.put("token", luser.getToken());
        }
    	return ret;
    }

    @RequestMapping(value = "/cfg")
    public AjaxResult config() {//if(System.currentTimeMillis() > 0) { return ;}
        try {
            if (!service.checkConfig()) {
                return AjaxResult.error();
            }

            return AjaxResult.success();
        } catch (Exception err) {
            return AjaxResult.error(err.getMessage());
        }
    }
}