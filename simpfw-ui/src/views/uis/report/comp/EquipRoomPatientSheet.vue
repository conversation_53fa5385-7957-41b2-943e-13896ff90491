<template>
<el-container ref="patientListWrap" class="nested-container" style="height: 100%">
  <el-header height="auto">
    <div class="searchFormBar">
      <el-form>
        <el-select v-model="searchForm.propName" style="width: 120px">
          <el-option
            v-for="dict in searchForm.combo_props"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>      
        <el-input v-model="searchForm.propValue"
        @focus="handleFocusRegistCode"  @keyup.enter.native="getList" 
        clearable style="width: 160px" />
        <el-tooltip class="item" effect="dark" content="查询" placement="bottom">
          <el-button type="primary" icon="el-icon-search" @click="getList"></el-button></el-tooltip>
        <el-tooltip class="item" effect="dark" content="复呼" placement="bottom">
          <el-button type="primary" icon="el-icon-microphone" @click="toCall(currentRow)"></el-button></el-tooltip>
        <el-tooltip class="item" effect="dark" content="恢复排队" placement="bottom">
          <el-button type="warning" icon="el-icon-s-promotion" @click="handleQueuePast"></el-button></el-tooltip>
        <el-tooltip class="item" effect="dark" content="偏好配置" placement="bottom">
          <el-button type="primary" icon="el-icon-s-tools" @click="prepareSearchOpt"></el-button></el-tooltip>
      </el-form>
    </div>
  </el-header>

  <el-main class="flex-container-column">
    <div class="flex-item-fill scroll-auto">

      <el-table ref="dataGrid" v-loading="loading" :data="grid.data" row-key="id"
       stripe highlight-current-row height="100%"
       @current-change="selectTableRow"
       @row-contextmenu="showTableAction"
       @row-dblclick="handleTableRowDblClick">

        <el-table-column label="操作" fixed="left" align="center" width="60" class-name="button-col">
          <template slot-scope="scope">
            <el-button
              size="mini"
              icon="el-icon-menu"
              @click="handleDetail(scope.row, $event)"
            ></el-button>
          </template>
        </el-table-column>

        <el-table-column prop="callInfo.callNo" label="排队号" width="60" :formatter="colFmt_object" />
        <el-table-column prop="patientInfo.name" label="患者姓名" width="80" :formatter="colFmt_object" />
        <el-table-column prop="patientInfo.gender.dictLabel" label="性别" width="50"
        :formatter="colFmt_dictData" />
        <el-table-column prop="patientInfo.age" label="年龄" width="60" :formatter="colFmt_age" />
        <el-table-column prop="examItem.dictLabel" label="检查项目" width="100" />
        <el-table-column prop="examParts.partsName" label="检查部位" width="100" :formatter="colFmt_object" show-overflow-tooltip /> 
        <el-table-column prop="resultStatus.dictLabel" label="工作状态" width="100">
            <template slot-scope="scope" v-if="!!scope.row.resultStatus">
                <i class="el-icon-error state-icon-err"
                    v-if="2 === scope.row.status || '10' === scope.row.resultStatus.dictValue"></i>
                <i :class="'el-icon-success' + ' result-status-icon-' + scope.row.resultStatus.dictValue"
                    v-else></i>
                <span style="margin-left: 4px">{{ 2 === scope.row.status? '已删除' : (!!scope.row.resultStatus?
                    scope.row.resultStatus.dictLabel : null) }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="createTime" label="登记时间" min-width="160" />
        <el-table-column prop="examTime" label="检查时间" min-width="160" />
        <el-table-column prop="examNo" label="检查号" width="120" />
        <el-table-column prop="equipRoom.roomName" label="检查房间" width="100" :formatter="colFmt_equipRoom" />
        <el-table-column prop="patientInfo.registNo" label="登记号" min-width="140"
            :formatter="colFmt_object" />
        <!-- <el-table-column label="工作状态" width="80">
          <template slot-scope="scope">
            <el-button type="text"
              @click="writeReport(scope.row)"
            >{{colFmt_reportStatus(scope.row)}}</el-button>
          </template>
        </el-table-column> -->

      </el-table>
    </div>

    <pagination small layout="total, prev, pager, next"
      v-show="grid.pager.total>0"
      :total="grid.pager.total"
      :page.sync="searchForm.pageNum"
      :limit.sync="searchForm.pageSize"
      @pagination="getList"
    />

    <!-- 右键菜单 -->
    <Contextmenu ref="tableContextmenu" :items="tableAction" @select="handleTableAction" />
    <!-- 查询偏好设置 -->
    <PatientListSearchOptions ref="searchOpt" cacheKey="report::write::patientListSearchOptions" @change="getList" />
    <!-- 拆分检查 -->
    <ExamSplit ref="examSplit" @success="getList" />
    
  </el-main>
</el-container>

</template>

<style scoped>
.searchFormBar {
  margin-bottom: 4px;
}
.searchFormBar >>> .el-button{
  margin-left: 4px;
  padding: 10px;
}
</style>

<style scoped src="@/assets/styles/pacs/exammanagement/patient/Index.css"></style>
<style scoped src="@/assets/styles/pacs/exammanagement/patient/exam-info-icon.css"></style>
<script>
import model from "@/assets/scripts/uis/report/comp/EquipRoomPatientSheet";
export default model;
</script>
