package yyy.xxx.simpfw.framework.auth.web;

import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import yyy.xxx.simpfw.common.core.domain.AjaxResult;
import yyy.xxx.simpfw.common.core.domain.model.LoginUser;
import yyy.xxx.simpfw.common.core.redis.RedisCache;
import yyy.xxx.simpfw.common.utils.SecurityUtils;
import yyy.xxx.simpfw.common.utils.StringUtils;
import yyy.xxx.simpfw.framework.auth.QRAuthConst;
import yyy.xxx.simpfw.framework.auth.bo.CertAuthCode;
import yyy.xxx.simpfw.framework.auth.bo.gxca.GetDoctorInfoResponse;
import yyy.xxx.simpfw.framework.auth.bo.gxca.GetSignInfoResponse;
import yyy.xxx.simpfw.framework.auth.bo.gxca.HttpServerException;
import yyy.xxx.simpfw.framework.auth.constant.CAProvider;
import yyy.xxx.simpfw.framework.auth.entity.ExamCASignEntityWithBLOBs;
import yyy.xxx.simpfw.framework.auth.service.TestAuthService;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

/**
 * 测试环境CA扫码认证
 */
@Controller
@RequestMapping(value = "/testAuth")
public class TestAuthController implements QRAuthConst {

    private static final Logger logger = LoggerFactory.getLogger(TestAuthController.class);

    private static final String signId = "testSign";

    @Autowired
    private TestAuthService service;

    @Autowired
    private TestAuthService testAuthService;

    @RequestMapping(value = "/createSign")
    @ResponseBody
    public AjaxResult createSign(@RequestPart("signFile") MultipartFile signFile) {
        return AjaxResult.success("创建签名", signId);
    }

    @RequestMapping(value = "/getSignInfo")
    @ResponseBody
    public AjaxResult getSignInfo(@RequestPart("signNum") String signNum) {
        GetSignInfoResponse response;
        try {
            response = testAuthService.getSignInfo(signNum);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success(response);
    }

    @RequestMapping(value = "/saveSign")
    @ResponseBody
    public AjaxResult saveSign(@RequestPart("examUid") String examUid,
                               @RequestPart("signFilePath") String signFilePath) {
        try {
            String loginToken = SecurityUtils.getLoginUser().getToken();
            logger.info("登录用户token：{}", loginToken);
            GetSignInfoResponse signOutput = testAuthService.getSignInfo(signId);
            CertAuthCode authCode = testAuthService.getAuthCode();
            GetDoctorInfoResponse doctorInfo = testAuthService.getDoctorInfo();

            if (null == authCode || null == doctorInfo) {
                return AjaxResult.error("登录信息已过期，请重新扫码登录");
            }
            if (null == signOutput) {
                return AjaxResult.error("签署信息已过期，请重新审核签署。");
            }

            ExamCASignEntityWithBLOBs signEntity = new ExamCASignEntityWithBLOBs();
            signEntity.setExamuid(examUid);
            signEntity.setLoginUserId(SecurityUtils.getUserId());
            signEntity.setSignUserId(Long.valueOf(authCode.getUserUid()));
            signEntity.setCaProvider(CAProvider.TEST.getCode());
            signEntity.setFilePath(signFilePath);
            signEntity.setSignInput("");
            signEntity.setSignOutput(JSON.toJSONString(signOutput));
            service.saveCASign(signEntity);

            return AjaxResult.success("CA签署成功。");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * `检查配置
     *
     * @return
     */
    @RequestMapping(value = "/cfg")
    @ResponseBody
    public AjaxResult config(HttpServletRequest request) {
        try {
            if (!service.checkConfig()) {
                return AjaxResult.error();
            }

            return AjaxResult.success();
        } catch (Exception err) {
            return AjaxResult.error(err.getMessage());
        }
    }
}
