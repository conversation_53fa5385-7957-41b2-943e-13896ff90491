package yyy.xxx.simpfw.framework.auth.entity;

public class ExamCASignEntityWithBLOBs extends ExamCASignEntity {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column d_exam_ca_sign.file_path
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    private String filePath;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column d_exam_ca_sign.sign_input
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    private String signInput;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column d_exam_ca_sign.sign_output
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    private String signOutput;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column d_exam_ca_sign.file_path
     *
     * @return the value of d_exam_ca_sign.file_path
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    public String getFilePath() {
        return filePath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column d_exam_ca_sign.file_path
     *
     * @param filePath the value for d_exam_ca_sign.file_path
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column d_exam_ca_sign.sign_input
     *
     * @return the value of d_exam_ca_sign.sign_input
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    public String getSignInput() {
        return signInput;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column d_exam_ca_sign.sign_input
     *
     * @param signInput the value for d_exam_ca_sign.sign_input
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    public void setSignInput(String signInput) {
        this.signInput = signInput;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column d_exam_ca_sign.sign_output
     *
     * @return the value of d_exam_ca_sign.sign_output
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    public String getSignOutput() {
        return signOutput;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column d_exam_ca_sign.sign_output
     *
     * @param signOutput the value for d_exam_ca_sign.sign_output
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    public void setSignOutput(String signOutput) {
        this.signOutput = signOutput;
    }
}