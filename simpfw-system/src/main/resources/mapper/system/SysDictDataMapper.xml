<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="yyy.xxx.simpfw.system.mapper.SysDictDataMapper">

	<resultMap type="SysDictData" id="SysDictDataResult">
		<id     property="dictCode"   column="dict_code"   />
		<result property="dictSort"   column="dict_sort"   />
		<result property="dictLabel"  column="dict_label"  />
		<result property="dictValue"  column="dict_value"  />
		<result property="dictType"   column="dict_type"   />
		<result property="cssClass"   column="css_class"   />
		<result property="listClass"  column="list_class"  />
		<result property="isDefault"  column="is_default"  />
		<result property="status"     column="status"      />
		<result property="createBy"   column="create_by"   />
		<result property="createTime" column="create_time" />
		<result property="updateBy"   column="update_by"   />
		<result property="updateTime" column="update_time" />

		<association property="extend" javaType="yyy.xxx.simpfw.common.core.domain.model.Extend" autoMapping="true" columnPrefix="ext__" />

		<association property="parent" javaType="SysDictData" autoMapping="true" columnPrefix="p__" />
	</resultMap>

	<resultMap type="SysDictData" id="SysDictDataResultMap" extends="SysDictDataResult">
		<collection property="children" ofType="SysDictData" select="selectSubDictData" column="{dictType=dict_type,parentDictCode=sub_sup_dict_code}" />
	</resultMap>
	
	<sql id="selectDictDataVo">
        select a.dict_code, a.dict_sort, a.dict_label, a.dict_value, a.dict_type, a.css_class, a.list_class, a.is_default, a.status, a.create_by, a.create_time, a.remark
		,a.extend_s1 ext__extendS1,a.extend_s2 ext__extendS2,a.extend_s3 ext__extendS3,a.extend_s4 ext__extendS4
		,a.extend_i1 ext__extendI1,a.extend_i2 ext__extendI2
		,a.extend_f1 ext__extendF1,a.extend_f2 ext__extendF2

		,p.dict_code p__dictCode,p.dict_label p__dictLabel,p.dict_value p__dictValue,p.dict_sort p__dictSort

		,a.dict_code sub_sup_dict_code

		from sys_dict_data a
		left join sys_dict_data p on p.dict_code=a.parent_dict_code
    </sql>

	<select id="selectDictDataList" parameterType="SysDictData" resultMap="SysDictDataResultMap">
	    <include refid="selectDictDataVo"/>
		<where>
		    <if test="dictType != null and dictType != ''">
				AND a.dict_type = #{dictType}
			</if>
			<if test="dictLabel != null and dictLabel != ''">
				AND a.dict_label like concat('%', #{dictLabel}, '%')
			</if>
			<if test="status != null and status != ''">
				AND a.status = #{status}
			</if>

			<choose>
				<when test="null==parent or null==parent.dictCode"> and (a.parent_dict_code is null or a.parent_dict_code='0')</when>
				<otherwise> and a.parent_dict_code=#{parent.dictCode}</otherwise>
			</choose>
		</where>
		order by a.dict_sort asc,a.dict_code
	</select>
	
	<select id="selectDictDataByType" parameterType="SysDictData" resultMap="SysDictDataResult">
		<include refid="selectDictDataVo"/>
		where a.status = '0' and a.dict_type = #{dictType} order by a.dict_sort asc
	</select>
	
	<select id="selectDictLabel" resultType="String">
		select dict_label from sys_dict_data
		where dict_type = #{dictType} and dict_value = #{dictValue}
	</select>
	
	<select id="selectDictDataById" parameterType="Long" resultMap="SysDictDataResult">
		<include refid="selectDictDataVo"/>
		where a.dict_code = #{dictCode}
	</select>

	<select id="selectDictDataByValue" resultMap="SysDictDataResult">
		<include refid="selectDictDataVo"/>
		where a.dict_type = #{dictType}
		and a.dict_value=#{dictValue}
	</select>

	<select id="countDictDataByType" resultType="Integer">
	    select count(1) from sys_dict_data where dict_type=#{dictType}  
	</select>
	
	<delete id="deleteDictDataById" parameterType="Long">
 		delete from sys_dict_data where dict_code = #{dictCode}
 	</delete>
 	
 	<delete id="deleteDictDataByIds" parameterType="Long">
 		delete from sys_dict_data where dict_code in
 		<foreach collection="array" item="dictCode" open="(" separator="," close=")">
 			#{dictCode}
        </foreach> 
 	</delete>
	
	<update id="updateDictData" parameterType="SysDictData">
 		update sys_dict_data
 		<set>
 			<if test="dictSort != null">dict_sort = #{dictSort},</if>
 			<if test="dictLabel != null and dictLabel != ''">dict_label = #{dictLabel},</if>
 			<if test="dictValue != null and dictValue != ''">dict_value = #{dictValue},</if>
 			<if test="dictType != null and dictType != ''">dict_type = #{dictType},</if>
 			<if test="cssClass != null">css_class = #{cssClass},</if>
 			<if test="listClass != null">list_class = #{listClass},</if>
 			<if test="isDefault != null and isDefault != ''">is_default = #{isDefault},</if>
 			<if test="status != null">status = #{status},</if>
 			<if test="remark != null">remark = #{remark},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
			<if test="null != extend">
			extend_s1=#{extend.extendS1},extend_s2=#{extend.extendS2},extend_s3=#{extend.extendS3},extend_s4=#{extend.extendS4},
			extend_i1=#{extend.extendI1},extend_i2=#{extend.extendI2},
			extend_f1=#{extend.extendF1},extend_f2=#{extend.extendF2},
			</if>
 			update_time = sysdate()
 		</set>
 		where dict_code = #{dictCode}
	</update>
	
	<update id="updateDictDataType" parameterType="String">
 		update sys_dict_data set dict_type = #{newDictType} where dict_type = #{oldDictType}
	</update>
 	
 	<insert id="insertDictData" parameterType="SysDictData">
 		insert into sys_dict_data(
 			<if test="dictSort != null">dict_sort,</if>
 			<if test="dictLabel != null and dictLabel != ''">dict_label,</if>
 			<if test="dictValue != null and dictValue != ''">dict_value,</if>
 			<if test="dictType != null and dictType != ''">dict_type,</if>
 			<if test="cssClass != null and cssClass != ''">css_class,</if>
 			<if test="listClass != null and listClass != ''">list_class,</if>
 			<if test="isDefault != null and isDefault != ''">is_default,</if>
 			<if test="status != null">status,</if>
 			<if test="remark != null and remark != ''">remark,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
			extend_s1,extend_s2,extend_s3,extend_s4,
			extend_i1,extend_i2,
			extend_f1,extend_f2,
			parent_dict_code,
 			create_time
 		)values(
 		    <if test="dictSort != null">#{dictSort},</if>
 		    <if test="dictLabel != null and dictLabel != ''">#{dictLabel},</if>
 			<if test="dictValue != null and dictValue != ''">#{dictValue},</if>
 			<if test="dictType != null and dictType != ''">#{dictType},</if>
 			<if test="cssClass != null and cssClass != ''">#{cssClass},</if>
 			<if test="listClass != null and listClass != ''">#{listClass},</if>
 			<if test="isDefault != null and isDefault != ''">#{isDefault},</if>
 			<if test="status != null">#{status},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
			<choose><when test="null!=extend">ifnull(#{extend.extendS1},null),ifnull(#{extend.extendS2},null),
				ifnull(#{extend.extendS3},null),ifnull(#{extend.extendS4},null),
				ifnull(#{extend.extendI1},null),ifnull(#{extend.extendI2},null),
				ifnull(#{extend.extendF1},null),ifnull(#{extend.extendF2},null),
			</when><otherwise>null,null,null,null,null,null,null,null,</otherwise></choose>
			<choose><when test="null!=parent and null!=parent.dictCode">#{parent.dictCode}</when><otherwise>'0'</otherwise></choose>,
 			sysdate()
 		)
	</insert>

	<select id="selectSubDictData" resultMap="SysDictDataResult">
		<choose><when test="null!=dictType and null!=parentDictCode">
		<include refid="selectDictDataVo" />
		where a.dict_type=#{dictType} and a.parent_dict_code=#{parentDictCode}
		order by a.dict_sort,a.dict_value
		</when><otherwise>select null where 1=0</otherwise></choose>

	</select>
</mapper> 