package yyy.xxx.simpfw.module.pacs.vo;

import yyy.xxx.simpfw.module.pacs.entity.WritePhrase;
import yyy.xxx.simpfw.common.core.domain.TreeSelect;

public class WritePhraseVo extends WritePhrase {
    public TreeSelect convTreeNode() {
        TreeSelect node = new TreeSelect();
        node.setId(getId());
        node.setLabel(getPhraseContent());
        node.setData(this);

        return node;
    }
}
