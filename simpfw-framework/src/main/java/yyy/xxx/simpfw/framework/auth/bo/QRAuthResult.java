package yyy.xxx.simpfw.framework.auth.bo;

import java.io.Serializable;

/**
 * `请求二维码返回数据
 *<pre>
 *{
 *"status": "0",
 *"message": "success",
 *"data":    {
 *  	"transactionId": "a5fce71613c149c8951ba028e603f0c8",
 *  	"oauthMPCode": "iVBO....==",
 *  	"oauthWindowURL": "http://..../h5/authwindow/index.html?t=a5fce71613c149c8951ba028e603f0c8"
 *	}
 *}
 *</pre>
 */

public class QRAuthResult implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	private String accessToken, transactionId;
	private transient String oauthMPCode;
	private transient String oauthWindowURL;

	public String getTransactionId() {
		return transactionId;
	}
	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}

	public String getOauthMPCode() {
		return oauthMPCode;
	}
	public void setOauthMPCode(String oauthMPCode) {
		this.oauthMPCode = oauthMPCode;
	}

	public String getOauthWindowURL() {
		return oauthWindowURL;
	}
	public void setOauthWindowURL(String oauthWindowURL) {
		this.oauthWindowURL = oauthWindowURL;
	}
	
	public String getAccessToken() {
		return accessToken;
	}
	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}
	
}
