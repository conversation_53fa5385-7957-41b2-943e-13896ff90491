package yyy.xxx.simpfw.framework.auth.service.test;

import org.apache.commons.io.FileUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.charset.StandardCharsets;

@Aspect
@Component
public class ApiTester {
    //token + 二维码
    @Pointcut("execution(* yyy.xxx.simpfw.common.core.service.HttpClientService.ajaxPost(..))")
    private void ajaxPost(){}
    //
    @Around("ajaxPost()")
    public Object ajaxPost(ProceedingJoinPoint proceedingJoinPoint) throws Throwable{
        String url = (String)proceedingJoinPoint.getArgs()[0];
        if(url.contains("/api/v1.0/getAccessToken")) {
            return getAccessToken();
        } else if(url.contains("/doctor/api/v1.0/auth/getOauthStatus")) {
            return getOauthStatus();
        } else if(url.contains("/doctor/api/v1.0/auth/oauth")) {
            return oauth();
        }

        return proceedingJoinPoint.proceed();
    }

    private Object getAccessToken() throws Exception {
        Thread.sleep((int)(Math.random() * 2000));

        return "{\"status\":\"0\",\"message\":\"success\",\"data\":{\"accessToken\":\"9a821efefd9644c5bd85b69efce19ce7_11\"}}";
    }

    private Object oauth() throws Exception{

        Thread.sleep((int)(Math.random() * 1000));
        //
        File file = new File(
                new File(Thread.currentThread().getContextClassLoader().getResource("/").toURI()).getParentFile().getParentFile().getParentFile()
                , "simpfw-framework/src/main/java/"
                + ApiTester.class.getPackage().getName().replace(".", "/")
                + "/mpCode.dat");
        String mpCode = FileUtils.readFileToString(file, StandardCharsets.UTF_8);

        return "{\"status\":\"0\",\"message\":\"success\",\"data\":{\"transactionId\":\"emk35ifc90j9bg6j\",\"oauthMPCode\":\"" + mpCode + "\",\"oauthWindowURL\":\"http://10.108.155.35:8091/h5/authwindow/index.html?t=emk35ifc90j9bg6j\"}}";
    }

    private Object getOauthStatus() throws Exception {
        Thread.sleep(2000);

        return "{\"status\":\"0\",\"message\":\"success\",\"data\":{\"oauthStatus\":\"1\",\"userId\":\"18107712392\",\"userName\":\"陆志宏\",\"certDN\":\"C=CN,ST=广西壮族自治区,L=南宁市,O=广西医科大学第二附属医院-1637992668792,OU=广西医科大学第二附属医院,CN=陆志宏\",\"certSN\":\"790000000015728e\",\"certStartTime\":\"2021-11-27 13:14:41\",\"certEndTime\":\"2022-11-27 13:14:40\",\"oauthSignature\":null,\"authKEY\":\"fa38db561b8e4b648b78585b075d2256\",\"authTime\":30,\"expireTime\":1798,\"authStartTime\":\"2022-08-15 23:35:55\",\"authEndTime\":\"2022-08-16 00:05:55\",\"idcard\":\"******************\",\"transactionId\":\"emk35ifc90j9bg6j\",\"userPhone\":\"18107712392\",\"officeName\":\"医信签\",\"callbackURL\":null,\"authType\":\"1\",\"oauthMethod\":\"4\",\"optUserId\":\"18107712392\",\"signatureImg\":\"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\",\"officeQyId\":\"229\"}}";
    }
}


