package yyy.xxx.simpfw.framework.auth.bo;

/**
 * 医信签接口配置
 */
public class QRAuthApiProp {
    private String server, appid, appkey,oauthMethod;

    public String getServer() {
        return server;
    }

    public String getAppid() {
        return appid;
    }

    public String getAppkey() {
        return appkey;
    }

    public String getOauthMethod() { return oauthMethod; }

    public QRAuthApiProp(String server, String appid, String appkey, String oauthMethod) {
        this.server = server;
        this.appid = appid;
        this.appkey = appkey;
        this.oauthMethod = oauthMethod;
    }
}
