package yyy.xxx.simpfw.framework.auth.bo;

import java.io.Serializable;

public class CertAuthCode implements Serializable {
    private String id, userUid;
    private String cert, userToken;
    private Long expired;

    public String getId() {
        return id;
    }
    public void setId(String id) {
        this.id = id;
    }

    public String getCert() {
        return cert;
    }
    public void setCert(String cert) {
        this.cert = cert;
    }

    public String getUserToken() {
        return userToken;
    }
    public void setUserToken(String userToken) {
        this.userToken = userToken;
    }

    public Long getExpired() {
        return expired;
    }
    public void setExpired(Long expired) {
        this.expired = expired;
    }

    public String getUserUid() {
        return userUid;
    }
    public void setUserUid(String userUid) {
        this.userUid = userUid;
    }

    public CertAuthCode() { }

    public CertAuthCode(String id, String cert, Long expired) {
        this.id = id;
        this.cert = cert;
        this.expired = expired;
    }
}
