package yyy.xxx.simpfw.module.pacs.bo;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

public class PatAdmInfoList extends AbstractReturn {

    private List<AddRisAppBillRt> addRisAppBillRts;

    public List<AddRisAppBillRt> getAddRisAppBillRt() {
        return addRisAppBillRts;
    }

    @JSONField(name = "addRisAppBillRt")
    public void setAddRisAppBillRts(List<AddRisAppBillRt> addRisAppBillRts) {
        this.addRisAppBillRts = addRisAppBillRts;
    }
}
