package yyy.xxx.simpfw.framework.auth;

import yyy.xxx.simpfw.common.core.domain.AjaxResult;

public interface QRAuthConst {

    String ERRM_sessionInvalid = "已失效";
    String ERRM_sessionExpired = "已过期。", ERRM_NOAUTH = "无认证依据。";

    String charsetUTF8 = "UTF-8", imageType = "image/png", zero = "0";
    String sessionTailZero = "_0", sessionLeadCAPin = "CA::PIN.", sessionTpl = "%s%s" + sessionTailZero;

    String cacheKeyPattern = "qr_auth#%s";

    String cacheTokenPattern = "qr_auth_trans_token#%s";

    String cacheOutputDataPattern = "qr_auth_output_data#%s";

    String nameExpiredTime = "expiredTime";
    //分钟
    int invalidStatus = -1, expiredTime = 5 * 60;

    enum CAUserType {
        cert(0), auth(1);

        private int type;
        public int getType() {
            return type;
        }
        private CAUserType(int type) {
            this.type = type;
        }
    };
}
