package yyy.xxx.simpfw.module.pacs.vo;

import yyy.xxx.simpfw.module.pacs.entity.Hospital;
import yyy.xxx.simpfw.common.core.domain.TreeSelect;

import java.util.ArrayList;
import java.util.List;

public class HospitalVo extends Hospital {

    public TreeSelect convTreeNode() {
        TreeSelect node = new TreeSelect();
        node.setId(getId());
        node.setLabel(getHospitalName());
        node.setData(this);

        return node;
    }

    public static List<TreeSelect> buildTree(List<HospitalVo> items) {
        List<TreeSelect> nodes = new ArrayList<>();

        if(null != items) {
            for(HospitalVo r : items) {
                TreeSelect node = r.convTreeNode();
                node.setChildren(new ArrayList<>());
                nodes.add(node);
            }
        }

        return nodes;
    }
}
