<template>
<el-dialog ref="tplDialog" :visible.sync="visible" append-to-body
 :modal="false" :show-close="false"
 width="600px" class="popupdialog popupdialog-noheader" destroy-on-close>

  <el-form label-width="80px" class="tight-form">
    <el-row>
      <el-col :span="24">
        <el-form-item label="检查所见">
          <el-input type="textarea" v-model="tplNode.data.examDesc" :rows="8" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="检查诊断">
          <el-input type="textarea" v-model="tplNode.data.examDiagnosis" :rows="4" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="术后医嘱">
          <el-input type="textarea" v-model="tplNode.data.operationSuggestion" :rows="4" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-radio-group v-model="mode">
          <el-radio label="0">替换当前信息</el-radio>
          <el-radio label="1">追加模板</el-radio>
          <el-radio label="2">当前位置插入</el-radio>
        </el-radio-group>
      </el-col>
    </el-row>
  </el-form>
  <div slot="footer" class="dialog-footer">
    <span class="buttons-left">
      <el-button type="primary" size="mini" @click="submitUpdate()">保存更新</el-button>
    </span>
    <el-button type="primary" size="mini" @click="apply('examDesc')">添加所见</el-button>
    <el-button type="primary" size="mini" @click="apply('examDiagnosis')">添加诊断</el-button>
    <el-button type="primary" size="mini" @click="apply()">添加所有</el-button>
    <el-button type="primary" size="mini" @click="close">取 消</el-button>
  </div>
</el-dialog>
</template>

<script>
import * as api from "@/assets/scripts/pacs/tplcfg/template/api";

const model = {
  data() {
    return {
      visible: false,

      mode: "1",

      tplNode: {
        data: {
          id: null,
          examDesc: null,
          examDiagnosis: null,
          operationSuggestion: null
        }
      }
    }
  },

  methods: {
    //显示
    view(tplNode) {
      if(!tplNode || !tplNode.data) {
        return this.close();
      }
      this.visible = true;

      this.tplNode = tplNode;

      this.$nextTick(this.fixPosition);
    },
    //添加/应用
    apply(prop) {
      let props = prop? [prop] : null;
      this.triggerBind("applyTemplate", this.tplNode, props, parseInt(this.mode));

      this.close();
    },
    //隐藏
    close() {
      this.visible = false;
    },
    //设置模板信息窗口位置取消遮罩
    fixPosition() {
      const dialog = this.$refs.tplDialog.$el;
      const dialogInner = dialog.querySelector(".el-dialog:first-child");
      //console.log(dialogInner.offsetTop, dialogInner.offsetLeft, dialogInner.clientWidth, dialogInner.clientHeight);
      const dialogRight = (document.querySelector(".report-writer-wrap").parentNode.clientWidth / 0.6) * 0.4;
      dialog.style.cssText = "width: 600px; left: unset; top: unset; right: " + dialogRight + "px; bottom: 100px;";
      
      dialogInner.style.cssText = "width: 99.6%; margin: 2px auto !important; box-shadow: 0 1px 3px rgb(0 0 0)";
    },
    //保存更新
    submitUpdate() {
      const tpl = this.tplNode.data;
      api.get(tpl.id).then(res => {
        let dat = res.data;
        dat.examDesc = tpl.examDesc;
        dat.examDiagnosis = tpl.examDiagnosis;
        dat.operationSuggestion = tpl.operationSuggestion;
        return api.update(dat);
      }).then(res => {
        this.$modal.msgSuccess("更新完成。");
      });
    }
  }
};
export default model;
</script>
