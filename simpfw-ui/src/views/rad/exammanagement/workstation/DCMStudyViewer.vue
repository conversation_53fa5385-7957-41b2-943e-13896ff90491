<template>
  <div class="report-writer-wrap flex-container-column">
  
    <div v-if="!splitScreenUsed" class="report-writer-image2">
  
      <div class="exam-image-stat">
        <strong>选中影像</strong>（共有影像<span>{{null!=studyImagesSet? studyImagesSet.length:0}}</span>幅)
      </div>
  
      <div class="cornerstone-elements-wrap flex-container">
        <div class="cornerstone-elements-scroller cornerstone-elements-scroller-forward" @click="scrollImage(-1)">&lt;</div>
        <div ref="imageScrollView" class="cornerstone-elements-pane flex-item-fill" :class="{'cornerstone-elements-pane-emtpy': !studyImagesSet || studyImagesSet.length<=0}">
          <template v-if="null!=studyImagesSet && studyImagesSet.length>0" v-for="(item,idx) in studyImagesSet">
            <div v-if="validateExamImage(item)" :key="item.SOPInstanceUID" class="cornerstone-element-container" :data-index="idx">
              <template v-if="typeofVideo(item)">
                <div class="cornerstone-element cornerstone-element-video"
                 @dblclick="viewVideo($event)"
                 title="双击播放视频"><i class="el-icon-video-camera"></i></div>
              </template>
              <template v-else>
                <div class="cornerstone-element"
                 @mouseover="zoomImage($event)"
                 @mouseleave="zoomImage()" 
                 @dblclick="viewImage($event)" 
                 title="双击看大图"></div>
              </template>
              <div class="cornerstone-element-no" v-show="!!item.selectNum">{{item.selectNum}}</div>
                <div v-if="reportable && typeofJpg(item)" class="cornerstone-element-cls" @click="deleteExamImage(item)"><i class="el-icon-delete"/></div>
                <div v-if="!isExamImageDeleted(item)" class="exami-mage-checkbox">
                <!-- <el-checkbox @change="state=>selectExamImage(state, item)" v-model="item.selected" :disabled="!reportable || !typeofJpg(item)" /> -->
              </div>
            </div>
          </template>
        </div>
        <div class="cornerstone-elements-scroller cornerstone-elements-scroller-backward" @click="scrollImage(1)">&gt;</div>
      </div>
    </div>
    <!-- <ReportPreviewer ref="reportPreviewer" :report="reportForm" /> -->
    <!-- 扫码 -->
    <LinksignPopup :qr="qr" />
    <!-- 查看图像 -->
    <ImageBubble1 ref="imageBubble1" />
    <!-- 播放动态影像 -->
    <VideoView ref="videoView" />
    <!-- 选择医生 -->
    <UserPicker ref="userPicker" @pick="pickUser" />
    <!--  -->
    <el-dialog title="图像浏览" width="800px" :visible.sync="dialogVisible" :modal="false"   :append-to-body="false"  v-dialog-drag>
      <el-row >
          <el-col :span="21">
            <imageView v-if="dialogVisible" :changeUrl="imageURL.uri">
                <img :src="imageURL.uri" >
            </imageView>
          </el-col>
          <el-col :span="3" v-if="currentItemIdx!=null && !!studyImagesSet && studyImagesSet.length > 0">
            <!-- <el-button style="margin: 1%;" type="danger" v-if='auditable' @click="deleteExamImage(studyImagesSet[currentItemIdx])">删除</el-button>
            <el-button style="margin: 1%;" type="success" v-if="auditable&&!studyImagesSet[currentItemIdx].selected" @click="selectExamImage(1,studyImagesSet[currentItemIdx]);studyImagesSet[currentItemIdx].selected=!studyImagesSet[currentItemIdx].selected">选择</el-button>
            <el-button style="margin: 1%;" type="warning" v-if="auditable&&studyImagesSet[currentItemIdx].selected" @click="selectExamImage(0,studyImagesSet[currentItemIdx]);studyImagesSet[currentItemIdx].selected=!studyImagesSet[currentItemIdx].selected">取消</el-button> -->
            <el-button style="margin: 1%;" type="primary" @click="nextView(-1)">上一张</el-button>
            <el-button style="margin: 1%;" type="primary" @click="nextView(1)">下一张</el-button>
          </el-col>
      </el-row>
    </el-dialog>
  </div>
  </template>
  
  <!-- <style scoped src="@/assets/styles/uis/exammanagement/patient/Regist.css"></style> -->
  <!-- <style scoped src="@/assets/styles/uis/report/ReportWriter.css"></style> -->
  <style scoped src="@/assets/styles/pacs/cornerstone-image.css"></style>
  
  <script>
  import model from "@/assets/scripts/rad/exammanagement/workstation/DCMStudyViewer";
  export default model;
  </script>
  