<template>
<el-container ref="queueWrap" class="nested-container" style="height: 100%">
  <el-header height="auto">
    <div style="margin-bottom: 4px">
      <el-form>
        <el-select v-model="searchForm.propName" style="width: 120px"
         @change="focusPropValue">
          <el-option
            v-for="dict in searchForm.combo_props"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        <el-select v-model="searchForm.examItemCodes" style="width: 130px;" clearable multiple collapse-tags class="select-multi-sing" placeholder="检查项目"
         :disabled="this.examItemsFixed">
            <el-option
              v-for="dict in ctrlData.dict.uis_exam_item"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>     
        <el-input ref="propValue" v-model="searchForm.propValue"  @blur="checkValue" clearable style="width: 160px"
        @focus="handleFocusRegistCode" @keyup.enter.native="getList" />
        <el-button type="primary" icon="el-icon-search" @click="getList"></el-button>
      </el-form>
    </div>
  </el-header>

  <el-main class="flex-container-column">
    <div class="flex-item-fill scroll-auto">

      <el-table ref="dataGrid" v-loading="loading" :data="grid.data" row-key="id"
       stripe highlight-current-row height="100%"
       @current-change="handleCurrentChange"
       @row-dblclick="handleRowDblClick"
       @row-contextmenu="popRowContextmenu">

        <el-table-column prop="callNo" label="排队号" min-width="60" :formatter="colFmt_object" />
        <el-table-column prop="examInfo.patientInfo.name" label="姓名" min-width="80" :formatter="colFmt_object" />
        <el-table-column prop="examInfo.patientInfo.gender.dictLabel" label="性别" min-width="50" :formatter="colFmt_object" />
        <el-table-column prop="examInfo.patientInfo.age" label="年龄" min-width="50" :formatter="colFmt_age" />
        <el-table-column prop="examInfo.examParts.partsName" label="检查部位" min-width="100" :formatter="colFmt_object" show-overflow-tooltip />
        <el-table-column prop="examInfo.resultStatus.dictLabel" label="工作状态" width="100">
          <template slot-scope="scope" v-if="!!scope.row.examInfo.resultStatus">
            <i class="el-icon-error state-icon-err" v-if="2 === scope.row.examInfo.status || '10' === scope.row.examInfo.resultStatus.dictValue"></i>
            <i :class="'el-icon-success' + ' result-status-icon-' + scope.row.examInfo.resultStatus.dictValue" v-else></i>
            <span style="margin-left: 4px">{{ 2 === scope.row.examInfo.status? '已删除' : (!!scope.row.examInfo.resultStatus? scope.row.examInfo.resultStatus.dictLabel : null) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="examInfo.patientInfo.registNo" label="登记号" min-width="100" :formatter="colFmt_object" />
        <el-table-column prop="examInfo.examItem.dictLabel" label="检查项目" min-width="100" :formatter="colFmt_object" />
        <el-table-column prop="examInfo.createTime" label="登记时间" min-width="160" />
        <el-table-column label="操作" fixed="right" align="center" width="90" class-name="button-col">
          <template slot-scope="scope">
            <div v-if="!!scope.row.examInfo && !scope.row.examInfo.examAtPm">
              <el-button type="warning" icon="el-icon-video-pause"
                title="延迟检查"
                @click="handlePostpone(scope.row, 1)"
                ></el-button>

              <el-button type="primary" icon="el-icon-microphone"
                title="呼叫"
                @click="toCall(scope.row)"
              ></el-button>
            
              <!--<el-button type="danger" icon="el-icon-circle-close"
                title="迟到"
                @click="handlePast(scope.row)"
                ></el-button>-->
            </div>

            <div v-if="!!scope.row.examInfo && 1 === scope.row.examInfo.examAtPm">
              <el-button
                title="准备就绪"
                icon="el-icon-check"
                @click="handlePostpone(scope.row, null)"
                ></el-button>
            </div>
          </template>
        </el-table-column>

      </el-table>
    </div>

    <pagination small layout="total, prev, pager, next"
      :total="grid.pager.total"
      :page.sync="searchForm.pageNum"
      :limit.sync="searchForm.pageSize"
      @pagination="getList"
    />
    <!-- 叫号 -->
    <ExamCalling ref="examCalling" @refreshExamInfo="getList" />
    <!-- 右键菜单 -->
    <Contextmenu ref="tableContextmenu" :items="contextmenuItems" @select="handleContextmenu" />

  </el-main>
</el-container>

</template>

<style scoped src="@/assets/styles/pacs/exammanagement/patient/exam-info-icon.css"></style>

<script>
import model from "@/assets/scripts/gis/exammanagement/queue/comp/Queue";
export default model;
</script>
