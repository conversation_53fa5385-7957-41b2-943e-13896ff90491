package yyy.xxx.simpfw.framework.auth.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import yyy.xxx.simpfw.common.core.domain.AjaxResult;
import yyy.xxx.simpfw.common.core.domain.model.LoginUser;
import yyy.xxx.simpfw.common.utils.SecurityUtils;
import yyy.xxx.simpfw.framework.auth.service.QRAuthRelService;

/**
 * 关联常规账号登录Token和QR扫码登录的Token
 */
@RestController
@RequestMapping(value = "/qrauth/rel")
public class QRAuthRelController {

    @Autowired private QRAuthRelService service;

    @GetMapping
    public AjaxResult rel(String qrToken) {
        LoginUser luser = SecurityUtils.getLoginUser();
        service.setRelation(luser.getToken(), qrToken);

        return AjaxResult.success();
    }
}
