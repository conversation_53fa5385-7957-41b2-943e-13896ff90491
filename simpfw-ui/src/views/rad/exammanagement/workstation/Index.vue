<template>



    <el-row style="height: 100%; width: 100%">
      <el-col :span="12" style="height: 100%;">
        
        <DcmView ref="DcmView" @dispatchAction="handleAction" style="height: 50%; width: 100%"></DcmView>
        <DCMStudyViewer ref="DcmStudyView" @dispatchAction="handleAction" style="height: 50%; width: 100%"></DCMStudyViewer>

        <!-- <iframe src="http://localhost:3000/viewer?StudyInstanceUIDs=*******.4.1.25403.************.3824.20170125095722.1" style="height: 100%; width: 100%"></iframe> -->
      </el-col>

      <el-col style="height: 100%" :span="12">
   
        <div style="height: 50%; width: 100%">  
          <Patient @changeCurrentExam="changeCurrentExam" ></Patient>
        </div>          
        <div style="height: 50%; width: 100%">
          <el-tabs v-model="currentTab" class="tab-ver tab-ver-cp tab-ver-flex hei100" @tab-click="handleTabClick">
            <el-tab-pane label="所有图像" :name="tabsName.Study" style="height: 100%; width: 100%">
              <div class="tab-body-p4 hei100" style="height: 100%; width: 100%">
                <Study ref="all" @changeCurrentStudy="changeCurrentStudy" @dispatchAction="handleAction" style="height: 100%; width: 100%"></Study>
              </div>
            </el-tab-pane>   
            
            <el-tab-pane label="未匹配图像" :name="tabsName.StudyNone" style="height: 100%; width: 100%">
              <div class="tab-body-p4 hei100" style="height: 100%; width: 100%">
                <StudyNone ref="none" @changeCurrentStudy="changeCurrentStudy" @dispatchAction="handleAction" style="height: 100%; width: 100%"></StudyNone>
              </div>
            </el-tab-pane>   
            
            <el-tab-pane label="检查报告" :name="tabsName.ReportView" style="height: 100%; width: 100%">
              <div class="tab-body-p4 hei100" style="height: 100%; width: 100%">
                <ReportView ref="ReportView" @dispatchAction="handleAction" style="height: 100%; width: 100%"></ReportView>
              </div>
            </el-tab-pane>   
            
            <el-tab-pane label="电子申请单" :name="tabsName.ExamView" style="height: 100%; width: 100%">
              <div class="tab-body-p4 hei100" style="height: 100%; width: 100%">
                <ExamView ref="ExamView" @dispatchAction="handleAction" style="height: 100%; width: 100%"></ExamView>
              </div>
            </el-tab-pane>   
            
          </el-tabs>
        </div>
      </el-col>
    </el-row>





</template>


<script>

import model from "@/assets/scripts/rad/exammanagement/workstation/Index";
export default model;
</script>
