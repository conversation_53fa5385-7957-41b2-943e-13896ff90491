<template>
<div style="height: 100%">
  <el-container style="height: 100%">
    <div class="box" ref="box">
        <div class="left hei100 scroll-auto">
            <TemplateTree :wrapClass="{'aside-tree-wrap': true}" :priflag="true" ref="tplTree"   @dblclick="handleNodeClick" />
        </div>
        <div class="resize" title="收缩侧边栏">
            ⋮
        </div>
        <div class="mid hei100 ">
            <el-main>
                <el-form ref="editForm" :model="editForm" :rules="editFormRules" label-width="100px" class="tight-form"><!-- :rules="rules" -->
                  <el-row>
                    <el-col :span="18">
                      <el-row>
                        <el-col :span="24">









                          <el-form-item label="模板分类" prop="category.id">
                            <Treeselect  v-model="editForm.category.id"
                            :options="cateTreeData" :show-count="true"
                            placeholder="选择" style="max-width: 240px;font-weight: 100;" /> 

                            <!-- <el-select style="width: 140px;" v-model="cataTreeGrade.cateTreeVale_1"  ref="cTree_1" @change="companyChange(1)"  
                            clearable  filterable allow-create default-first-option placeholder="一级目录">
                                <el-option v-for="item in cataTreeGrade.cateTree_1" :key="item.data.id" :label="item.data.label" :value="item.data.label">
                                </el-option>
                            </el-select>
                            <el-select style="width: 140px;" v-model="cataTreeGrade.cateTreeVale_2" ref="cTree_2" @change="companyChange(2)"  
                            clearable  filterable allow-create default-first-option placeholder="二级目录">
                                <el-option v-for="item in cataTreeGrade.cateTree_2" :key="item.data.id" :label="item.data.label" :value="item.data.label">
                                </el-option>
                            </el-select>
                           
                            <el-select style="width: 140px;" v-model="cataTreeGrade.cateTreeVale_3"  ref="cTree_3" @change="companyChange(3)" 
                                 clearable  filterable allow-create default-first-option placeholder="三级目录">
                                <el-option v-for="item in cataTreeGrade.cateTree_3" :key="item.data.id" :label="item.data.label" :value="item.data.label">
                                </el-option>
                            </el-select>
                            
                            <el-select style="width: 140px;" v-model="cataTreeGrade.cateTreeVale_4" ref="cTree_4" @change="companyChange(4)" 
                             clearable  filterable allow-create default-first-option placeholder="四级目录">
                                <el-option v-for="item in cataTreeGrade.cateTree_4" :key="item.data.id" :label="item.data.label" :value="item.data.label">
                                </el-option>
                            </el-select> -->
                          </el-form-item>
                                       
                         
                        </el-col>
                        <!-- <el-col :span="12">
                          <el-form-item label="默认模板">
                            <el-switch v-model="editForm.preferred"
                                active-color="#018fd7"
                                :active-value="true" :inactive-value="false">
                              </el-switch>                
                          </el-form-item>
                        </el-col> -->
                      </el-row>
                      <el-row>
                        <el-col :span="24">
                          <el-form-item label="模板名称" prop="templateName">
                            <el-input v-model="editForm.templateName" :readonly="!editable" :title="!editable? '请选择模板分类或模板' : null" />
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="24">
                          <el-form-item label="检查所见">
                            <el-input type="textarea" v-model="editForm.examDesc" name="examDesc" :rows="8" :readonly="!editable" />
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="24">
                          <el-form-item label="检查诊断">
                            <el-input type="textarea" v-model="editForm.examDiagnosis" name="examDiagnosis" :rows="4" :readonly="!editable" />
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="24">
                          <el-form-item label="术后医嘱">
                            <el-input type="textarea" v-model="editForm.operationSuggestion" name="operationSuggestion" :rows="4" :readonly="!editable" />
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="24">
                          <el-form-item label="提示语">
                            <el-input v-model="editForm.prompts" />
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="12">
                          <el-form-item label="阴阳性">
                            <el-select v-model="editForm.examResultProp.dictValue" clearable>
                              <el-option
                                v-for="dict in dict.type.uis_exam_result_prop"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                              />
                            </el-select>              
                          </el-form-item>
                        </el-col>
        
                        <el-col :span="12">
                          <el-form-item label="检查项目">
                            <el-select v-model="editForm.examItem_dictValue" clearable multiple collapse-tags>
                              <el-option
                                v-for="dict in dict.type.uis_exam_item"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                              />
                            </el-select>              
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-col>
                    <el-col :span="6">
                      <div>关键短语</div>
                      <PhraseSelect :targets="['examDesc', 'examDiagnosis', 'operationSuggestion']" @change="updateFormField" />
                    </el-col>
                  </el-row>
                </el-form>
              </el-main>
              <el-footer height="unset">
                <div class="foot-tools">
                  <el-button type="primary" @click="submitEditForm" :loading="loading" :disabled="!editable" :title="!editable? '请选择模板分类或模板' : null">保 存</el-button>
                  <el-button type="danger" @click="handleDelete(editForm)" v-show="!!editForm.id">删 除</el-button>
                  <el-button @click="cancelEdit">重 置</el-button>
                </div>
              </el-footer>
        </div>
    </div>
  </el-container>
</div>
</template>

<style scoped src="@/assets/styles/pacs/dragControllerDiv.css"></style>

<script>
import model from "@/assets/scripts/pacs/tplcfg/template";
export default model;
</script>
