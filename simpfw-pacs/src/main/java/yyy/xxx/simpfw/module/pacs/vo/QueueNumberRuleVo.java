package yyy.xxx.simpfw.module.pacs.vo;

import yyy.xxx.simpfw.module.pacs.entity.QueueNumberRule;
import yyy.xxx.simpfw.module.pacs.entity.WritePhrase;
import yyy.xxx.simpfw.common.core.domain.TreeSelect;

public class QueueNumberRuleVo extends QueueNumberRule {
    public enum RuleType {
        ExamModality("exam_modality"), InpType("inp_type")
        , ExamItem("exam_item"), EquipRoom("equip_room");

        private String code;
        public String getCode() {
            return this.code;
        }
        private RuleType(String code) {
            this.code = code;
        }
    }
}
