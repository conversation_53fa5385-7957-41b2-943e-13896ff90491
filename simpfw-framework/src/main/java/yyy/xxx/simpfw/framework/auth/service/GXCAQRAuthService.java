package yyy.xxx.simpfw.framework.auth.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.NumberUtils;
import yyy.xxx.simpfw.common.annotation.DataSource;
import yyy.xxx.simpfw.common.core.domain.HttpResult;
import yyy.xxx.simpfw.common.core.domain.entity.SysUser;
import yyy.xxx.simpfw.common.core.domain.model.LoginUser;
import yyy.xxx.simpfw.common.core.redis.RedisCache;
import yyy.xxx.simpfw.common.core.service.HttpClientService;
import yyy.xxx.simpfw.common.enums.DataSourceType;
import yyy.xxx.simpfw.common.utils.SecurityUtils;
import yyy.xxx.simpfw.common.utils.StringUtils;
import yyy.xxx.simpfw.framework.auth.QRAuthConst;
import yyy.xxx.simpfw.framework.auth.bo.CertAuthCode;
import yyy.xxx.simpfw.framework.auth.bo.CertAuthResponse;
import yyy.xxx.simpfw.framework.auth.bo.gxca.*;
import yyy.xxx.simpfw.framework.auth.entity.ExamCASignEntity;
import yyy.xxx.simpfw.framework.auth.entity.ExamCASignEntityWithBLOBs;
import yyy.xxx.simpfw.framework.auth.mapper.ExamCASignEntityMapper;
import yyy.xxx.simpfw.framework.web.service.SysPermissionService;
import yyy.xxx.simpfw.framework.web.service.TokenService;
import yyy.xxx.simpfw.system.service.ISysConfigService;
import yyy.xxx.simpfw.system.service.ISysUserService;

import java.net.SocketTimeoutException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@DataSource(DataSourceType.SLAVE)
public class GXCAQRAuthService implements QRAuthConst {

    private final static String BASE_CONFIG_KEY = "uis.auth.gxca.service";
    private final static String BASE_ERROR_MSG = "易手签服务请求失败，请联系CA服务商排查原因。";
    private final static int AUTH_EXPIRE_TIME = 8; //小时

    private BaseConfig baseConfig;

    private final ISysConfigService sysConfigService;
    private final HttpClientService httpClientService;
    private final RedisCache redisCache;
    private final SysPermissionService permissionService;
    private final TokenService tokenService;
    private final ISysUserService userService;
    private final ExamCASignEntityMapper examCASignEntityMapper;

    public GXCAQRAuthService(ISysConfigService sysConfigService,
                             HttpClientService httpClientService,
                             RedisCache redisCache,
                             SysPermissionService permissionService,
                             TokenService tokenService,
                             ISysUserService userService,
                             ExamCASignEntityMapper examCASignEntityMapper) {
        this.sysConfigService = sysConfigService;
        this.httpClientService = httpClientService;
        this.redisCache = redisCache;
        this.permissionService = permissionService;
        this.tokenService = tokenService;
        this.userService = userService;
        this.examCASignEntityMapper = examCASignEntityMapper;
        String configStr = sysConfigService.selectConfigByKey(BASE_CONFIG_KEY);
        if (StringUtils.isNotBlank(configStr)) {
            baseConfig = JSON.parseObject(configStr, BaseConfig.class);
        }
    }

    public String createSign(String signContent) throws Exception {
        String loginUserToken = SecurityUtils.getLoginUser().getToken();
        GetDoctorInfoResponse doctorInfo = getDoctorInfoByCache(loginUserToken);
        if (doctorInfo == null) {
            throw new IllegalStateException("CA登录状态已过期，请重新扫码登录认证。");
        }
        GetDigestRequest digestRequest = createBuilder(GetDigestRequest.class).build();
        digestRequest.setSignContent(signContent);
        String digestData = handleResponse(handleRequest(digestRequest), String.class);

        CreateSignRequest createSignRequest = createBuilder(CreateSignRequest.class).build();
        createSignRequest.setDoctorNum(doctorInfo.getDoctorNum());
        createSignRequest.setSignTitle("审核签名");
        createSignRequest.setSignDesc("审核签名");
        createSignRequest.setSignContent(digestData);
        //createSignRequest.setResultType("1"); //传值 1 (立即返回需提前开启自动签名)

        return handleResponse(handleRequest(createSignRequest), String.class);
    }

    public GetSignInfoResponse getSignInfo(String signNum) throws Exception {
        GetSignInfoRequest getSignInfoRequest = createBuilder(GetSignInfoRequest.class).build();
        getSignInfoRequest.setSignNum(signNum);
        return handleResponse(handleRequest(getSignInfoRequest), GetSignInfoResponse.class);
    }

    public void dataVerify(String signContent,
                           String signResult,
                           String cert) throws Exception {
        DataVerifyRequest dataVerifyRequest = createBuilder(DataVerifyRequest.class).build();
        dataVerifyRequest.setPlain(signContent);
        dataVerifyRequest.setSignValue(signResult);
        dataVerifyRequest.setCert(cert);
        handleResponse(handleRequest(dataVerifyRequest), null);
    }

    public String tssVerify(String signContent,
                            String tssBase64) throws Exception {
        TssVerifyRequest tssVerifyRequest = createBuilder(TssVerifyRequest.class).build();
        tssVerifyRequest.setPlainData(signContent);
        tssVerifyRequest.setTsBase64(tssBase64);
        return handleResponse(handleRequest(tssVerifyRequest), String.class);
    }

    // 包装builder方法
    private <T extends BaseRequest> BaseRequest.Builder<T> createBuilder(Class<T> requestClass) {
        return BaseRequest.builder(requestClass)
                .baseUrl(baseConfig.getBaseUrl())
                .appId(baseConfig.getAppId());
    }

    public boolean checkConfig() {
        return baseConfig != null
                && StringUtils.isNotBlank(baseConfig.getBaseUrl())
                && StringUtils.isNotBlank(baseConfig.getAppId());
    }

    private HttpResult handleRequest(BaseRequest baseRequest) throws Exception {
        log.info("发送CA请求：{}", baseRequest);
        Map<String, Object> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        try {
            return this.httpClientService.doPost(baseRequest.getRequestUrl(),
                    JSON.toJSONString(baseRequest),
                    headers,
                    null);
        } catch (SocketTimeoutException e) {
            throw new RuntimeException("CA服务请求超时，请联系CA厂商（易手签）解决");
        }

    }

    private <T> T handleResponse(HttpResult httpResult, Class<T> dataClass) throws Exception {
        return handleResponse(httpResult, dataClass, true);
    }

    private <T> T handleResponse(HttpResult httpResult, Class<T> dataClass, boolean throwsException) throws Exception {
        HttpStatus httpStatus = HttpStatus.valueOf(httpResult.getCode());
        if (!httpStatus.is2xxSuccessful()) {
            throw new HttpServerException(BASE_ERROR_MSG + "httpStatus=" + httpStatus);
        }

        BaseResponse<T> response;
        if (dataClass != null) {
            response = JSON.parseObject(httpResult.getBody(), new TypeReference<BaseResponse<T>>(dataClass) {
            });
        } else {
            response = JSON.parseObject(httpResult.getBody(), BaseResponse.class);
        }

        log.info("CA响应数据：{}", response);
        if (response.getCode() != 0 && throwsException) {
            throw new HttpServerException(BASE_ERROR_MSG + "失败原因：" + response.getMsg());
        }
        return response.getData();
    }

    public GetQRCodeResponse getQRCode() throws Exception {
        GetQRCodeRequest request = createBuilder(GetQRCodeRequest.class).build();
        return handleResponse(handleRequest(request), GetQRCodeResponse.class);
    }

    public int saveCASign(ExamCASignEntityWithBLOBs entity) {
        return examCASignEntityMapper.insertSelective(entity);
    }

    /**
     * `给定sessionId授权状态
     *
     * @param sessionId 用于获取用户信息的CA session token
     * @param forRel    用于常规账号+扫码登录
     * @return
     */
    public LoginUser authcheck(String sessionId, Boolean forRel) throws Exception {

        if (log.isDebugEnabled()) {
            log.debug("sessionId={}", sessionId);
        }
        if (StringUtils.isBlank(sessionId)) {
            //logger.warn("医信签授权检查登录错误: 无法获取sessionId");
            throw new RuntimeException("sessionId为空，请重新扫码登录。");
        }

        LoginUser luser = null;
        //是否已登录
        if (!forRel) {
            try {
                luser = SecurityUtils.getLoginUser();
            } catch (Exception err) {
                log.error(err.getMessage(), err);
            }
        }
        //redis是否有签名信息
        if (null != luser) {
            if (luser.getExpireTime() < System.currentTimeMillis()) {
                if (log.isDebugEnabled()) {
                    log.debug("易手签授权过期 {}, {}, {}"
                            , luser.getUserId(), luser.getToken(), luser.getExpireTime());
                }
                redisCache.deleteObject(String.format(cacheKeyPattern, sessionId));
                throw new RuntimeException(ERRM_sessionExpired);
            }
        } else {
            luser = getOauthStatus(sessionId, forRel);
            if (null == luser) {
                if (null == redisCache.getCacheObject(String.format(cacheKeyPattern, sessionId))) {
                    throw new RuntimeException(ERRM_sessionInvalid);
                }
                return null;
            }
            return luser;
        }

        return luser;
    }


    /**
     * `查询扫码登录结果
     *
     * @param number 二维码唯一标识
     */
    public GetQRCodeLoginStatusResponse getQRCodeLoginStatus(String number) throws Exception {
        GetQRCodeLoginStatusRequest request = createBuilder(GetQRCodeLoginStatusRequest.class)
                .with(req -> req.setNumber(number))
                .build();
        return handleResponse(handleRequest(request), GetQRCodeLoginStatusResponse.class, false);
    }

    /**
     * `获取授权状态
     *
     * @param sessionId
     * @return
     */
    private LoginUser getOauthStatus(String sessionId, Boolean forRel) throws Exception {
        if (!checkConfig()) {
            log.error("请配置接口信息: wztsignBase.");
            return null;
        }
        //取得待认证参数：二维码 qrCodeIdentity
        CertAuthCode authCode = redisCache.getCacheObject(String.format(cacheKeyPattern, sessionId));
        ;
        if (null == authCode) {
            //throw new RuntimeException(errorInvalidSession);
            return null;
        }
        //CA接口
        GetQRCodeLoginStatusResponse loginStatus = getQRCodeLoginStatus(authCode.getId());
        switch (loginStatus.getAuthStatus()) {
            case REFUSE:
                throw new RuntimeException("CA服务拒绝授权，请联系CA厂商解决。");
            case TOSIGN:
                log.warn("CA服务等待授权，请稍后重试或联系CA厂商解决");
                return null;
            case EXPIRE:
                throw new RuntimeException(ERRM_sessionExpired);
        }


        //缓存扫描token
        LoginUser loginUser = SecurityUtils.getLoginUser();
        GetDoctorInfoResponse doctorInfo = getDoctorInfo(loginStatus.getDoctorJobNo(),
                loginStatus.getDoctorNum());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long expireTime = sdf.parse(loginStatus.getExpireTime()).getTime();

        //用登录token关联认证信息
        authCode.setExpired(expireTime); //更新成登录的过期时间
        LoginUser signUser = makeSession(doctorInfo, forRel);
        authCode.setUserUid(signUser.getUserId().toString());
        final String origCacheKey = String.format(cacheKeyPattern, sessionId);
        redisCache.deleteObject(origCacheKey);
        //long cacheExpireTime = expireTime - System.currentTimeMillis();
        this.renewAuth(authCode, doctorInfo);
        return signUser;
    }

    public void renewAuth(CertAuthCode authCode,
                          GetDoctorInfoResponse doctorInfo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        redisCache.setCacheObject(String.format(cacheKeyPattern, loginUser.getToken()), authCode, AUTH_EXPIRE_TIME, TimeUnit.HOURS);
        redisCache.setCacheObject(String.format(cacheTokenPattern, loginUser.getToken()), doctorInfo, AUTH_EXPIRE_TIME, TimeUnit.HOURS);
    }

    private GetDoctorInfoResponse getDoctorInfo(String doctorJobNo,
                                                String doctorNum) throws Exception {
        GetDoctorInfoRequest request = createBuilder(GetDoctorInfoRequest.class)
                .with(req -> {
                    req.setDoctorJobNo(doctorJobNo);
                    req.setDoctorNum(doctorNum);
                })
                .build();
        return handleResponse(handleRequest(request), GetDoctorInfoResponse.class);
    }

    public GetDoctorInfoResponse getDoctorInfoByCache(String loginUserToken) {
        return redisCache.getCacheObject(String.format(cacheTokenPattern, loginUserToken));
    }

    /**
     * `生成会话
     *
     * @return SSO用户
     */
    private LoginUser makeSession(GetDoctorInfoResponse response,
                                  Boolean forRel) {
        //缓存到Redis
        SysUser user = new SysUser();
        user.setUserName(response.getDoctorJobNo());
        user.setNickName(response.getDoctorName());

        SysUser user0 = userService.selectUserByUserName(user.getUserName());
        if (null == user0) {
            throw new IllegalArgumentException(String.format("当前系统不存在用户名为：%s 的用户信息", user.getUserName()));
        } else {
            user.setUserId(user0.getUserId());
            if (null != user0.getDept()) {
                user.setDept(user0.getDept());
                user.setDeptId(user0.getDeptId());
            }
            if (null != user0.getPosts()) {
                user.setPostIds(user0.getPostIds());
                user.setPosts(user0.getPosts());
            }
            if (null != user0.getRoles()) {
                user.setRoleId(user0.getRoleId());
                user.setRoleIds(user0.getRoleIds());
                user.setRoles(user0.getRoles());
            }
        }

        LoginUser luser = new LoginUser();
        //luser.getDeptId()
        luser.setLoginTime(System.currentTimeMillis());
        //授权有效时长，秒
        int expireTime = expiredTime;
        luser.setExpireTime(System.currentTimeMillis() + (1000L * expireTime));
        //
        luser.setUserId(user.getUserId());
        luser.setUser(user);
        luser.setPermissions(permissionService.getMenuPermission(user));
        String tokenU = tokenService.createToken(luser);//jwt
        //更换cache sessionId
        final String origToken = luser.getToken();
        //
        if (!forRel) {
            //临时修改，前端使用jwt
            luser.setToken(tokenU);//sessionId
        }

        return luser;
    }

}
