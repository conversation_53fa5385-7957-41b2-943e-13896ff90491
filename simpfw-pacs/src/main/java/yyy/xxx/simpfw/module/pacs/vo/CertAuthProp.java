package yyy.xxx.simpfw.module.pacs.vo;

import yyy.xxx.simpfw.common.utils.StringUtils;

public class CertAuthProp {
    private String base, projectUid, fmtProjectUid, applicationId, fmtApplicationId;
    /**
     * 3-App，4-微信小程序
     */
    private Integer[] qrTypes = new Integer[] {3, 4};

    private CertAuthServer[] servers;

    public String getBase() {
        return base;
    }

    public String getProjectUid() {
        return projectUid;
    }
    public String getFmtProjectUid() {
        return fmtProjectUid;
    }

    public String getApplicationId() {
        return applicationId;
    }
    public String getFmtApplicationId() {
        return fmtApplicationId;
    }

    public Integer[] getQrTypes() {
        return qrTypes;
    }

    public CertAuthServer[] getServers() {
        return servers;
    }
    public void setServers(CertAuthServer[] servers) {
        this.servers = servers;
    }

    public CertAuthProp(String base, String projectUid, String applicationId, Integer[] qrTypes, CertAuthServer[] servers) {
        this.base = base;
        this.projectUid = projectUid;
        this.applicationId = applicationId;
        this.qrTypes = qrTypes;
        this.servers = servers;
        //
        if(null != projectUid) {
            fmtProjectUid = "\"" + projectUid + "\"";
        }
        //
        if(null != applicationId) {
            fmtApplicationId = "\"" + applicationId + "\"";
        }
    }

    public boolean validate() {
        return StringUtils.isNotBlank(base);
    }
}
