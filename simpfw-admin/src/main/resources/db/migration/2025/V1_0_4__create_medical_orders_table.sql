USE pacs_mt;
CREATE TABLE IF NOT EXISTS `d_medical_orders`
(
    `ID`                      bigint    NOT NULL AUTO_INCREMENT,
    `HospitalCode`            varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '院区代码',
    `PATPatientID`            varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者主索引',
    `PATDocumentNo`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '病案号',
    `PATHealthCardID`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '健康卡号',
    `PATCardType`             varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '卡类型',
    `PATName`                 varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者姓名',
    `PATDob`                  date                                                          DEFAULT NULL COMMENT '患者出生日期',
    `PATSexCode`              varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者性别代码',
    `PATSexDesc`              varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者性别描述',
    `PATMaritalStatusCode`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者婚姻状况代码',
    `PATMaritalStatusDesc`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者婚姻状况描述',
    `PATNationCode`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者民族代码',
    `PATNationDesc`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者民族描述',
    `PATCountryCode`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者国籍代码',
    `PATCountryDesc`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者国籍描述',
    `PATMotherID`             varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者母亲ID',
    `PATDeceasedDate`         date                                                          DEFAULT NULL COMMENT '死亡日期',
    `PATDeceasedTime`         time                                                          DEFAULT NULL COMMENT '死亡时间',
    `PATTelephone`            varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者联系电话',
    `PATOccupationCode`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者职业代码',
    `PATOccupationDesc`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者职业描述',
    `PATWorkPlaceTelNum`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者工作单位电话号码',
    `PATIdentityNum`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者证件号码',
    `PATIdTypeCode`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者证件类别代码',
    `PATIdTypeDesc`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者证件类别描述',
    `PATRelationName`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者联系人姓名',
    `PATRelationPhone`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者联系人电话',
    `PATWorkPlaceName`        text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '患者工作地点',
    `PATRemarks`              text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
    `PAADMVisitNumber`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '就诊号码',
    `PAADMVisitTimes`         int                                                           DEFAULT NULL COMMENT '住院次数',
    `PAADMDeptCode`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '就诊科室代码',
    `PAADMDeptDesc`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '就诊科室名称',
    `PAADMAdmWardCode`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '就诊病区代码',
    `PAADMAdmWardDesc`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '就诊病区描述',
    `PAADMCurBedNo`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '病床号',
    `PAADMHosCode`            varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '就诊医院代码',
    `PAADMDocCode`            varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '就诊医生代码',
    `PAADMDocDesc`            varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '就诊医生名称',
    `PAADMStartDate`          date                                                          DEFAULT NULL COMMENT '就诊日期',
    `PAADMStartTime`          time                                                          DEFAULT NULL COMMENT '就诊时间',
    `PAADMCurDeptCode`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '当前科室代码',
    `PAADMCurDeptDesc`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '当前科室描述',
    `PAAdmStatusCode`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '就诊状态代码',
    `PAAdmStatusDesc`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '就诊状态描述',
    `PAADMTypeCode`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '就诊类型代码',
    `PAADMTypeDesc`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '就诊类型描述',
    `PAADMFeeTypeCode`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者费用类型代码',
    `PAADMFeeTypeDesc`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者费用类型描述',
    `Charger`                 varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收费员',
    `ChargeDate`              datetime                                                      DEFAULT NULL COMMENT '收费时间',
    `RISRExamID`              varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '检查号',
    `RISRArExaReqSym`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '患者主诉',
    `RISRArPurpose`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '检查目的？？',
    `RISRSystemType`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '检查系统类型',
    `RISRAppNum`              varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '检查申请单号',
    `RISRMattersAttention`    text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '检查注意事项',
    `RISRSpecalMedicalRecord` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '检查特殊病史编码（高血压/心脏病）',
    `RISRPatDiag`             text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '检查诊断',
    `AppDeptCode`             varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '检查申请科室代码',
    `AppDeptDesc`             varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '检查申请科室',
    `RISRSubmitDocCode`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '检查申请医生代码',
    `RISRSubmitDocDesc`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '检查申请医生描述',
    `RISRSubmitTime`          datetime                                                      DEFAULT NULL COMMENT '检查申请时间',
    `RISRAcceptDeptCode`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '接收科室代码',
    `RISRAcceptDeptDesc`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '接收科室',
    `RISRDeptLocation`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '检查科室位置',
    `RISRISEmergency`         varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '检查是否加急',
    `RISRClinicalSymptoms`    text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '检查临床所见',
    `OEORIOrderItemID`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '医嘱明细ID',
    `RISRPositionCode`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '医嘱部位代码',
    `RISRPostureCode`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '医嘱体位代码',
    `RISRCode`                varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '医嘱项目代码',
    `RISRDesc`                varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '医嘱项目名称',
    `RISRPrice`               varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '医嘱价格',
    `OrdSubCatCode`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '医嘱子类代码',
    `OrdSubCatDesc`           text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '医嘱子类描述',
    `OrdCatCode`              varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '医嘱大类代码',
    `OrdCatDesc`              text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '医嘱大类描述',
    `OEORIStatusCode`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '医嘱状态代码',
    `UpdateUserCode`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '最后更新人代码',
    `UpdateUserDesc`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '最后更新人',
    `UpdateDate`              date                                                          DEFAULT NULL COMMENT '更新日期',
    `UpdateTime`              time                                                          DEFAULT NULL COMMENT '更新时间',
    `OrdBillStatus`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收费状态',
    `OrdBillNum`              varchar(100)                                                  DEFAULT NULL COMMENT '医嘱数量',
    `CreatedOn`               timestamp NULL                                                DEFAULT CURRENT_TIMESTAMP,
    `UpdatedOn`               timestamp NULL                                                DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `CreatedBy`               varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL,
    `UpdatedBy`               varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL,
    `status`                  int                                                           DEFAULT NULL,
    PRIMARY KEY (`ID`) USING BTREE,
    UNIQUE KEY `udx_OEORIOrderItemID` (`OEORIOrderItemID`) USING BTREE,
    KEY `idx_PATPatientID` (`PATPatientID`) USING BTREE,
    KEY `idx_PATDocumentNo` (`PATDocumentNo`) USING BTREE,
    KEY `idx_PATHealthCardID` (`PATHealthCardID`) USING BTREE,
    KEY `idx_PATIdentityNum` (`PATIdentityNum`) USING BTREE,
    KEY `idx_RISRExamID` (`RISRExamID`) USING BTREE,
    KEY `idx_RISRAppNum` (`RISRAppNum`) USING BTREE,
    KEY `idx_PAADMVisitNumber` (`PAADMVisitNumber`) USING BTREE,
    KEY `idx_RISRCode` (`RISRCode`) USING BTREE,
    KEY `idx_RISRPositionCode` (`RISRPositionCode`) USING BTREE,
    KEY `idx_RISRPostureCode` (`RISRPostureCode`) USING BTREE,
    KEY `idx_PAADMStartDate` (`PAADMStartDate`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 8818204
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci
  ROW_FORMAT = COMPACT COMMENT ='医嘱表';


CALL mt_db_manager.add_column_if_not_exists('pacs_mt', 'd_medical_orders', 'PATHealthCardID_reverse',
                                            'VARCHAR(100) GENERATED ALWAYS AS (REVERSE(PATHealthCardID)) VIRTUAL');

CALL mt_db_manager.create_index_if_not_exists('pacs_mt', 'd_medical_orders', 'idx_health_reverse',
                                              'PATHealthCardID_reverse');