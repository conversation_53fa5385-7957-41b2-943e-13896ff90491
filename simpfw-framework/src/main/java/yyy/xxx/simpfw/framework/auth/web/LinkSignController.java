package yyy.xxx.simpfw.framework.auth.web;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import yyy.xxx.simpfw.common.core.domain.AjaxResult;
import yyy.xxx.simpfw.common.core.domain.model.LoginUser;
import yyy.xxx.simpfw.framework.auth.QRAuthConst;
import yyy.xxx.simpfw.framework.auth.bo.LinkSign;
import yyy.xxx.simpfw.framework.auth.bo.LinkSignResult;
import yyy.xxx.simpfw.framework.auth.bo.QRAuthResult;
import yyy.xxx.simpfw.framework.auth.service.LinkSignService;
import yyy.xxx.simpfw.framework.auth.service.QRAuthService;

import javax.servlet.http.HttpServletRequest;

/**
 * `医信签动态口令，短信验证
 * @version 
 */
@RestController
@RequestMapping(value = "/linksign")
public class LinkSignController implements QRAuthConst {
    
    private static Logger logger = LoggerFactory.getLogger(LinkSignController.class);

    @Autowired private LinkSignService service;

    private static final String paramSessionId = "sessionId";

    /**
     * `动态口令
     * @param linkSign
     */
    @RequestMapping(value = "/cfmotp")
    public AjaxResult cfmotp(@RequestBody LinkSign linkSign) {

        try {
            LinkSignResult res = service.confirmOtp(linkSign);
            if(res.isSuccess()) {
                return AjaxResult.success().put("token", linkSign.getUuid());
            }
            return AjaxResult.error(res.getMessage());
        } catch (Exception err) {
            logger.error(err.getMessage(), err);
            return AjaxResult.error(err.getMessage());
        }
    }

    /**
     * `给定sessionId授权状态
     * @return
     */
    @RequestMapping(value = "/aqrsms")
    public AjaxResult sms(LinkSign linkSign) {
        try {
            LinkSignResult res = service.aqrsms(linkSign);

            return res.isSuccess()? AjaxResult.success() : AjaxResult.error(res.getMessage());
        } catch (Exception err) {
            logger.error(err.getMessage(), err);
            return AjaxResult.error(err.getMessage());
        }
    }

    @RequestMapping(value = "/cfmsms")
    public AjaxResult cfmsms(@RequestBody LinkSign linkSign) {
        try {
            LinkSignResult res = service.confirmSms(linkSign);

            if(res.isSuccess()) {
                return AjaxResult.success().put("token", linkSign.getUuid());
            }
            return AjaxResult.error(res.getMessage());
        } catch (Exception err) {
            logger.error(err.getMessage(), err);
            return AjaxResult.error(err.getMessage());
        }
    }    
}