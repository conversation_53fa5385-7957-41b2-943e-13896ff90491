USE pacs_mt;
CREATE TABLE d_exam_info_medical_orders_ref
(
    exam_info_id      bigint                             not null comment '检查流水表主键ID',
    medical_orders_id bigint                             not null comment '医嘱表主键ID',
    create_time       datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    constraint d_exam_info_medical_orders_ref_pk
        unique (exam_info_id, medical_orders_id)
)
    comment '检查流水医嘱关系表';


-- 导入历史数据
-- 创建临时表存储拆分后的数据
CREATE TEMPORARY TABLE temp_ord_items (
                                          exam_info_id BIGINT,
                                          ord_item VARCHAR(128)
);

-- 使用存储过程拆分数据并插入临时表
INSERT INTO temp_ord_items (exam_info_id, ord_item)
SELECT
    id,
    SUBSTRING_INDEX(SUBSTRING_INDEX(ord_id, '@', numbers.n), '@', -1) AS ord_item
FROM
    d_exam_info
        CROSS JOIN
    (
        SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL
        SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL
        SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10
    ) AS numbers
WHERE
    ord_id IS NOT NULL
  AND ord_id != ''
  AND LENGTH(ord_id) - LENGTH(REPLACE(ord_id, '@', '')) >= numbers.n - 1;

-- 创建索引提高查询速度
ALTER TABLE temp_ord_items ADD INDEX idx_ord_item (ord_item);

-- 使用临时表进行关联查询
INSERT INTO d_exam_info_medical_orders_ref (exam_info_id, medical_orders_id)
SELECT
    t.exam_info_id,
    mo.id AS medical_orders_id
FROM
    temp_ord_items t
        JOIN
    d_medical_orders mo ON mo.OEORIOrderItemID = t.ord_item;

-- 删除临时表
DROP TEMPORARY TABLE IF EXISTS temp_ord_items;