<template>
  <el-dialog
    title="选择"
    :visible="opened"
    width="1500px"
    custom-class="popupdialog"
    append-to-body
    @close="close"
    height="80%"
  >
  <div class="">
    <el-table
        :data="listData"
        
        class="tableArea"
        style="width: 100%">
        
        <el-table-column
          prop="groupName"
          label="分组名称"
          />
          <el-table-column v-for="(tableColumn) in tableColumns" :key="tableColumn.label" :prop="tableColumn.prop"  :label="tableColumn.label"/>
      
      </el-table>
  </div>
</el-dialog>
</template>
    
<style scoped src="@/assets/styles/pacs/exammanagement/patient/Index.css"></style>
<style scoped src="@/assets/styles/pacs/exammanagement/patient/exam-info-icon.css"></style>
<style>

 
 .el-table th>.cell {
     white-space: pre-wrap;
 }

 .el-table .cell {
  white-space: pre-wrap;   /*这是重点。文本换行*/
}
</style>
<script>
import model from "@/assets/scripts/rad/report/schedule/Test";
export default model;
</script>
    