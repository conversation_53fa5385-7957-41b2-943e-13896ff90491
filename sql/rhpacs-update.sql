-- d_exam_info
ALTER TABLE `d_exam_info`
	-- 20230412 EFY-008-9
	ADD COLUMN `operation_grade_code` VARCHAR(16) NULL DEFAULT NULL COMMENT '手术级别d_operation_grade' AFTER `operation_suggestion`,
	ADD COLUMN `anes_doctor_code` VARCHAR(16) NULL DEFAULT NULL COMMENT '麻醉医师代码' AFTER `oper_nurse_name`,
	ADD COLUMN `anes_doctor_name` VARCHAR(128) NULL DEFAULT NULL COMMENT '麻醉医师名字' AFTER `anes_doctor_code`,
	-- EFY-008-15
	ADD COLUMN pathology_diagnosis TEXT NULL DEFAULT NULL COMMENT '病理诊断' AFTER status_of_send_report,
	ADD COLUMN pathology_treatment TEXT NULL DEFAULT NULL COMMENT '病理处理' AFTER pathology_diagnosis,
	ADD COLUMN anes_way_code varchar(16) NULL DEFAULT NULL COMMENT '麻醉方式代码' AFTER pathology_treatment,
	ADD COLUMN mirror_measure_code varchar(16) NULL DEFAULT NULL COMMENT '进镜措施代码' AFTER anes_way_code,
	ADD COLUMN breather_way_code varchar(16) NULL DEFAULT NULL COMMENT '通气方式代码' AFTER mirror_measure_code,
	ADD COLUMN retain_specimen_flag varchar(16) NULL DEFAULT NULL COMMENT '是否留取标本，0为否，1为是' AFTER breather_way_code,
	ADD COLUMN operation_complication_flag varchar(16) NULL DEFAULT NULL COMMENT '是否是操作并发症，0为否，1为是' AFTER retain_specimen_flag,
	ADD COLUMN operation_complications_code varchar(128) NULL DEFAULT NULL COMMENT '操作并发症代码' AFTER operation_complication_flag,
	ADD COLUMN oc_treatment_measure TEXT NULL DEFAULT NULL COMMENT '处理措施' AFTER operation_complications_code,
	-- 20230505 EFY-008-9
	ADD COLUMN `appoint_exam_date` DATE NULL DEFAULT NULL COMMENT '预约检查日期' AFTER `appoint_time` ,
	ADD COLUMN `appoint_exam_time` VARCHAR(128) NULL DEFAULT NULL COMMENT '预约检查时间' AFTER `appoint_exam_date`,
	-- 20240103
	ADD COLUMN `unexecute_ord_id` VARCHAR(128) NULL DEFAULT NULL COMMENT '未执行医嘱' AFTER `ord_id`,
	-- 20240116
	ADD COLUMN `sign_info_uuid` VARCHAR(32) NULL DEFAULT NULL COMMENT '签名表uuid' AFTER `unexecute_ord_id`,
	-- 20240228
	ADD COLUMN `exam_batch_no` VARCHAR(128) NOT NULL COMMENT '检查批次号' AFTER `exam_no`,
	ADD COLUMN `exam_serial_no` VARCHAR(128) NOT NULL COMMENT '检查流水号' AFTER `exam_batch_no`,
	ADD COLUMN `accession_number` VARCHAR(128) NOT NULL COMMENT 'accession_number' AFTER `exam_serial_no`,
	-- 20241209
	ADD COLUMN `exam_age` VARCHAR(50) NULL DEFAULT NULL COMMENT '检查年龄' AFTER `weight`;
	
-- d_dicom_image
ALTER TABLE `d_dicom_image`
	-- 20240313
	ADD COLUMN `statusOfSendImage` tinyint(0) NULL DEFAULT NULL COMMENT '图像发送状态:0未发送，1首次发送返回，2发送/重发失败，3重发成功' AFTER `dataSource`,
	
	
-- d_template 
ALTER TABLE `d_template`
	-- EFY-008-18
    ADD COLUMN `template_type` tinyint NULL COMMENT '模板类型(pacs_template_type)' AFTER `template_name`,
	-- 20231226
	ADD COLUMN exam_parts_id varchar(500) NULL DEFAULT NULL COMMENT '检查部位' AFTER update_time;
	
-- d_template 
ALTER TABLE `d_template_category`
	-- EFY-008-18
    ADD COLUMN `personal_flag` VARCHAR(128) NULL DEFAULT NULL COMMENT '是否是个人模板，0为否，1为是',
	ADD COLUMN `create_by` VARCHAR(128) NULL DEFAULT NULL COMMENT '模板创建用户',
	-- 20231226
	ADD COLUMN exam_modality_code varchar(16) NULL DEFAULT NULL COMMENT '检查类型' AFTER personal_flag,
	ADD COLUMN exam_item_code varchar(255) NULL DEFAULT NULL COMMENT '检查项目' AFTER exam_modality_code;



-- d_booked_info
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for d_booked_info
-- ----------------------------
-- DROP TABLE IF EXISTS `d_booked_info`;
CREATE TABLE `d_booked_info`  (
  `id` bigint(0) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主健	',
  `exam_uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'uid',
  `exam_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '检查号',
  `booked_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '预约ID',
  `mpi` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '患者主索引',
  `patient_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '患者ID，关联检查信息',
  `regist_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '暂时不用',
  `medical_record_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '病历号',
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '姓名	',
  `reg_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '登记号	',
  `insurance_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '医保号	',
  `phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话	',
  `exam_item_code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '检查项目代码',
  `exam_cost` decimal(12, 4) NULL DEFAULT NULL COMMENT '检查费用	',
  `inp_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '住院号	',
  `inp_times` tinyint(0) UNSIGNED NULL DEFAULT NULL COMMENT '住院次数',
  `inp_ward_code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '病区',
  `inp_room_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `bed_no` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '床号	',
  `outp_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '门诊号	',
  `adm_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '就诊号	',
  `adm_series_num` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '就诊流水号',
  `ord_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '医嘱号	',
  `ord_priority_Code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '医嘱类型代码	',
  `ord_priority` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '医嘱类型',
  `ord_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '医嘱名称	',
  `ord_bill_status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '医嘱状态	',
  `ord_status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '医嘱状态',
  `charge_status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收费状态	',
  `adm_serial_num` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '就诊流水号	',
  `diagnosis_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '就诊类型	有就诊字典表',
  `appoint_time` DATETIME NULL DEFAULT NULL COMMENT '预约时间',
  `appoint_exam_date` date NULL DEFAULT NULL COMMENT '预约检查日期',
  `appoint_exam_time` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '预约检查时间',
  `status` tinyint(0) NULL DEFAULT 0,
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uidx_exam_uid`(`exam_uid`) USING BTREE,
  INDEX `idx_exam_no`(`exam_no`) USING BTREE,
  INDEX `idx_exam_item_code`(`exam_item_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;

	
-- d_booked_info 20230517 
/*
ALTER  TABLE d_booked_info 
	CHANGE appoint_date appoint_exam_date DATE NULL DEFAULT NULL COMMENT '预约检查日期';
ALTER  TABLE d_booked_info 
	CHANGE appoint_time appoint_exam_time VARCHAR(128) NULL DEFAULT NULL COMMENT '预约检查时间';
ALTER TABLE `d_booked_info`
	ADD COLUMN `appoint_time` DATETIME NULL DEFAULT NULL COMMENT '预约时间' AFTER `diagnosis_type`;
*/
	
