package yyy.xxx.simpfw.module.uis.web;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import yyy.xxx.simpfw.common.core.controller.BaseController;
import yyy.xxx.simpfw.common.core.domain.AjaxResult;
import yyy.xxx.simpfw.common.core.page.TableDataInfo;
import yyy.xxx.simpfw.module.uis.entity.TraceCase;
import yyy.xxx.simpfw.module.uis.service.TraceCaseService;

@RestController
@RequestMapping("/exammanagement/traceCase")
public class TraceCaseController extends BaseController {

	private static Logger logger = LoggerFactory.getLogger(TraceCaseController.class);
	
    @Autowired
    private TraceCaseService traceCaseService;
    
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody Map<String, Object> parameters) {
    	logger.debug("查询参数： {}", parameters);
        startPage();
        List<TraceCase> list = traceCaseService.getList(parameters);
        logger.debug("返回结果： {}", list);
        return getDataTable(list);
    }
    
    /**
     * 根据追踪状态获取对应的数量
     * @return
     */
    @GetMapping("/getCounts")
    public Map<String, Object> groupCountsByTraceStatus() {
    	Map<String, Object> countsMap = traceCaseService.groupCountsByTraceStatus();
    	return AjaxResult.success(countsMap);
    }
    
    /**
     * 根据检查号查找
     * @param examNo
     * @return
     */
    @GetMapping("/getByExamNo/{examNo}")
    public AjaxResult getById(@PathVariable String examNo) {
    	TraceCase traceCase = traceCaseService.findByExamNo(examNo);
    	return AjaxResult.success(traceCase);
    }
    
    
    /**
     * 更新
     * @param traceCase
     * @return
     */
    @PostMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(@RequestBody TraceCase traceCase) {
    	logger.debug("查询参数： {}", traceCase);
        traceCaseService.saveOrUpdate(traceCase);
        return AjaxResult.success();
    }
    
}

