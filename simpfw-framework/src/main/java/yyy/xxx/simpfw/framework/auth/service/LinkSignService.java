package yyy.xxx.simpfw.framework.auth.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import yyy.xxx.simpfw.common.constant.Constants;
import yyy.xxx.simpfw.common.core.domain.model.LoginUser;
import yyy.xxx.simpfw.framework.auth.QRAuthConst;
import yyy.xxx.simpfw.framework.auth.bo.LinkSign;
import yyy.xxx.simpfw.framework.auth.bo.LinkSignResult;
import yyy.xxx.simpfw.framework.auth.bo.QRAuthResult;

import java.util.HashMap;
import java.util.Map;

@Service
public class LinkSignService extends LinkSignServiceComm {

    private static final String linksignURL_sms = "%s/doctor/app/h5/auth/sms?transactionId=%s&phone=%s"
            , linksignURL_confirmPhone = "%s/doctor/app/h5/auth/confirm/phone"
            , linksignParam_confirmPhone = "{\"transactionId\":\"%s\",\"authTime\":\"%s\",\"phone\":\"%s\",\"smsCode\":\"%s\",\"destUserNum\":\"\"}"

            , linksignURL_info = "%s/doctor/app/h5/auth/info?transactionId=%s"

            , linksignUrl_confirmOtp = "%s/doctor/app/h5/auth/confirm/otp"
            , linksignParam_confirmOtp = "{\"transactionId\":\"%s\",\"authTime\":\"%s\",\"userNumber\":\"%s\",\"otpCode\":\"%s\",\"destUserNum\":\"\"}";

    private static final TypeReference<LinkSignResult> typeRef_result
            = new TypeReference<LinkSignResult>() {};

    /**
     * 验证动态口令
     * @param linkSign
     */
    public LinkSignResult confirmOtp(LinkSign linkSign) {
        if(!this.checkConfig()) {
            throw new RuntimeException(ERRM_UNAVAIL_SERVICE);
        }
        //
        String url, postData;
        //
        url = String.format(linksignUrl_confirmOtp, authApi.getServer());
        postData = String.format(linksignParam_confirmOtp, getTransactionId(linkSign), linkSign.getAuthTime(), linkSign.getUsername(), linkSign.getPassword());
        if(logger.isDebugEnabled()) { logger.debug("医信签动态口令验证参数={}", postData); }
        String resText = httpClient.ajaxPost(url, postData, Constants.UTF8, getRequestHeaders());
        //{"code":20000,"message":"操作成功","data":{"status":"1","userNumber":"18100000000","systemName":"PACS系统"}} {"code":20003,"message":"缺少必要参数，请重试"}
        //resText = "{\"code\":20000,\"message\":\"操作成功\",\"data\":{\"status\":\"1\",\"userNumber\":\"18100000000\",\"systemName\":\"PACS系统\"}}";
        if(logger.isDebugEnabled()) { logger.debug("医信签动态口令验证结果={}", resText); }
        LinkSignResult res = JSON.parseObject(resText, typeRef_result);
        //
        if(res.isSuccess()) {
            LoginUser luser = authcheck(linkSign.getUuid(), false);
            linkSign.setUuid(luser.getToken());
        }

        return res;
    }

    /**
     * 发送短信
     */
    public LinkSignResult aqrsms(LinkSign linkSign) {
        //
        if(!this.checkConfig()) {
            throw new RuntimeException(ERRM_UNAVAIL_SERVICE);
        }
        //
        String url;
        //
        url = String.format(linksignURL_sms, authApi.getServer(), getTransactionId(linkSign), linkSign.getUsername());
        if(logger.isDebugEnabled()) { logger.debug("医信签获取短信地址={}", url); }
        String resText = httpClient.ajaxGet(url, Constants.UTF8, getRequestHeaders());
        //{"code":20000,"message":"操作成功","data":null}
        //resText = "{\"code\":20000,\"message\":\"操作成功\",\"data\":null}";
        if(logger.isDebugEnabled()) { logger.debug("医信签获取短信结果={}", resText); }
        LinkSignResult res = JSON.parseObject(resText, typeRef_result);

        return res;
    }

    /**
     * 验证短信
     * @param linkSign
     */
    public LinkSignResult confirmSms(LinkSign linkSign) {
        //
        if(!this.checkConfig()) {
            throw new RuntimeException(ERRM_UNAVAIL_SERVICE);
        }
        //
        String url, postData;
        //
        url = String.format(linksignURL_confirmPhone, authApi.getServer());
        //
        postData = String.format(linksignParam_confirmPhone, getTransactionId(linkSign), linkSign.getAuthTime(), linkSign.getUsername(), linkSign.getPassword());
        if(logger.isDebugEnabled()) { logger.debug("医信签短信验证参数={}", postData); }
        String resText = httpClient.ajaxPost(url, postData, Constants.UTF8, getRequestHeaders());
        //{"code":20000,"message":"" "data":("status":"1","userNumber":"18100000000" "systemName":"PACS系统"}}
        //resText = "{\"code\":20000,\"message\":\"\" \"data\":(\"status\":\"1\",\"userNumber\":\"18100000000\" \"systemName\":\"PACS系统\"}}";
        if(logger.isDebugEnabled()) { logger.debug("医信签短信验证结果={}", resText); }
        LinkSignResult res = JSON.parseObject(resText, typeRef_result);
        //
        if(res.isSuccess()) {
            LoginUser luser = authcheck(linkSign.getUuid(), false);
            linkSign.setUuid(luser.getToken());
        }

        return res;
    }

    /**
     * 参考医信签封装界面必要参数
     * @return
     */
    private Map<String, Object> getRequestHeaders() {
        //
        String nonce = randomNumbers(16);
        long timestamp = System.currentTimeMillis();
        String signature = DigestUtils.md5DigestAsHex((timestamp + nonce + timestamp).getBytes());
        //
        Map<String, Object> headers = new HashMap<>(3);
        headers.put("X-Link-Open-Nonce", nonce);
        headers.put("X-Link-Open-Timestamp", timestamp);
        headers.put("X-Link-Open-Signature", signature);
        return headers;
    }

    /**
     * 参考医信签封装界面指定长度随机字符
     * @param len
     * @return
     */
    private String randomNumbers(int len) {
        String chars = "0123456789abcdefg";
        int maxPos = chars.length();
        StringBuilder str = new StringBuilder(len);
        for (int i = 0; i < len; i++) {
            str.append(chars.charAt((int)Math.floor(Math.random() * maxPos)));
        }
        return str.toString();
    }
    /**
     * 获取 transactionId
     * @return
     */
    private String getTransactionId(LinkSign linkSign) {
        String sid = linkSign.getUuid();
        QRAuthResult authResultData = (QRAuthResult)redisCache.getCacheObject(String.format(cacheKeyPattern, sid));
        if(null == authResultData) {
            if(logger.isDebugEnabled()) { logger.debug("重新获取医信签凭证。"); }
            if(null == (authResultData = fetchTokenAndCode(sid))) {
                throw new IllegalStateException(QRAuthConst.ERRM_NOAUTH);
            }
        }

        return authResultData.getTransactionId();
    }
}
