package yyy.xxx.simpfw.framework.auth.entity;

import java.util.Date;

public class ExamCASignEntity {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column d_exam_ca_sign.id
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column d_exam_ca_sign.examUid
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    private String examuid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column d_exam_ca_sign.login_user_id
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    private Long loginUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column d_exam_ca_sign.sign_user_id
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    private Long signUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column d_exam_ca_sign.ca_provider
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    private Integer caProvider;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column d_exam_ca_sign.create_time
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    private Date createTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column d_exam_ca_sign.id
     *
     * @return the value of d_exam_ca_sign.id
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column d_exam_ca_sign.id
     *
     * @param id the value for d_exam_ca_sign.id
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column d_exam_ca_sign.examUid
     *
     * @return the value of d_exam_ca_sign.examUid
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    public String getExamuid() {
        return examuid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column d_exam_ca_sign.examUid
     *
     * @param examuid the value for d_exam_ca_sign.examUid
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    public void setExamuid(String examuid) {
        this.examuid = examuid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column d_exam_ca_sign.login_user_id
     *
     * @return the value of d_exam_ca_sign.login_user_id
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    public Long getLoginUserId() {
        return loginUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column d_exam_ca_sign.login_user_id
     *
     * @param loginUserId the value for d_exam_ca_sign.login_user_id
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    public void setLoginUserId(Long loginUserId) {
        this.loginUserId = loginUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column d_exam_ca_sign.sign_user_id
     *
     * @return the value of d_exam_ca_sign.sign_user_id
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    public Long getSignUserId() {
        return signUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column d_exam_ca_sign.sign_user_id
     *
     * @param signUserId the value for d_exam_ca_sign.sign_user_id
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    public void setSignUserId(Long signUserId) {
        this.signUserId = signUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column d_exam_ca_sign.ca_provider
     *
     * @return the value of d_exam_ca_sign.ca_provider
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    public Integer getCaProvider() {
        return caProvider;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column d_exam_ca_sign.ca_provider
     *
     * @param caProvider the value for d_exam_ca_sign.ca_provider
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    public void setCaProvider(Integer caProvider) {
        this.caProvider = caProvider;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column d_exam_ca_sign.create_time
     *
     * @return the value of d_exam_ca_sign.create_time
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column d_exam_ca_sign.create_time
     *
     * @param createTime the value for d_exam_ca_sign.create_time
     *
     * @mbg.generated Thu Jan 02 00:00:24 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}