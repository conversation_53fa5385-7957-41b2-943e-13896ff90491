<template>
<div class="app-container">
  <el-tabs class="tabs-container-1" tab-position="left" @tab-click="handleClick">
    <el-tab-pane v-for="tab in tabs" :key="tab.name" :label="tab.label" :id="tab.name + '_pane'">
      <el-tabs class="tabs-container-2 flex-container-column" @tab-click="handleClick">
        <el-tab-pane
         v-for="ei in dict.type.uis_exam_item" 
         :key="ei.value" 
         :label="ei.label" 
         :id="tab.name + '_' + ei.value + '_queue'">
          <Queue :ref="tab.name + '_' + ei.value + '_queue'"
           :examItemsFixed="true"
           :filter="{examInfo: {examAtPm: ('nor' === tab.name? 0 : ('postpone' === tab.name? 1 : null))}, examItemCodes: [ei.value], resultStatusCodes: ['0']}" />
        </el-tab-pane>
      </el-tabs>
    </el-tab-pane>
  </el-tabs>
</div>
</template>

<style>
  .tabs-container-1, .tabs-container-2{
    height: 100%;
  }
  .tabs-container-1>div.el-tabs__header{
    background-color: #F4F9FC;
  }
  .tabs-container-1>div.el-tabs__header>.el-tabs__nav-wrap{
    margin-top: 32px;
  }
  .tabs-container-1>.el-tabs__content, .tabs-container-1 .el-tab-pane{
    height: 100%;
  }

  .tabs-container-2>.el-tabs__content{
    padding-top: 8px;
    flex-grow: 1;
  }
</style>

<script>
import Queue from "@/views/gis/exammanagement/queue/comp/Queue";

const model = {
  name: "QueueFrame",

  components: { Queue },

  dicts: ["uis_exam_item"],

  data() {
    return {
      tabs: [
        {name: "nor", label: "等待检查"},
        {name: "postpone", label: "延迟检查"},
        {name: "all", label: "全部检查"}
      ],
      tabState: {}
    }
  },

  methods: {
    handleClick(vm) {
      //console.log(arguments);
      const tabId = vm.$attrs["id"], segm = tabId.split("_");
      let tabState = this.tabState, ref, paneId, queueId;
      paneId = segm[0];
      if(segm.length == 3) {
        queueId = segm[1];
      }
      //点第一层
      if(!queueId) {
        //是否有切换第二层
        queueId = tabState[paneId];
        if(!queueId) {
          tabState[paneId] = queueId = this.dict.type.uis_exam_item[0].value;
        }
      } else {
        if(!(paneId in tabState)) {
          tabState[paneId] = {};
        }
        tabState[paneId] = queueId;
      }
      //console.log(paneId, queueId)
      this.$nextTick(() => {
        this.$refs[paneId + '_' + queueId + '_queue'][0].getList();
      });
    }
  }
};
export default model;
</script>
