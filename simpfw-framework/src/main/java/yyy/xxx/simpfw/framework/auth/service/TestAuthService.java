package yyy.xxx.simpfw.framework.auth.service;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import yyy.xxx.simpfw.common.annotation.DataSource;
import yyy.xxx.simpfw.common.core.domain.model.LoginUser;
import yyy.xxx.simpfw.common.core.redis.RedisCache;
import yyy.xxx.simpfw.common.enums.DataSourceType;
import yyy.xxx.simpfw.common.utils.SecurityUtils;
import yyy.xxx.simpfw.common.utils.StringUtils;
import yyy.xxx.simpfw.framework.auth.QRAuthConst;
import yyy.xxx.simpfw.framework.auth.bo.CertAuthCode;
import yyy.xxx.simpfw.framework.auth.bo.QRAuthStatus;
import yyy.xxx.simpfw.framework.auth.bo.gxca.GetDoctorInfoResponse;
import yyy.xxx.simpfw.framework.auth.bo.gxca.GetSignInfoRequest;
import yyy.xxx.simpfw.framework.auth.bo.gxca.GetSignInfoResponse;
import yyy.xxx.simpfw.framework.auth.bo.test.BaseConfig;
import yyy.xxx.simpfw.framework.auth.entity.ExamCASignEntityWithBLOBs;
import yyy.xxx.simpfw.framework.auth.mapper.ExamCASignEntityMapper;
import yyy.xxx.simpfw.system.service.ISysConfigService;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@DataSource(DataSourceType.SLAVE)
public class TestAuthService implements QRAuthConst {

    private final static String BASE_CONFIG_KEY = "uis.auth.testca.service";
    private final static int AUTH_EXPIRE_TIME = 8; //小时
    private static final String codeId = "testCAID";

    private BaseConfig baseConfig;

    private final ExamCASignEntityMapper examCASignEntityMapper;

    public TestAuthService(ISysConfigService sysConfigService,
                           ExamCASignEntityMapper examCASignEntityMapper) {
        this.examCASignEntityMapper = examCASignEntityMapper;
        String configStr = sysConfigService.selectConfigByKey(BASE_CONFIG_KEY);
        if (StringUtils.isNotBlank(configStr)) {
            baseConfig = JSON.parseObject(configStr, BaseConfig.class);
        }
    }

    public boolean checkConfig() {
        return baseConfig != null
                && baseConfig.isEnable();
    }

    public int saveCASign(ExamCASignEntityWithBLOBs entity) {
        return examCASignEntityMapper.insertSelective(entity);
    }

    public CertAuthCode getAuthCode() {
        LoginUser loginUser = SecurityUtils.getLoginUser();

        long expireTime = System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(expiredTime);
        CertAuthCode authCode = new CertAuthCode(codeId, null, expireTime);
        authCode.setUserUid(loginUser.getUserId().toString());
        return authCode;
    }

    public QRAuthStatus getQRAuthStatus() {
        GetDoctorInfoResponse doctorInfoResponse = getDoctorInfo();
        QRAuthStatus qrAuthStatus = new QRAuthStatus();
        qrAuthStatus.setSignatureImg(doctorInfoResponse.getSignImg());
        qrAuthStatus.setUserId(doctorInfoResponse.getDoctorJobNo());
        qrAuthStatus.setUserName(doctorInfoResponse.getDoctorName());
        return qrAuthStatus;
    }

    public GetDoctorInfoResponse getDoctorInfo() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        GetDoctorInfoResponse doctorInfoResponse = new GetDoctorInfoResponse();
        doctorInfoResponse.setDoctorJobNo(loginUser.getUsername());
        doctorInfoResponse.setDoctorName(loginUser.getUser().getNickName());
        try {
            int width = 150;
            int height = 50;
            // 创建图片
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = image.createGraphics();

            // 设置白色背景
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, width, height);

            // 设置字体和颜色
            g2d.setColor(Color.BLACK);
            InputStream is = getClass().getResourceAsStream("/fonts/dingliesongtypeface20241217-2.ttf");
            g2d.setFont(Font.createFont(Font.TRUETYPE_FONT, is).deriveFont(Font.PLAIN, 24f));

            // 计算文字位置使其居中
            FontMetrics fm = g2d.getFontMetrics();
            int textWidth = fm.stringWidth(loginUser.getUser().getNickName());
            int x = (width - textWidth) / 2;
            int y = ((height - fm.getHeight()) / 2) + fm.getAscent();

            // 绘制文字
            g2d.drawString(loginUser.getUser().getNickName(), x, y);
            g2d.dispose();

            // 转换为base64
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "png", baos);
            String signImg = Base64.getEncoder().encodeToString(baos.toByteArray());
            doctorInfoResponse.setSignImg(signImg);
        } catch (Exception e) {
            log.error("生成签名图片失败", e);
            throw new RuntimeException("生成签名图片失败", e);
        }
        //doctorInfoResponse.setSignImg("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");
        return doctorInfoResponse;
    }

    public GetSignInfoResponse getSignInfo(String signNum) {
        GetSignInfoResponse response = new GetSignInfoResponse();
        response.setDoctorNum("HD202412170000000000000000");
        response.setDoctorName(SecurityUtils.getLoginUser().getUser().getNickName());
        response.setSignNum(signNum);
        response.setSignStatus(1);
        response.setSignTitle("审核签名");
        response.setSignDesc("审核签名");
        response.setSignContent("test-signContent");
        response.setTssData("test-tssData");
        response.setSignResult("test-signResult");
        response.setSignImg("test-signImg");
        response.setCert("test-cert");
        return response;
    }
}
