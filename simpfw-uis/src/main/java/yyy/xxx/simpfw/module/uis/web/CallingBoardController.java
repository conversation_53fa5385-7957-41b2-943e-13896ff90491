package yyy.xxx.simpfw.module.uis.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import yyy.xxx.simpfw.common.core.controller.BaseController;
import yyy.xxx.simpfw.common.core.domain.AjaxResult;
import yyy.xxx.simpfw.common.core.redis.RedisCache;
import yyy.xxx.simpfw.module.pacs.entity.EquipRoom;
import yyy.xxx.simpfw.module.pacs.service.CallInfoService;
import yyy.xxx.simpfw.module.pacs.service.CallIngBoardService;
import yyy.xxx.simpfw.module.pacs.service.QueueInterService;
import yyy.xxx.simpfw.module.pacs.utils.DesensUtil;
import yyy.xxx.simpfw.module.pacs.vo.CallInfoVo;
import yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo;

import java.util.*;

@RestController
@RequestMapping("/exammanagement/callingBoard")
public class CallingBoardController extends BaseController {

    @Autowired private CallIngBoardService service;

    @Autowired private RedisCache redisCache;

    @Autowired private QueueInterService queueInterService;

    @RequestMapping("info")
    public AjaxResult info() {
        try {
            AjaxResult r = AjaxResult.success();
            r.put("service", queueInterService.getInterInfo());
            r.put("timestamp", System.currentTimeMillis());
            return r;
        } catch (Exception err) {
            return AjaxResult.error(err.getMessage());
        }
    }

    @RequestMapping("calling")
    public AjaxResult calling() {
        //当天所有排队：等待中+呼叫中+过号
        List<CallInfoVo> items = service.selectList()
                , calling = new ArrayList<>(), waiting = new ArrayList<>(), past = new ArrayList<>();
        //已有呼叫的机房
        //List<String> callingEquipRoomCode = new ArrayList<>();
        for(CallInfoVo item : items) {
        	//
        	DesensUtil.maskName(item.getExamInfo().getPatientInfo());
            //已呼叫：首次呼叫未结束，首次复呼未结束，再次复呼未结束
            if(null != item.getFirstCallTime() && null == item.getFirstEndCallTime()
             || null != item.getLastCallTime() && null == item.getLastEndCallTime()
             || null != item.getLastCallTime() && null != item.getLastEndCallTime() && item.getLastCallTime().after(item.getLastEndCallTime())) {
                EquipRoom equipRoom = item.getCallRoom();
                equipRoom = null != equipRoom? equipRoom : item.getExamInfo().getEquipRoom();
                //呼叫中：机房最近的呼叫
                /*String equipRoomCode;
                if(null != equipRoom && !callingEquipRoomCode.contains(equipRoomCode = equipRoom.getRoomCode())) {
                    calling.add(item);
                    //
                    callingEquipRoomCode.add(equipRoomCode);
                } else {
                    past.add(item);
                }*/
                //过号
                if(CallInfoVo.isPast(item)) {
                    past.add(item);
                } else {
                    calling.add(item);
                }
                //
                continue;
            }
            //等待中：首次呼叫为空，复呼为空
            if(null == item.getFirstCallTime() && null == item.getLastCallTime()) {
                waiting.add(item);
            }
        }
        //呼叫进行中
        Set<ExamInfoVo> callingItems = redisCache.getCacheSet(CallInfoService.callingExamCacheKey);
        for(ExamInfoVo exam : callingItems) {
        	DesensUtil.maskName(exam.getPatientInfo());
        }

        return AjaxResult.success(items)
                .put("calling", calling)
                .put("waiting", waiting)
                .put("past", past)
                .put("underway", callingItems);
    }
}
