<template>
  <div class="app-container">
    <!-- 修改用户组别 -->
    <el-dialog 
    :title="editUserGroup.title"
      :visible.sync="editUserGroup.isVisible"
      width="60%"
      center>
      <el-form ref="queryForm" size="small" :inline="true" label-width="88px">
        <el-row>
          <el-col :span="8">
            <el-form-item v-if="this.editUserGroup.isAdd" label=" ">
              <el-select
                v-model="editUserGroup.selectUser"
                placeholder="请选择医生"
                
              >
                <el-option
                  v-for="item in editUserGroup.userOptions"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="医生姓名：" prop="title" v-if="this.editUserGroup.isAdd==false">
                <el-input 
                  :disabled = true
                  v-model="editUserGroup.tableSelectUser.userName"
                />
              </el-form-item>

          </el-col>
          <el-col :span="8">
            <el-form-item label=" ">
              <el-select
                v-model="editUserGroup.selectGroup"
                placeholder="请选择分组"
              >
                <el-option
                  v-for="item in editUserGroup.groupOptions"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-button @click="editUserGroup.isVisible = false"
          >取 消</el-button
        >
        <el-button type="primary" @click="userGroupConfirm"
          >确 定</el-button
        >
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>

  <el-container style="height: 100%">
  <el-main style="height: 90%">
  <div style="height: 100%">
    <div style="height: 100%; width: 100%">
      <el-row style="height: 100%">
        <el-col style="height: 100%; width: 20%">
        <el-form>
          <el-form-item label="医生：" prop="title">
            <el-select
              v-model="editAllotedWork.user.selectUser"
              placeholder="请选择医生"
              @change="userSelectChanged"
            >
              <el-option
                v-for="item in editAllotedWork.user.userOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="">
            <el-button type="primary" icon="el-icon-search" @click="search('simp')">查询</el-button>
            <el-radio v-model="searchReportType" label="1">写报告</el-radio>
            <el-radio v-model="searchReportType" label="2">审核报告</el-radio>
            
          </el-form-item>
          <el-form-item label="">
            <el-button type="primary" icon="el-icon-search" @click="autoAllocate">自动分配</el-button>
            <el-checkbox v-model="checked1">报告</el-checkbox>
            <el-checkbox v-model="checked2">审核</el-checkbox>
          </el-form-item>
          <el-form-item label="检查进度">
            <el-select v-model="searchFormSimp.resultStatusValues" placeholder="检查进度"
             multiple collapse-tags class="select-multi-sing">
              <el-option
                v-for="dict in dict.type.uis_exam_result_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
              </el-select>
          </el-form-item>
        </el-form>
        </el-col>
        <el-col style="height: 100%; width: 80%">
          <el-table v-loading="loading" :data="grid.data" row-key="id" height="100%"
          stripe highlight-current-row
          >

            <el-table-column prop="id" label="检查流水" width="80" />
            <el-table-column prop="patientInfo.name" label="患者姓名" width="100" :formatter="colFmt_object" />
            <el-table-column prop="examNo" label="检查号" width="120" />
            <el-table-column prop="examItem.dictLabel" label="检查项目" width="100" />
            <el-table-column prop="reportDoctorCodeAllocate" label="报告分配医生" width="100" />
            <el-table-column prop="auditDoctorCodeAllocate" label="审核分配医生" width="100" />
            <el-table-column prop="examParts.partsName" label="检查部位" width="180" :formatter="colFmt_object" show-overflow-tooltip  /> 
            <el-table-column prop="examDiagnosis" label="检查结论" width="180" :formatter="colFmt_object" show-overflow-tooltip  /> 
            
            <el-table-column prop="examCost" label="费用" width="100" />
            <el-table-column prop="callInfo.callNo" label="排队号" width="70" :formatter="colFmt_object" />
            <el-table-column prop="patientInfo.gender.dictLabel" label="性别" width="50" :formatter="colFmt_dictData" />
            <el-table-column prop="patientInfo.age" label="年龄" width="60" :formatter="colFmt_age" />
            <el-table-column prop="equipRoom.roomName" label="检查房间" width="100" :formatter="colFmt_equipRoom" />
            <el-table-column prop="patientInfo.registNo" label="登记号" min-width="140"
            :formatter="colFmt_object" />
            <el-table-column prop="inpNo" label="住院号" width="180" />
            <el-table-column prop="reqDept.deptName" label="申请科室" min-width="140" 
            :formatter="colFmt_object"
            show-overflow-tooltip />
            <el-table-column prop="bedNo" label="床号" width="80" />
            <el-table-column prop="resultStatus.dictLabel" label="工作状态" width="100">
              <template slot-scope="scope" v-if="!!scope.row.resultStatus">
                <i class="el-icon-error state-icon-err" v-if="2 === scope.row.status || '10' === scope.row.resultStatus.dictValue"></i>
                <i :class="'el-icon-success' + ' result-status-icon-' + scope.row.resultStatus.dictValue" v-else></i>
                <span style="margin-left: 4px">{{ 2 === scope.row.status? '已删除' : (!!scope.row.resultStatus? scope.row.resultStatus.dictLabel : null) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="reqDoctor.nickName" label="申请医生" min-width="100" :formatter="colFmt_object" />
            <el-table-column prop="examDoctorsName" label="检查医生" min-width="100" />
            <el-table-column prop="reportDoctor.nickName" label="报告医生" width="100" :formatter="colFmt_object"  /> 
            <el-table-column prop="auditDoctor.nickName" label="审核医生" width="100" :formatter="colFmt_object"  /> 
            <el-table-column prop="consultantsName" label="会诊医生" min-width="100" />
            <el-table-column prop="recordersName" label="记录人员" min-width="100" />
            <el-table-column prop="creator.nickName" label="登记人员" min-width="120" />
            <el-table-column prop="createTime" label="登记时间" min-width="160" />
            <el-table-column prop="examTime" label="检查时间" min-width="160" />
            <el-table-column prop="signTime" label="审核时间" min-width="160" />
            <el-table-column prop="reportTime" label="报告时间" min-width="160" />
            <el-table-column prop="examModality.dictLabel" label="检查类型" width="100" :formatter="colFmt_dictData" />

            <!-- <el-table-column label="操作" fixed="right" align="center" width="120" class-name="button-col">
              <template slot-scope="scope">
                <el-button title="查看"
                  icon="el-icon-tickets"
                  @click="handleDetail(scope.row)"
                ></el-button>
                <el-button title="编辑"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                ></el-button>
              <el-button v-if="2 === scope.row.status"
                title="撤销删除"
                icon="el-icon-refresh-left"
                @click="callUndoDelete(scope.row)"
                v-hasPermi="['exam-info:delete']"
                ></el-button>
              <el-button v-else
                title="删除"
                icon="el-icon-delete"
                @click="verifyForDelete(scope.row)"
                v-hasPermi="['exam-info:delete']"
                ></el-button>
              </template>
            </el-table-column> -->
          </el-table>
        </el-col>
      </el-row>
      
    </div>
    <pagination
          v-show="grid.total>0"
          :total="grid.total"
          :page.sync="searchForm.pageNum"
          :limit.sync="searchForm.pageSize"
          :pageSizes="[10, 17, 20, 30, 50]"
          @pagination="getList"
        />
  </div>
  </el-main>

  <el-footer style="height: 10%">
                <div class="foot-tools" style="float: right">
                
      <el-button type="primary" @click="addUserGroup" >新增</el-button>
                </div>
              </el-footer>

  </el-container>
    

        
    
    <el-dialog
      :title="groupGrid.editGroupDialog.title"
      :visible.sync="groupGrid.editGroupDialog.isVisible"
      width="60%"
      center
    >
      <span slot="footer" class="dialog-footer">
        <el-form ref="queryForm" size="small" :inline="true" label-width="88px">
          <el-row>
            <el-col :span="8">
              <el-form-item label="分组代码：" prop="title">
                <el-input
                  :disabled="!groupGrid.editGroupDialog.isAdd"
                  v-model="groupGrid.editGroupDialog.groupCode"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="分组名称：" prop="title">
                <el-input v-model="groupGrid.editGroupDialog.groupName" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-button @click="groupGrid.editGroupDialog.isVisible = false"
          >取 消</el-button
        >
        <el-button type="primary" @click="editGroupDialogConfirm"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
    
<style scoped src="@/assets/styles/pacs/exammanagement/patient/Index.css"></style>
<style scoped src="@/assets/styles/pacs/exammanagement/patient/exam-info-icon.css"></style>
<script>
import model from "@/assets/scripts/rad/report/schedule/ScheduleGroupManagement";
export default model;
</script>
    