<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="yyy.xxx.simpfw.module.uis.mapper.CallIngBoardMapper">

    <resultMap id="listResultMap" type="yyy.xxx.simpfw.module.pacs.vo.CallInfoVo"
               extends="yyy.xxx.simpfw.module.uis.mapper.CallInfoMapper.listResultMap" autoMapping="true">
        <association property="examInfo" javaType="yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo"
                     columnPrefix="ei__" resultMap="yyy.xxx.simpfw.module.pacs.mapper.ExamInfoMapper.resultMapOutline" />
    </resultMap>

    <!-- 所有排队信息，等待中+呼叫中+过号 -->
    <select id="selectList" resultMap="listResultMap">
        select r.id,r.create_time createTime,r.call_no callNo,r.first_call_time firstCallTime,r.last_call_time lastCallTime,r.status
        <!-- 检查、患者信息 -->
        ,ei.id ei__id,ei.patient_id EI__patient_id,p.name ei__p__name,ei.exam_item_code EI__ei__dictValue,ei.exam_uid ei__examUid
        <!-- 呼叫房间 -->
        ,r.call_room_code rm__dictValue,rm.room_name rm__roomName
        <!-- 申请科室 -->
        ,dp.dept_id ei__rd__deptId,dp.dept_code ei__rd__deptCode,dp.dept_name ei__rd__deptName

        from `d_call_info` `r`
        join d_exam_info ei on ei.id=r.exam_info_id
        join d_patient p on p.patient_id=ei.patient_id

        left join d_equip_room rm on rm.room_code=r.call_room_code

        left join v_sys_dept dp on dp.dept_code=ei.req_dept_code
        where
        <!-- 未延迟检查 -->
         (ei.exam_at_pm is null or ei.exam_at_pm=0)
        <!-- 工作状态为已登记和已检查 -->
         AND (ei.result_status_code is null or ei.result_status_code='0' or ei.result_status_code='1')
        <!-- 排队时间当天+前一天最后5小时 -->
        and (
        r.create_time&gt;=DATE_ADD(current_date(), interval -5 HOUR) and ei.appoint_time is null
        or ei.appoint_time&gt;=DATE_ADD(current_date(), interval -5 HOUR) and ei.appoint_time&lt;DATE_ADD(current_date(), interval 1 DAY)
        )

        order by case when r.last_call_time is not null then r.last_call_time else r.first_call_time end desc
    </select>

</mapper>