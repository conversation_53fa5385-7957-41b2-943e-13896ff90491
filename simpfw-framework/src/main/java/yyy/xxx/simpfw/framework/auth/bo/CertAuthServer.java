package yyy.xxx.simpfw.framework.auth.bo;

public class CertAuthServer {
    private String stategy, host;

    public String getStategy() {
        return stategy;
    }

    public void setStategy(String stategy) {
        this.stategy = stategy;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public CertAuthServer() { }

    public CertAuthServer(String serv) {
        if(null != serv) {
            String[] parts = serv.split("::");
            host = parts[parts.length - 1];
            if(parts.length == 2) {
                stategy = parts[0];
            }
        }
    }
}
