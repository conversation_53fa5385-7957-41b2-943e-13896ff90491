package yyy.xxx.simpfw.module.pacs.bo;

import yyy.xxx.simpfw.common.utils.SecurityUtils;
import yyy.xxx.simpfw.common.utils.StringUtils;

/**
 * 排队叫号接口
 */
public class CallingServiceProp {
    //版本信息
    public static class Ver {
        private String rest, ws;

        public String getRest() {
            return rest;
        }
        public void setRest(String rest) {
            this.rest = rest;
        }

        public String getWs() {
            return ws;
        }
        public void setWs(String ws) {
            this.ws = ws;
        }
    }
    //ip:端口, 用户名, 密码
    private String server, username, password;

    private Ver ver;

    private Boolean active;

    public String getServer() {
        return server;
    }
    public void setServer(String server) {
        this.server = server;
    }

    public String getUsername() {
        return username;
    }
    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }
    public void setPassword(String password) {
        this.password = password;
    }

    public Ver getVer() {
        return ver;
    }
    public void setVer(Ver ver) {
        this.ver = ver;
    }

    public Boolean getActive() {
        return active;
    }
    public void setActive(Boolean active) {
        this.active = active;
    }

    private String auth;
    public String getAuth() {
        if(null == auth && StringUtils.isNotBlank(username) && StringUtils.isNotBlank(password)) {
            auth = String.format("%s:%s", username, password);
            auth = SecurityUtils.encryptPassword(auth);
        }
        return auth;
    }
}
