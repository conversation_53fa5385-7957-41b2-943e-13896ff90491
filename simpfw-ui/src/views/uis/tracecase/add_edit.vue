
<template>

  <div style="height: 100%; margin-left: 30px; overflow-y: auto; overflow-x: hidden;">
    <el-form ref="saveTraceCaseForm" :model="traceCaseObj" label-width="100px">

    <el-row :gutter="80">
     <el-col :span="3">
        <el-form-item label="患者姓名：">
          <div style="width: 100px;"><span>{{traceCaseObj.patientInfo.name}}</span></div>
        </el-form-item>
      </el-col>

      <el-col :span="3">
        <el-form-item label="性别：">
          <div style="width: 100px;"><span>{{traceCaseObj.patientInfo.genderCode | textGender}}</span></div>
        </el-form-item>
      </el-col>

      <el-col :span="3">
        <el-form-item label="年龄：">
           <div style="width: 100px;"><span>{{traceCaseObj.patientInfo.birthday | calculateAge}}</span></div>
        </el-form-item>
       </el-col>

      <el-col :span="5">
        <el-form-item label="住院号：">
           <div style="width: 150px;"><span>{{traceCaseObj.examInfo.inpNo||""}}</span></div>
        </el-form-item>
      </el-col>

      <el-col :span="10">
        <el-form-item label="检查号：">
           <div style="width: 100px;"><span>{{traceCaseObj.examInfo.examNo||""}}</span></div>
           <!-- <input type="hidden" name="traceCaseObj.examInfoId" v-model="traceCaseObj.examInfo.examNo" />-->
        </el-form-item>
      </el-col>
    </el-row>

   <el-row :gutter="80" style="margin-top: -500px;">
      <el-col :span="9">
        <el-form-item label="联系方式：">
           <div style="width: 300px;"><span>{{traceCaseObj.patientInfo.address||""}}</span></div>
        </el-form-item>
      </el-col>

      <el-col :span="5">
        <el-form-item label="检查医师：">
           <div style="width: 300px;"><span>{{traceCaseObj.examInfo.reqDoctorName||""}}</span></div>
        </el-form-item>
      </el-col>

      <el-col :span="4">
        <el-form-item label="检查日期：">
           <div style="width: 300px;"><span>{{traceCaseObj.examInfo.examTime||""}}</span></div>
        </el-form-item>
      </el-col>
    </el-row>


    <el-row :gutter="50" style="float: left;">
      <el-form-item label="检查结论">
        <el-input type="textarea" v-model="traceCaseObj.examConclusion" rows="3" style="width: 450px;"></el-input>
      </el-form-item>

      <el-form-item label="辅助检查">
        <el-input type="textarea" v-model="traceCaseObj.auxiliaryExam" rows="3" style="width: 450px;"></el-input>
      </el-form-item>

      <el-form-item label="手术结论">
        <el-input type="textarea" v-model="traceCaseObj.surgeryConclusion" rows="3" style="width: 450px;"></el-input>
      </el-form-item>

      <el-form-item label="病理结果">
        <el-input type="textarea" v-model="traceCaseObj.pathologyResult" rows="3" style="width: 450px;"></el-input>
      </el-form-item>

      <el-form-item label="追踪结论">
        <el-input type="textarea" v-model="traceCaseObj.traceConclusion" rows="3" style="width: 450px;"></el-input>
      </el-form-item>
    </el-row>


      <el-row :gutter="80" style="float: left; margin-left: 150px;">
          <el-form-item label="追踪状态" style="border: 1px solid gray; padding: 5px;">
            <el-radio-group v-model="traceCaseObj.traceStatus" size="mini">
              <div><el-radio label="0">不追踪</el-radio></div>
              <div style="margin-top: 10px"><el-radio :label="1">计划追踪</el-radio></div>
              <div style="margin-top: 10px"><el-radio :label="2">正在追踪</el-radio></div>
              <div style="margin-top: 10px"><el-radio :label="4">追踪完成</el-radio></div>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="符合情况" style="border: 1px solid gray; padding: 5px;">
            <el-radio-group v-model="traceCaseObj.confirmStatus">
              <div style="margin-top: 10px"><el-radio :label="2">不符合</el-radio></div>
              <div style="margin-top: 10px"><el-radio :label="1">基本符合</el-radio></div>
              <div style="margin-top: 10px"><el-radio :label="0">符合</el-radio></div>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="计划追踪日期">
              <el-date-picker type="date" placeholder="选择日期" v-model="traceCaseObj.planTraceDate" style="width: 150px;"></el-date-picker>
          </el-form-item>

          <el-form-item label="追踪日期" prop="traceDate"  :rules="[{required: true, message: '请选择追踪日期'}]" >
              <el-date-picker type="date" placeholder="选择日期" v-model="traceCaseObj.traceDate" style="width: 150px;"></el-date-picker>
          </el-form-item>


          <el-form-item label="追踪医生" prop="traceDoctorName"  
                :rules="[
                    { required: true, message: '请选择追踪医生'}]"  >
            <input type="hidden" name="traceDoctorCode" v-model="traceCaseObj.traceDoctorCode" />
            <el-input v-model.trim="traceCaseObj.traceDoctorName" placeholder="请选择医生" clearable class="el-input-group-thin" @clear="clearTraceDocter" readonly @focus="toPickUser('reqDoctor', [{postCode:'YS'}])" style="width: 150px;">
                <el-button el-button slot="append" size="mini" icon="el-icon-user-solid" 
                 title="选择医师"
                 @click="toPickUser('reqDoctor',  [{postCode:'YS'}])"></el-button>
            </el-input>

          </el-form-item>
     
      </el-row>

      <el-row style="clear: both;">
         <el-form-item>
          <el-button type="primary" @click="saveOrUpdate">保存</el-button>
          <el-button style="margin-left: 20px;" @click="handleClose">取消</el-button>
        </el-form-item>
      </el-row>

    </el-form>

    <!-- 选择医生 -->
    <UserPicker ref="userPicker" @pick="pickUser" />

  </div>
</template>


<style scoped>
  .el-row {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .el-col {
    border-radius: 4px;
    height: 30px;
  }
  .bg-purple-dark {
    background: #99a9bf;
  }
  .bg-purple {
    background: #d3dce6;
  }
  .bg-purple-light {
    background: #e5e9f2;
  }
  .grid-content {
    border-radius: 4px;
    min-height: 36px;
  }
  .row-bg {
    padding: 10px 0;
    background-color: #f9fafc;
  }
  textarea {
    overflow-y: scroll;
  }
</style>


<script>
  import Request from "@/utils/request.js";
  import UserPicker from "@/views/system/user/comp/UserPicker";

  export default {
    components: {
        UserPicker
    },

    data() {
      return {
        traceCaseObj: {
          "examInfo": {},
          "patientInfo": {},
          "traceDoctorName": ""
        },

        // 从router nav 传递
        examNoParamFromNav: this.$route.params.examNo,
        prevNavObj: {},
      }
    },
    props: {
      // 从 parent component 传递
      examNoFromParentComponent: ''
    },
    beforeRouteEnter (to, from, next) {
      // 在导航完成前获取数据
      console.log("to: ", to);
      console.log("from: ", from);
      console.log("next: ", next);
      next(vm => {
        vm.prevNavObj = from;
      });
    },

    created() {
      console.log("this.examNoFromParentComponent: ", this.examNoFromParentComponent);
      console.log("this.examNoParamFromNav: ", this.examNoParamFromNav);
      this.getData();
    }, 
    filters: {
       calculateAge(birthday) {
          if (!birthday) {
            return "";
          }

          var birthday = new Date(birthday);
          var curr = new Date();
          var diff = (curr.getTime() - birthday.getTime())/(1000 * 60 * 60 * 24);  // 天
          if (diff >= 365) {
              return Math.floor(diff/365) + "岁";
          } else if (diff > 30 && diff < 365) {
              return Math.floor(diff/30) + "个月";
          } else {
              return Math.floor(diff/30) + "天";
          }
      },
      textGender(genderCode) {
          if (!genderCode) {
            return "";
          }
          switch(genderCode) {
            case "M":
              return "男";
            case "F":
              return "女";
            default:
              return "";
          }
      },

    },
    methods: {
      getData() {
        let targetExamNo = "";
        if (this.examNoFromParentComponent) {
          targetExamNo = this.examNoFromParentComponent;

        } else if (this.examNoParamFromNav) {
          targetExamNo = this.examNoParamFromNav;
        }

        if (targetExamNo) {
          Request.get("/exammanagement/traceCase/getByExamNo/" + targetExamNo)
            .then(res => {
              this.traceCaseObj = res.data;
            })
            .catch(function (error) {
                console.log(error);
            });
            return ;
        }
      },

      saveOrUpdate() {
        this.$refs["saveTraceCaseForm"].validate((valid) => {
          if (valid) {
             this.$modal.loading("正在努力更新数据，请稍后...");
             Request.post("/exammanagement/traceCase/saveOrUpdate", this.traceCaseObj)
              .then(res => {
                this.$modal.msgSuccess(res.msg);
                this.handleClose();
              })
              .catch(function (error) {
                  console.log(error);
                  this.$modal.msgSuccess(error);
              });
              this.$modal.closeLoading();

          } else {
            return false;
          }
        });

      },

      handleClose() {
        if (this.examNoFromParentComponent) {
          this.$emit('refresh', '');
          this.$emit('handleClose', '');
        } else if (this.examNoParamFromNav) {
          const tab = { name: this.prevNavObj.name };
          this.$tab.closeOpenPage(tab);
        } else {
          this.$tab.closePage();
        }
      },


      // --- 选择医生，copy from Regist.js
      toPickUser(tar, posts) {
        this.$refs["userPicker"].showPicker({target: tar, posts});
      },
      pickUser(tar, usr) {
        const fm = this.$refs.saveTraceCaseForm;

          console.log("tar: ", tar);
          console.log("usr: ", usr);
        if (tar) {
          if('examDoctors' === tar) {
            fm[`${tar}Code`] = usr.userName;
            fm[`${tar}Name`] = usr.nickName;
          } else {
            fm[tar] = usr;
          }
          this.traceCaseObj.traceDoctorCode = usr.userName;
          this.traceCaseObj.traceDoctorName = usr.nickName;
          console.log("usr.userName: ", usr.userName);
          console.log("usr.nickName: ", usr.nickName);
        }
      },

      clearTraceDocter() {
        this.traceCaseObj.traceDoctorCode = '';
        this.traceCaseObj.traceDoctorName = '';
      },

    }
  }
</script>