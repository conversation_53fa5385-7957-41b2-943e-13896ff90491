package yyy.xxx.simpfw.module.pacs.vo;

import yyy.xxx.simpfw.module.pacs.entity.TemplateBinding;
import yyy.xxx.simpfw.module.pacs.entity.WritePhrase;
import yyy.xxx.simpfw.common.core.domain.TreeSelect;

public class TemplateBindingVo extends TemplateBinding {
    public Boolean getPreferredFlag() {
        Integer preferred = getPreferred();
        return null != preferred && 1 == preferred.intValue()? Boolean.TRUE : Boolean.FALSE;
    }

    public void setPreferredFlag(Boolean preferredFlag) {
        setPreferred(null != preferredFlag && preferredFlag? 1 : 0);
    }
}
