<template>
<el-dialog ref="reportPreviewerDialog" :visible.sync="opened"
 title="报告预览" class="popupdialog" 
 width="826px" top="0px" 
 close-on-click-modal append-to-body modal-append-to-body destroy-on-close>
<div slot="title" class="dialog-slot-title dialog-slot-title-tools">
  <span>报告预览</span>
  <span class="fr">
    <span v-if="extOperExportAsDoc">
      <el-button type="primary" size="mini" disabled>正在生成报告....</el-button>
    </span>
    <span v-else>
      <el-button type="primary" size="mini" @click="plintAsReportTemplate" v-hasPermi="['exam-report:tpl']">生成模板</el-button><!--  -->
      <el-button type="primary" size="mini" @click="audit" v-if="auditable" v-hasPermi="['exam-report:audit']">审核报告</el-button>
      <el-button type="primary" size="mini" @click="plint" v-hasPermi="['exam-report:print']">打印报告</el-button>
      <el-button type="primary" size="mini" @click="switchPageSize">{{pageFitted? '实际效果' : '适应页面'}}</el-button>
    </span>
  </span>
</div>
<div class="report-previewer-wrap a4page" v-for="(page,index) in pages" :class="{'page-break': (index > 0)}">
  <div class="report-previewer-form hei100">
  <el-form class="tight-form hei100 flex-container-column">

    <div class="report-preview-header">
      <div class="report-preview-header-inner">
        <div class="report-preview-logo"><img :src="logoImage" /></div>
        <h1 class="report-preview-title">{{host.title}}</h1>
        <h2 class="report-preview-subject">{{formProps.subtitle}}</h2> 
        <!-- <h2 class="report-preview-subject">{{subject}}诊断报告单</h2>  -->
      </div>
      <div class="report-preview-hline"></div>

      <template v-if="!!report.inpType && report.inpType.dictValue === 'H'">
        <el-row class="report-preview-hinfo report-header">
          <el-col :span="6">
            <el-form-item>
                <span  class='report-header-label' slot="label">
                   姓名：
                </span>
                <span class="report-header-value">{{affectedReport.patientInfo.name}}</span>
              
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item >
                <span  class='report-header-label' slot="label">
                    性别：
                 </span>
                 <span class="report-header-value">{{affectedReport.patientInfo.gender? affectedReport.patientInfo.gender.dictLabel : ''}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item >
                <span  class='report-header-label' slot="label">
                    卡号：
                 </span>
              <span class="report-header-value">{{affectedReport.inpNo}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item >
                <span  class='report-header-label' slot="label">
                    检查号：
                 </span>
                 <span class="report-header-value">{{affectedReport.examNo}}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="report-preview-hinfo">
          <el-col :span="6">
            <el-form-item>
                <span  class='report-header-label' slot="label">
                    登记号：
                 </span>
              <span class="report-header-value">{{affectedReport.patientInfo.registNo}}</span> 
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item >
                <span  class='report-header-label' slot="label">
                    年龄：
                 </span>
                 <span class="report-header-value">
              {{affectedReport.patientInfo.age}}
              {{affectedReport.patientInfo.age && affectedReport.patientInfo.ageUnit? affectedReport.patientInfo.ageUnit.dictLabel : ''}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item >
                <span  class='report-header-label' slot="label">
                    申请科室：
                 </span>
                 <span class="report-header-value">
              {{affectedReport.reqDept? affectedReport.reqDept.deptName : ''}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item >
                <span  class='report-header-label' slot="label">
                    检查日期：
                 </span>
                 <span class="report-header-value">
              {{!!affectedReport.examTime? affectedReport.examTime.split(" ")[0] : null}}</span>
            </el-form-item>
            <!-- <el-form-item label="检查日期：">
                2022-10-31
              </el-form-item> -->
          </el-col>

        </el-row>

        <el-row class="report-preview-hinfo">
          <el-col :span="24">
            <!-- <el-form-item label="检查部位：">
              {{report.examParts_names}}
            </el-form-item> -->
            <el-form-item >
                <span  class='report-header-label' slot="label">
                    检查部位：
                 </span>
                 <span class="report-header-value">{{affectedReport.examParts_names}}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <template v-else>
        <el-row class="report-preview-hinfo">
          <el-col :span="6">
            <el-form-item label="姓名：">
              {{affectedReport.patientInfo.name}}
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="性别：">
              {{affectedReport.patientInfo.gender? affectedReport.patientInfo.gender.dictLabel : ''}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="年龄：">
              {{affectedReport.patientInfo.age}}
              {{affectedReport.patientInfo.age && affectedReport.patientInfo.ageUnit? affectedReport.patientInfo.ageUnit.dictLabel : ''}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检查号：">
              {{affectedReport.examNo}}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="report-preview-hinfo">
          <el-col :span="6">
            <el-form-item label="住院号：">
              <span class="keepline">{{affectedReport.inpNo}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="床号：">
              <span class="keepline">{{affectedReport.bedNo}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="门诊号：">
              <span class="keepline">{{affectedReport.inpNo}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请科室：">
              {{affectedReport.reqDept? affectedReport.reqDept.deptName : ''}}
            </el-form-item>
          </el-col>

        </el-row>

        <el-row class="report-preview-hinfo">
          <el-col :span="16">
            <el-form-item label="检查部位：">
              {{affectedReport.examParts_names}}
            </el-form-item>
          </el-col>

          <!-- <el-col :span="6">
            <el-form-item label="阴阳性：">
              {{report.examResultProp? report.examResultProp.dictLabel : ''}}
            </el-form-item>
          </el-col> -->
          <el-col :span="8">
            <el-form-item label="检查日期：">
              {{!!affectedReport.examTime? affectedReport.examTime.split(" ")[0] : null}}
            </el-form-item>
          </el-col>

        </el-row>
      </template>

      <div class="report-preview-hline"></div>
    </div>

    <div class="report-preview-main report-flex-item-fill flex-container-column">
      <div v-show="!asReportTemplate">
      <!-- 第1页显示 -->
      <div class="report-preview-image"
       :class="['report-preview-image-'+(null!=report.images?report.images.length:0)]"
       v-if="0 === index"
       @contextmenu.prevent="() => $event.preventDefault()">
        <template v-if="!!imagesSet">
          <div v-for="(row, ri) in imagesSet" :key="'row-'+ri">
            <div v-for="(item,idx) in row" :key="item.SOPInstanceUID" class="cornerstone-element-container">
              <div class="cornerstone-element"></div>
            </div>
          </div>
        </template>
      </div>

      <div class="report-preview-tx  report-flex-item-fill">
        <div class="report-preview-tx-exam-desc" v-if="1 === pages.length || !!page.examDesc">
          <div class="report-para-title">{{formProps.descLabel}}：</div>
          <div class="text-block-pre">{{page.examDesc}}</div>
        </div>

        <div class="report-preview-tx-exam-diag" v-if="1 === pages.length || !!page.examDiagnosis">
          <div class="report-para-title">{{formProps.diagLabel}}：</div>
          <div class="text-block-pre">{{page.examDiagnosis}}</div>
        </div>

        <!-- <template v-if="!!page.operationSuggestion">
        <div class="report-para-title">术后医嘱：</div>
        <div class="text-block-pre">{{page.operationSuggestion}}</div>
        </template> -->
      </div>
      </div>
    </div>

    <div class="report-preview-footer">
      <div class="report-preview-hline"></div>
      <el-row class="report-preview-hinfo report-footer">
        <el-col :span="6">
          <el-form-item>
            <span  class='report-header-value' slot="label">
                检查医生：
             </span>
             <span class="report-header-value">{{affectedReport.reportDoctor? affectedReport.reportDoctor.nickName : ''}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item class="signDoctorItem">
            <span  class='report-header-value' slot="label">
                审核医师：
             </span>
            <img style="position: fixed;" v-if="!asReportTemplate && signed" :src="'data:image/png;base64,'+affectedReport.signImage" />
            {{affectedReport.auditSign || ""}}
          </el-form-item>
        </el-col>
        <!-- <el-col :span="6">
          <el-form-item label="诊断医生：" class="signDoctorItem"><img v-if="!!signImage" :src="'data:image/png;base64,'+signImage" /></el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item >
            <span  class='report-header-value' slot="label">
                报告时间：
             </span>
             <span class="report-header-value">{{!!affectedReport.reportDate? affectedReport.reportDate : affectedReport.examTime}}</span>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row class="report-preview-hinfo report-footer">
        <!-- <el-col :span="6">
          <el-form-item label="书写医生：">
            {{report.reportDoctor? report.reportDoctor.nickName : ''}}
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="6">
          <el-form-item label="审核/会审医生：">{{report.auditDoctor? report.auditDoctor.nickName : ''}}</el-form-item>
        </el-col> -->
        <el-col :span="16">
          <el-form-item >
            <!-- <span  class='report-header-value' slot="label">
                复审/会诊医生：
             </span>
             <span class="report-header-value">{{report.examDoctorsName}}</span> -->
            </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item>
            <span  class='report-header-value'>
                本报告诊断仅供本院临床参考
             </span>
             </el-form-item>
        </el-col>
      </el-row>
    </div>
  </el-form>
  </div>

</div>
</el-dialog>
</template>

<style scoped src="@/assets/styles/pacs/report/ReportPreviewer.css"></style>

<script>
import model from "@/assets/scripts/gis/report/comp/ReportPreviewer"
export default model;
</script>
