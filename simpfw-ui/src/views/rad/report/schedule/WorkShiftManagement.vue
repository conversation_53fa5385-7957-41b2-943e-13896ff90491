<template>
  <div class="app-container">
    

    <div style="height: 90%; width: 100%">
      <el-row style="height: 95%">
        <el-col style="height: 100%; width: 50%">
          <el-table
            v-loading="loading"
            :data="grid.data"
            row-key="id"
            height="100%"
            stripe
            highlight-current-row
          >
          <el-table-column label="班次管理" align="center">
            <el-table-column prop="id" label="序号"/>
            <el-table-column
              prop="shiftCode"
              label="班次代码"
              :formatter="colFmt_object"
            />
            <el-table-column
              prop="shiftName"
              label="班次名称"
              :formatter="colFmt_object"
            />
            <el-table-column
              prop="beginTime"
              label="上班时间"
              :formatter="colFmt_object"
            />
            <el-table-column
              prop="endTime"
              label="下班时间"
              :formatter="colFmt_object"
            />

            <el-table-column
              prop="noteInfo"
              label="备注"
              :formatter="colFmt_object"
            />
            <el-table-column fixed="right" label="操作" width="150">
              <template slot-scope="scope">
                <el-button
                  @click="updateWorkShift(scope.row)"
                  type="text"
                  size="small"
                  >修改</el-button
                >
                <el-button @click="deleteWorkShift(scope.row)" type="text" size="small">删除</el-button>
              </template>
            </el-table-column>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col style="height: 100%; width: 50%">
          <el-table
            v-loading="loading"
            :data="groupGrid.data"
            row-key="id"
            height="100%"
            stripe
            highlight-current-row
          >
          <el-table-column label="分组管理" align="center">
            <el-table-column type="index" label="序号"/>
            <el-table-column
              prop="groupCode"
              label="分组代码"
              :formatter="colFmt_object"
            />
            <el-table-column
              prop="groupName"
              label="分组名称"
              :formatter="colFmt_object"
            />
            <el-table-column fixed="right" label="操作" width="150">
              <template slot-scope="scope">
                <el-button
                  @click="modifyGroup(scope.row)"
                  type="text"
                  size="small"
                  >修改</el-button
                >
                <el-button @click="delGroup(scope.row)" type="text" size="small">删除</el-button>
              </template>
            </el-table-column>
          </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </div>

    <div style="height: 20%; width: 100%">
      <div class="foot-tools">
        <el-button type="primary" @click="addWorkShift" style="margin-left: 500px"
          >新增班次</el-button
        >

        <el-button type="primary" @click="addGroup" style="margin-left: 500px"
          >新增组别</el-button
        >
        <el-dialog
          :title=editWorkShiftDialog.title
          :visible.sync="centerDialogVisible"
          width="60%"
          center
        >
          <span slot="footer" class="dialog-footer">
            <el-form
              ref="queryForm"
              size="small"
              :inline="true"
              label-width="88px"
            >
              <el-row>
                <el-col :span="8">
                  <el-form-item label="班次代码：" prop="title">
                    <el-input v-model="editWorkShiftDialog.shiftCode" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="班次名称：" prop="title">
                    <el-input v-model="editWorkShiftDialog.shiftName" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="备注：" prop="title">
                    <el-input v-model="editWorkShiftDialog.noteInfo" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="上班时间：" prop="title">
                  <el-time-picker
                    v-model="editWorkShiftDialog.beginTime"
                    value-format="HH:mm:ss"
                    placeholder="上班时间"
                  >
                  </el-time-picker>
                </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="下班时间：" prop="title">
                  <el-time-picker
                    arrow-control
                    v-model="editWorkShiftDialog.endTime"
                    value-format="HH:mm:ss"
                    :picker-options="{
                      minTime: editWorkShiftDialog.beginTime,
                    }"
                    placeholder="下班时间"
                  >
                  </el-time-picker>
                </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <el-button @click="centerDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="confirm">确 定</el-button>
          </span>
        </el-dialog>

        <el-dialog
      :title="groupGrid.editGroupDialog.title"
      :visible.sync="groupGrid.editGroupDialog.isVisible"
      width="60%"
      center
    >
      <span slot="footer" class="dialog-footer">
        <el-form ref="queryForm" size="small" :inline="true" label-width="88px">
          <el-row>
            <el-col :span="8">
              <el-form-item label="分组代码：" prop="title">
                <el-input
                  :disabled="true"
                  v-model="groupGrid.editGroupDialog.groupCode"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <!-- <el-form-item label="分组名称：" prop="title">
                <el-input v-model="groupGrid.editGroupDialog.groupName" />
              </el-form-item> -->
              <el-form-item label="组别名称">
                 
                <el-select
                  v-model="groupGrid.editGroupDialog.groupCode"
                  placeholder=""
                >
                  <el-option
                      v-for="dict in groupGrid.editGroupDialog.groupOptions"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    >
                    </el-option>
                </el-select>            
            </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-button @click="groupGrid.editGroupDialog.isVisible = false"
          >取 消</el-button
        >
        <el-button type="primary" @click="editGroupDialogConfirm"
          >确 定</el-button
        >
      </span>
    </el-dialog>
      </div>
    </div>
  </div>
</template>
    
<style scoped src="@/assets/styles/pacs/exammanagement/patient/Index.css"></style>
<style scoped src="@/assets/styles/pacs/exammanagement/patient/exam-info-icon.css"></style>
<script>
import model from "@/assets/scripts/rad/report/schedule/WorkShiftManagement";
export default model;
</script>
    