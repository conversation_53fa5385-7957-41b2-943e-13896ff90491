alter table d_exam_info
    modify audit_time datetime null comment '初次审核日期';

alter table d_exam_info
    add second_audit_time datetime null comment '二次审核日期' after audit_time;

alter table d_exam_info
    add third_audit_time datetime null comment '三次审核日期' after second_audit_time;

alter table d_exam_info
    modify audit_doctor_code varchar(16) null comment '初次审核医生代码';

alter table d_exam_info
    modify audit_doctor_name varchar(128) null comment '初次审核医生名字';

alter table d_exam_info
    add second_audit_doctor_code varchar(16) null comment '二次审核医生代码' after audit_doctor_name;

alter table d_exam_info
    add second_audit_doctor_name varchar(128) null comment '二次审核医生名字' after second_audit_doctor_code;

alter table d_exam_info
    add third_audit_doctor_code varchar(16) null comment '三次审核医生代码' after second_audit_doctor_name;

alter table d_exam_info
    add third_audit_doctor_name varchar(128) null comment '三次审核医生名字' after third_audit_doctor_code;