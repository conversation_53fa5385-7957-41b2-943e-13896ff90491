package yyy.xxx.simpfw.framework.auth.bo.gxca;

import lombok.Data;

@Data
public class GetQRCodeLoginStatusResponse {

    private AuStatus authStatus;

    private String doctorIdCard;

    private String doctorJobNo;

    private String doctorName;

    private String doctorNum;

    private String doctorPhone;

    private String number;

    private String timestamp;

    private String currentQueryTime;

    private String token;

    private String expireTime;
}
