package yyy.xxx.simpfw.module.uis.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import yyy.xxx.simpfw.module.uis.entity.TraceCase;

public interface TraceCaseMapper {
    
    void insert(TraceCase entity);
    
    void delete(List<Long> idList);
    
    void update(TraceCase entity);
    
    void updateByPrimaryKeyWithBLOBs(TraceCase entity);
    
    TraceCase findByExamNo(@Param("examNo") String examNo);
    
    List<TraceCase> getList(@Param("parameters") Map<String, Object> parameters);
    
    List<Map<String, Object>> groupCountsByTraceStatus();
    
}
