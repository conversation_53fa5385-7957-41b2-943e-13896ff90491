<template>
<el-container class="qc-container hei100">

  <el-header class="qc-header" height="unset">
    <el-row class="qc-header-inner">
      <el-col :span="6"><div class="qc-header-logo"><img :src="header.logo" alt="" /></div></el-col>
      <el-col :span="12"><div class="qc-header-title"><span>{{deptFullTitle}}</span></div></el-col>
      <el-col :span="6">
        <div class="qc-header-datetime">
          <div>{{header.date}}</div>
          <div>{{header.weekname}}&nbsp;{{header.time}}</div>
        </div>
      </el-col>
    </el-row>
  </el-header>

  <el-main>
    <el-row class="hei100">
      <el-col :span="16" class="hei100">
        <el-card header="等待就诊" class="nested-card hei100 waiting-card">

          <div class="waiting-parag" v-for="eit in examItems" :key="eit.id">
            <div class="qc-queue-grid-parag-title">{{eit.dictLabel}}</div>
            <div class="qc-queue-grid-parag-body">
              <template v-for="(grp, i) in queue.waiting">
              <div v-for="(item, j) in grp.list" :key="'w-'+i+'-'+j"
                class="qc-queue-grid-row"
                v-if="!!item.callNo && grp.examItemName === eit.dictLabel">
                  <strong>{{item.callNo}}</strong>
                  <strong>{{item.patientName || '-'}}</strong>
              </div>
            </template>
            </div>
          </div>
        </el-card>
        
      </el-col>
      <el-col :span="8" class="hei100">
        <el-card header="当前检查" class="nested-card hei50 rolling-pane calling-card">

          <div class="qc-queue-item" v-for="(item, i) in queue.calling" :key="'e-' + i">
            <span>
              {{item.callNo}}
              {{item.patientName || '-'}}
              ({{item.callRoomName || '-'}})
            <!-- {{item.callRoom && item.callRoom.roomName || item.examInfo && item.examInfo.equipRoom && item.examInfo.equipRoom.roomName || null}} -->
          </span>
          </div>

        </el-card>

        <el-card header="过号" class="nested-card  hei50 rolling-pane past-card">

          <div class="qc-queue-item" v-for="(item, i) in queue.past" :key="'p-' + i">
            <span>
              {{item.callNo}}
              {{item.patientName || '-'}}
              ({{item.callRoomName || '-'}})
            </span>
          </div>

        </el-card>
      </el-col>
    </el-row>
    <!-- 正在叫号 -->
    <div ref="underwayDialog" class="qc-queue-underway" :style="underwayDialogStyle" v-show="!!underwayExam">
      <!--<div class="qc-queue-underway-top">请{{underwayExam && underwayExam.callInfo? underwayExam.callInfo.callNo: null}}号</div>
      <div class="qc-queue-underway-main">{{underwayExam && underwayExam.patientInfo? underwayExam.patientInfo.name : null}}</div>
      <div class="qc-queue-underway-bottom">到{{underwayExam && underwayExam.callInfo && underwayExam.callInfo.callRoom? underwayExam.callInfo.callRoom.roomName : null}}检查</div>-->
      <!--<div class="qc-queue-underway-top">{{underwayExam && underwayExam.callInfo && underwayExam.callInfo.callRoom? underwayExam.callInfo.callRoom.roomName : null}}</div>
      <div class="qc-queue-underway-main">{{underwayExam && underwayExam.callInfo? underwayExam.callInfo.callNo: null}}号 {{underwayExam && underwayExam.patientInfo? underwayExam.patientInfo.name : null}}</div>-->
      <div class="qc-queue-underway-top">{{underwayExam && underwayExam.callRoomName || '-'}}</div>
      <div class="qc-queue-underway-main">{{underwayExam && underwayExam.callNo || '-'}} {{underwayExam && underwayExam.patientName || '-'}}</div>
    </div>
  </el-main>

  <el-footer height="unset">
    <div class="qc-footer-inner">请未叫到号的患者在候诊大厅耐心等待！</div>
  </el-footer>
</el-container>
</template>

<style scoped src="@/assets/styles/pacs/exammanagement/patient/queue/queue-board.css"></style>

<script>
export {default} from "@/assets/scripts/gis/exammanagement/queue/comp/QueueBoard";
</script>
