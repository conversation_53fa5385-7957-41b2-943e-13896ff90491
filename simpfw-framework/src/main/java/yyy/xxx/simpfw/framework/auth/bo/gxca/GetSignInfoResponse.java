package yyy.xxx.simpfw.framework.auth.bo.gxca;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.ToString;

@Data
public class GetSignInfoResponse {

    private String doctorNum;

    private String doctorName;

    private String signNum;

    private Integer signStatus;

    private String signTitle;

    private String signDesc;

    @ToString.Exclude
    private String signContent;

    @ToString.Exclude
    private String tssData;

    @ToString.Exclude
    private String signResult;

    @ToString.Exclude
    private String signImg;

    @ToString.Exclude
    private String cert;
}
