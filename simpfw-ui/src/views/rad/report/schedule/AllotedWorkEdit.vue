<template>
  <el-dialog
    title="选择"
    :visible="opened"
    width="1200px"
    custom-class="popupdialog"
    append-to-body
    @close="close"
    height="80%"
  >
    <el-form ref="queryForm" size="small" :inline="true" label-width="88px">
      <el-row>
        <el-col :span="8">
          <el-form-item label="医生：" prop="title">
            <el-select
              :disabled="!editAllotedWork.isAdd"
              v-model="editAllotedWork.user.selectUser"
              placeholder="请选择医生"
              @change="userSelectChanged"
            >
              <el-option
                v-for="item in editAllotedWork.user.userOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="工作组：" prop="title">
            <el-select
              :disabled="true"
              v-model="editAllotedWork.group.selectGroup"
              placeholder=""
            >
              <el-option
                v-for="item in editAllotedWork.group.groupOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="就诊方式：" prop="title">
            <el-select
              multiple
              v-model="editAllotedWork.inpTypeValues"
              placeholder="就诊方式"
              class="select-multi-sing"
            >
              <el-option
                v-for="dict in dict.type.uis_inp_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="工作日" prop="title">
            <el-date-picker
              v-model="editAllotedWork.beginTime"
              type="date"
              placeholder="选择开始日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="" prop="title">
            <el-checkbox v-model="editAllotedWork.endTimeAvailable">到</el-checkbox>
            <el-date-picker
              :disabled="!editAllotedWork.endTimeAvailable"
              v-model="editAllotedWork.endTime"
              type="date"
              placeholder="选择结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="班次" prop="title">
            <el-select
              multiple
              v-model="editAllotedWork.workShift.selectWorkShift"
              placeholder="请选择班次"
            >
              <el-option
                v-for="item in editAllotedWork.workShift.workShiftOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="设备类型" prop="title">
            <el-select
              multiple
              v-model="editAllotedWork.modalityType"
              placeholder="设备类型"
              collapse-tags
              class="select-multi-sing"
              clearable
            >
              <el-option
                v-for="dict in dict.type.uis_exam_modality"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="设备型号" prop="title">
            <el-select
              multiple
              v-model="editAllotedWork.examDevicesCode"
              placeholder="设备类型"
              collapse-tags
              class="select-multi-sing"
              clearable
            >
              <el-option
                v-for="item in combo.devices"
                :key="item.deviceCode"
                :label="item.device"
                :value="item.deviceCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="工作内容" prop="title">
            <el-select
              multiple
              v-model="editAllotedWork.workType"
              placeholder="工作内容"
            >
              <el-option
                v-for="item in dict.type.uis_job_content"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
          
    

      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
      <el-button type="primary" @click="delAllotedWork(null)">删除</el-button>
    </el-form>

    <el-table
            v-loading="loading"
            :data="grid.data"
            row-key="id"
            height="500px"
            stripe
            highlight-current-row
            v-if="!editAllotedWork.isAdd"
            @selection-change="handleAlloteWorkSelectionChange"
          >
          <el-table-column type="selection" />
          <el-table-column type="index" label="序号" />
          <el-table-column
            prop="workDate"
            label="工作日"
          />
          <el-table-column
            prop="workShift.shiftName"
            label="班次"
          />
          <el-table-column
            prop="modalityType"
            label="设备类型"
          />

          <el-table-column
            prop="examDevicesCode"
            label="设备型号"
          />

          <el-table-column
            prop="workType"
            label="工作内容"
          />

          <el-table-column
            prop="diagnosisType"
            label="就诊方式"
          />

          <el-table-column
            prop="reportAllocateCount"
            label="写报告分配数量"
          />

          <el-table-column
            prop="auditAllocateCount"
            label="审核报告分配数量"
          />

          <el-table-column fixed="right" label="操作">
              <template slot-scope="scope">
                <el-button
                  @click="delAllotedWork(scope.row)"
                  type="text"
                  size="small"
                  >删除</el-button
                >
              </template>
            </el-table-column>
           
          </el-table>

  </el-dialog>
</template>

<script>
import model from "@/assets/scripts/rad/report/schedule/AllotedWorkEdit";
export default model;
</script>