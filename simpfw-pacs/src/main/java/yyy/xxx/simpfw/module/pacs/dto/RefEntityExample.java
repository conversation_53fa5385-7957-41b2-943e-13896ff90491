package yyy.xxx.simpfw.module.pacs.dto;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RefEntityExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table r_*
     *
     * @mbg.generated Tue Jun 10 17:26:10 CST 2025
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table r_*
     *
     * @mbg.generated Tue Jun 10 17:26:10 CST 2025
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table r_*
     *
     * @mbg.generated Tue Jun 10 17:26:10 CST 2025
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table r_*
     *
     * @mbg.generated Tue Jun 10 17:26:10 CST 2025
     */
    public RefEntityExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table r_*
     *
     * @mbg.generated Tue Jun 10 17:26:10 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table r_*
     *
     * @mbg.generated Tue Jun 10 17:26:10 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table r_*
     *
     * @mbg.generated Tue Jun 10 17:26:10 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table r_*
     *
     * @mbg.generated Tue Jun 10 17:26:10 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table r_*
     *
     * @mbg.generated Tue Jun 10 17:26:10 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table r_*
     *
     * @mbg.generated Tue Jun 10 17:26:10 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table r_*
     *
     * @mbg.generated Tue Jun 10 17:26:10 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table r_*
     *
     * @mbg.generated Tue Jun 10 17:26:10 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table r_*
     *
     * @mbg.generated Tue Jun 10 17:26:10 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table r_*
     *
     * @mbg.generated Tue Jun 10 17:26:10 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table r_*
     *
     * @mbg.generated Tue Jun 10 17:26:10 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andObjectId1IsNull() {
            addCriterion("object_id_1 is null");
            return (Criteria) this;
        }

        public Criteria andObjectId1IsNotNull() {
            addCriterion("object_id_1 is not null");
            return (Criteria) this;
        }

        public Criteria andObjectId1EqualTo(Long value) {
            addCriterion("object_id_1 =", value, "objectId1");
            return (Criteria) this;
        }

        public Criteria andObjectId1NotEqualTo(Long value) {
            addCriterion("object_id_1 <>", value, "objectId1");
            return (Criteria) this;
        }

        public Criteria andObjectId1GreaterThan(Long value) {
            addCriterion("object_id_1 >", value, "objectId1");
            return (Criteria) this;
        }

        public Criteria andObjectId1GreaterThanOrEqualTo(Long value) {
            addCriterion("object_id_1 >=", value, "objectId1");
            return (Criteria) this;
        }

        public Criteria andObjectId1LessThan(Long value) {
            addCriterion("object_id_1 <", value, "objectId1");
            return (Criteria) this;
        }

        public Criteria andObjectId1LessThanOrEqualTo(Long value) {
            addCriterion("object_id_1 <=", value, "objectId1");
            return (Criteria) this;
        }

        public Criteria andObjectId1In(List<Long> values) {
            addCriterion("object_id_1 in", values, "objectId1");
            return (Criteria) this;
        }

        public Criteria andObjectId1NotIn(List<Long> values) {
            addCriterion("object_id_1 not in", values, "objectId1");
            return (Criteria) this;
        }

        public Criteria andObjectId1Between(Long value1, Long value2) {
            addCriterion("object_id_1 between", value1, value2, "objectId1");
            return (Criteria) this;
        }

        public Criteria andObjectId1NotBetween(Long value1, Long value2) {
            addCriterion("object_id_1 not between", value1, value2, "objectId1");
            return (Criteria) this;
        }

        public Criteria andObjectId2IsNull() {
            addCriterion("object_id_2 is null");
            return (Criteria) this;
        }

        public Criteria andObjectId2IsNotNull() {
            addCriterion("object_id_2 is not null");
            return (Criteria) this;
        }

        public Criteria andObjectId2EqualTo(Long value) {
            addCriterion("object_id_2 =", value, "objectId2");
            return (Criteria) this;
        }

        public Criteria andObjectId2NotEqualTo(Long value) {
            addCriterion("object_id_2 <>", value, "objectId2");
            return (Criteria) this;
        }

        public Criteria andObjectId2GreaterThan(Long value) {
            addCriterion("object_id_2 >", value, "objectId2");
            return (Criteria) this;
        }

        public Criteria andObjectId2GreaterThanOrEqualTo(Long value) {
            addCriterion("object_id_2 >=", value, "objectId2");
            return (Criteria) this;
        }

        public Criteria andObjectId2LessThan(Long value) {
            addCriterion("object_id_2 <", value, "objectId2");
            return (Criteria) this;
        }

        public Criteria andObjectId2LessThanOrEqualTo(Long value) {
            addCriterion("object_id_2 <=", value, "objectId2");
            return (Criteria) this;
        }

        public Criteria andObjectId2In(List<Long> values) {
            addCriterion("object_id_2 in", values, "objectId2");
            return (Criteria) this;
        }

        public Criteria andObjectId2NotIn(List<Long> values) {
            addCriterion("object_id_2 not in", values, "objectId2");
            return (Criteria) this;
        }

        public Criteria andObjectId2Between(Long value1, Long value2) {
            addCriterion("object_id_2 between", value1, value2, "objectId2");
            return (Criteria) this;
        }

        public Criteria andObjectId2NotBetween(Long value1, Long value2) {
            addCriterion("object_id_2 not between", value1, value2, "objectId2");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table r_*
     *
     * @mbg.generated do_not_delete_during_merge Tue Jun 10 17:26:10 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table r_*
     *
     * @mbg.generated Tue Jun 10 17:26:10 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}