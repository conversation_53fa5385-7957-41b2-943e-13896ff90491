package yyy.xxx.simpfw.module.pacs.bo;

public class DicomImageIndex {
    private Long studyId, imageId;
    private String username, password, fileType, imageUrl, imageName, imageType, sopClassUid, studyInstanceUid, sopInstanceUid;

    private String studyDate, studyTime, modality,manufacturer, referringPhysicianName, studyDescription, seriesDescription
            , seriesInstanceUid, patientName, patientId, protocolName
            , photometricInterpretation, planarConfiguration, patientPosition;

    private Integer seriesNumber, instanceNumber, samplesPerPixel, rows, columns, pixelAspectRatio, bitsAllocated
            , bitsStored, highBit, pixelRepresentation, stageNumber, numberOfStages, numberOfViewsInStage;

    public Long getStudyId() {
        return studyId;
    }

    public void setStudyId(Long studyId) {
        this.studyId = studyId;
    }

    public Long getImageId() {
        return imageId;
    }

    public void setImageId(Long imageId) {
        this.imageId = imageId;
    }

    public String getFileType() {
        return fileType;
    }
    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getUsername() {
        return username;
    }
    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getImageName() {
        return imageName;
    }

    public void setImageName(String imageName) {
        this.imageName = imageName;
    }

    public String getImageType() {
        return imageType;
    }

    public void setImageType(String imageType) {
        this.imageType = imageType;
    }

    public String getSopClassUid() {
        return sopClassUid;
    }

    public void setSopClassUid(String sopClassUid) {
        this.sopClassUid = sopClassUid;
    }

    public String getStudyInstanceUid() {
        return studyInstanceUid;
    }

    public void setStudyInstanceUid(String studyInstanceUid) {
        this.studyInstanceUid = studyInstanceUid;
    }

    public String getSopInstanceUid() {
        return sopInstanceUid;
    }

    public void setSopInstanceUid(String sopInstanceUid) {
        this.sopInstanceUid = sopInstanceUid;
    }

    public String getStudyDate() {
        return studyDate;
    }

    public void setStudyDate(String studyDate) {
        this.studyDate = studyDate;
    }

    public String getStudyTime() {
        return studyTime;
    }

    public void setStudyTime(String studyTime) {
        this.studyTime = studyTime;
    }

    public String getModality() {
        return modality;
    }

    public void setModality(String modality) {
        this.modality = modality;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getReferringPhysicianName() {
        return referringPhysicianName;
    }

    public void setReferringPhysicianName(String referringPhysicianName) {
        this.referringPhysicianName = referringPhysicianName;
    }

    public String getStudyDescription() {
        return studyDescription;
    }

    public void setStudyDescription(String studyDescription) {
        this.studyDescription = studyDescription;
    }

    public String getSeriesDescription() {
        return seriesDescription;
    }

    public void setSeriesDescription(String seriesDescription) {
        this.seriesDescription = seriesDescription;
    }

    public String getSeriesInstanceUid() {
        return seriesInstanceUid;
    }

    public void setSeriesInstanceUid(String seriesInstanceUid) {
        this.seriesInstanceUid = seriesInstanceUid;
    }

    public Integer getStageNumber() {
        return stageNumber;
    }

    public void setStageNumber(Integer stageNumber) {
        this.stageNumber = stageNumber;
    }

    public Integer getNumberOfStages() {
        return numberOfStages;
    }

    public void setNumberOfStages(Integer numberOfStages) {
        this.numberOfStages = numberOfStages;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getProtocolName() {
        return protocolName;
    }
    public void setProtocolName(String protocolName) {
        this.protocolName = protocolName;
    }

    public String getPhotometricInterpretation() {
        return photometricInterpretation;
    }
    public void setPhotometricInterpretation(String photometricInterpretation) {
        this.photometricInterpretation = photometricInterpretation;
    }

    public String getPlanarConfiguration() {
        return planarConfiguration;
    }
    public void setPlanarConfiguration(String planarConfiguration) {
        this.planarConfiguration = planarConfiguration;
    }

    public Integer getSeriesNumber() {
        return seriesNumber;
    }
    public void setSeriesNumber(Integer seriesNumber) {
        this.seriesNumber = seriesNumber;
    }

    public Integer getInstanceNumber() {
        return instanceNumber;
    }
    public void setInstanceNumber(Integer instanceNumber) {
        this.instanceNumber = instanceNumber;
    }

    public Integer getSamplesPerPixel() {
        return samplesPerPixel;
    }
    public void setSamplesPerPixel(Integer samplesPerPixel) {
        this.samplesPerPixel = samplesPerPixel;
    }

    public Integer getRows() {
        return rows;
    }
    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getColumns() {
        return columns;
    }
    public void setColumns(Integer columns) {
        this.columns = columns;
    }

    public Integer getPixelAspectRatio() {
        return pixelAspectRatio;
    }
    public void setPixelAspectRatio(Integer pixelAspectRatio) {
        this.pixelAspectRatio = pixelAspectRatio;
    }

    public Integer getBitsAllocated() {
        return bitsAllocated;
    }
    public void setBitsAllocated(Integer bitsAllocated) {
        this.bitsAllocated = bitsAllocated;
    }

    public Integer getBitsStored() {
        return bitsStored;
    }
    public void setBitsStored(Integer bitsStored) {
        this.bitsStored = bitsStored;
    }

    public Integer getHighBit() {
        return highBit;
    }
    public void setHighBit(Integer highBit) {
        this.highBit = highBit;
    }

    public Integer getPixelRepresentation() {
        return pixelRepresentation;
    }
    public void setPixelRepresentation(Integer pixelRepresentation) {
        this.pixelRepresentation = pixelRepresentation;
    }

    public Integer getNumberOfViewsInStage() {
        return numberOfViewsInStage;
    }
    public void setNumberOfViewsInStage(Integer numberOfViewsInStage) {
        this.numberOfViewsInStage = numberOfViewsInStage;
    }

    public String getPatientPosition() {
        return patientPosition;
    }
    public void setPatientPosition(String patientPosition) {
        this.patientPosition = patientPosition;
    }
}
