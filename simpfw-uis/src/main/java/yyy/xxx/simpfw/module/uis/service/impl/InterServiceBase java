package yyy.xxx.simpfw.module.gis.service.impl;

import java.io.IOException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import yyy.xxx.simpfw.module.gis.mapper.ExamInfoMapper;
import yyy.xxx.simpfw.module.pacs.ws.WebServiceClient;
import yyy.xxx.simpfw.system.service.ISysConfigService;

public abstract class InterServiceBase {
    protected final Logger log = LoggerFactory.getLogger(getClass());

    @Autowired protected ExamInfoMapper mapper;

    @Autowired protected ISysConfigService configService;

    @Autowired private WebServiceClient client;

    //接口地址
    private String registInterfaceService = "uis.registInterfaceService_%s";

    protected String getService(String registWay) {
        return configService.selectConfigByKey(String.format(registInterfaceService, registWay));
    }

    /**
     * 发送报文
     * @param method
     * @param params
     * @return
     */
    protected String buildSoapMessage(String method, String params) {
        StringBuilder data = new StringBuilder("<?xml version=\"1.0\" encoding=\"UTF-8\" ?>")
                .append("\n<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org\">")
                //.append("<SOAP-ENV:Header/>")
                .append("\n\t<soapenv:Body>")
                .append("<tem:").append(method).append(">");
        //参数
        data.append("<tem:Input><![CDATA[");
        data.append(params);
        data.append("]]></tem:Input>");

        data.append("</tem:").append(method).append(">")
                .append("\n\t</soapenv:Body>")
                .append("\n</soapenv:Envelope>");

        return data.toString();
    }

    protected String invokeService(String service, String soapMessage) throws IOException {
        return client.invoke(service, soapMessage);
    }
}
