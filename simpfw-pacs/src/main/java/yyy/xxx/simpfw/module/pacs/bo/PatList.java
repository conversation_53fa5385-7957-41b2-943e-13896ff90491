package yyy.xxx.simpfw.module.pacs.bo;



import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import yyy.xxx.simpfw.common.core.domain.entity.SysDept;
import yyy.xxx.simpfw.common.core.domain.entity.SysDictData;
import yyy.xxx.simpfw.common.core.domain.entity.SysUser;
import yyy.xxx.simpfw.common.utils.CommonUtil;
import yyy.xxx.simpfw.common.utils.DateUtils;
import yyy.xxx.simpfw.common.utils.DictUtils;
import yyy.xxx.simpfw.module.pacs.Const;
import yyy.xxx.simpfw.module.pacs.entity.ExamInfo;
import yyy.xxx.simpfw.module.pacs.entity.ExamParts;
import yyy.xxx.simpfw.module.pacs.entity.Patient;
import yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo;
import yyy.xxx.simpfw.module.pacs.vo.PatientVo;



/*
<PatList>
    <patientID>4815952</patientID>
    <RegNo>**********</RegNo>
    <DocumentID></DocumentID>
    <CardNo>001120000111111</CardNo>
    <HealthCardId></HealthCardId>
    <Name>陆xx</Name>
    <SexCode>1</SexCode>
    <Sex>男</Sex>
    <Age>39</Age>
    <BirthDay>1982-11-20</BirthDay>
    <InsuranceNo>*****************1</InsuranceNo>
    <Marry></Marry>
    <Address>广西北海市海城区</Address>
    <Telephone>**********</Telephone>
    <CredentialNo>*****************</CredentialNo>
    <NationCode></NationCode>
    <Nation></Nation>
    <OccupationCode>17</OccupationCode>
    <Occupation>职员</Occupation>
    <AdmNo>27745609</AdmNo>
    <AdmSerialNum>**********</AdmSerialNum>
    <AdmType>H</AdmType>
    <FeeType>市职工</FeeType>
    <WardCode></WardCode>
    <Ward></Ward>
    <RoomCode></RoomCode>
    <Room></Room>
    <BedNo></BedNo>
    <ClinicDiagnose></ClinicDiagnose>
    <ClinicDisease></ClinicDisease>
    <PatSymptom></PatSymptom>
    <OperationInfo></OperationInfo>
    <OtherInfo></OtherInfo>
    <ARNo>APPI2020112604819</ARNo>
    <OrdRowID>36676366_13</OrdRowID>
    <ARCIMCODE>15050137TJ</ARCIMCODE>
    <OrdName>腹部彩超(肝、胆、脾、胰、双肾)(体检)</OrdName>
    <OrdPrice>74.1</OrdPrice>
    <OrdBillStatus>已收费</OrdBillStatus>
    <OrdPriorityCode>NORM</OrdPriorityCode>
    <OrdPriority>临时医嘱</OrdPriority>
    <Hospital>广西医科大学第一附属医院</Hospital>
    <OrdLocCode>2017</OrdLocCode>
    <OrdLoc>体检部</OrdLoc>
    <OrdDoctorCode>jktj</OrdDoctorCode>
    <OrdDoctor>健康体检</OrdDoctor>
    <OrdDate>2020-11-26</OrdDate>
    <OrdTime>09:51:44</OrdTime>
    <OrdExeLocCode>2003</OrdExeLocCode>
    <OrdExeLoc>超声科</OrdExeLoc>
    <SampleCode></SampleCode>
    <SampleName></SampleName>
    <SendFlag></SendFlag>
    <NoteInfo></NoteInfo>
    <Position>120^胰@13^胆@251^肝脏@67^脾</Position>
    <Purpose></Purpose>
    <CurCase></CurCase>
    <Destination></Destination>
    <AutoFlag></AutoFlag>
    <BookDate></BookDate>
    <BookTime></BookTime>
    <ExecutedDate>2020-12-11</ExecutedDate>
    <ExecutedTime>08:50</ExecutedTime>
    <ExecutedUser>温东樾</ExecutedUser>
    <StatusCode>E</StatusCode>
    <OrdDeptCode>2017</OrdDeptCode>
    <OrdDeptDesc>体检部</OrdDeptDesc>
    <PatSigns></PatSigns>
    <EmgFlag>0</EmgFlag>
    <PAAdmPriority></PAAdmPriority>
    <OrdSubCatDesc>体检超声</OrdSubCatDesc>
    <OrdCatDesc>体检</OrdCatDesc>
</PatList>
 */
@XmlRootElement(name = "PatList")
public class PatList {

    private static final Logger log = LoggerFactory.getLogger(PatList.class);

    private ExamInfo examInfo = new ExamInfoVo();
    public ExamInfo getExamInfo() {
        return examInfo;
    }

    private Patient getPatient() {
        Patient patientInfo = examInfo.getPatientInfo();
        if(null == patientInfo) {
            examInfo.setPatientInfo((patientInfo = new PatientVo()));
        }
        return patientInfo;
    }
    //登记号
    @XmlElement(name = "RegNo")
    public void setPatientId(String value) {
        getPatient().setRegistNo(value);
    }
    //住院号，病历号
    @XmlElement(name = "DocumentID")
    public void setDocumentID(String value) {
        //getPatient().setMedicalRecordNo(value);
        examInfo.setInpNo(value);
    }
    //卡号（就诊卡）/门诊号
    @XmlElement(name = "CardNo")
    public void setCardNo(String value) {
        //getPatient().setCardNo(value);
        //examInfo.setInpNo(value);
        examInfo.setOutpNo(value);
    }
    @XmlElement(name = "HealthCardId")
    public void setHealthCardId(String value) {
        getPatient().setHealthCardId(value);
    }
    //患者姓名
    @XmlElement(name = "Name")
    public void setName(String value) {
        getPatient().setName(value);
    }
    //患者性别代码
    @XmlElement(name = "SexCode")
    public void setSexCode(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysDictData gender = getPatient().getGender();
            if(null == gender) { gender = new SysDictData(); }
            gender.setDictValue(value);
            getPatient().setGender(gender);
            //
            final String dictType = Const.DICT_TYPE_GENDER;
            String dd = DictUtils.getDictLabel(dictType, value);
            if(StringUtils.isBlank(dd)) {
                gender.setDictType(dictType);
            }
        }
    }
    //患者性别
    @XmlElement(name = "Sex")
    public void setSex(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysDictData gender = getPatient().getGender();
            if(null == gender) { gender = new SysDictData(); }
            gender.setDictLabel(value);
            getPatient().setGender(gender);
            //
            final String dictType = Const.DICT_TYPE_GENDER;
            String dd = DictUtils.getDictValue(dictType, value);
            if(StringUtils.isBlank(dd)) {
                gender.setDictType(dictType);
            }
        }
    }

    static int getNumber(String str,String unit){
        Pattern patternS = Pattern.compile("\\d+"+unit);
        Matcher matcherS = patternS.matcher(str);

        if(matcherS.find()){
            Pattern patternN = Pattern.compile("\\d+");
            Matcher matcherN = patternN.matcher(matcherS.group());
            if(matcherN.find()){
                String strN = matcherN.group();
                return Integer.parseInt(strN);
            }
        }
        return -1;
    }

    //患者年龄
    @XmlElement(name = "Age")
    public void setAge(String value) {
        if(NumberUtils.isDigits(value)) {
            //his旧版接口，只传数字
            Integer ageNum = Integer.valueOf(value);
            getPatient().setAge(ageNum);
            if(0!=ageNum) examInfo.setExamAge(ageNum+"Y");
        }else{
            int year = getNumber(value,"岁");
            int month = getNumber(value,"月");
            int day = getNumber(value,"天");
            if(year>0){
                getPatient().setAge(year);
            }else if(month>0){
                SysDictData ageUnit = new SysDictData();
                ageUnit.setDictLabel("月");
                //
                final String dictType = Const.DICT_TYPE_AGE_UNIT;
                String dv = DictUtils.getDictValue(dictType, value);
                ageUnit.setDictValue(dv);
                if(StringUtils.isBlank(dv)) {
                    ageUnit.setDictType(dictType);
                }

                getPatient().setAge(month);
                getPatient().setAgeUnit(ageUnit);
            }else if(day>0){
                SysDictData ageUnit = new SysDictData();
                ageUnit.setDictLabel("天");
                //
                final String dictType = Const.DICT_TYPE_AGE_UNIT;
                String dv = DictUtils.getDictValue(dictType, value);
                ageUnit.setDictValue(dv);
                if(StringUtils.isBlank(dv)) {
                    ageUnit.setDictType(dictType);
                }

                getPatient().setAge(day);
                getPatient().setAgeUnit(ageUnit);
            }
            try {
                examInfo.setExamAge(CommonUtil.convertAgeFormat(value));
            } catch (Exception err) { log.error(err.getMessage(), err); }
        }
    }
    //患者出生日期
    @XmlElement(name = "BirthDay")
    public void setBirthDay(String value) {
        if(StringUtils.isNotBlank(value)) {
            try {
                getPatient().setBirthday(DateUtils.parseDate(value));
            } catch (Exception err) { log.error(err.getMessage(), err); }
        }
    }
    //患者医保号
    @XmlElement(name = "InsuranceNo")
    public void setInsuranceNo(String value) {
        getPatient().setInsuranceNo(value);
    }
    //患者婚姻情况
    @XmlElement(name = "Marry")
    public void setMarry(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysDictData mar = new SysDictData();
            mar.setDictLabel(value);
            //
            final String dictType = Const.DICT_TYPE_MARITAL_STATUS;
            String dv = DictUtils.getDictValue(dictType, value);
            mar.setDictValue(dv);
            if(StringUtils.isBlank(dv)) {
                mar.setDictType(dictType);
            }
            getPatient().setMarriedStatus(mar);
        }
    }
    //患者住址
    @XmlElement(name = "Address")
    public void setAddress(String value) {
        getPatient().setAddress(value);
    }
    //患者电话
    @XmlElement(name = "Telephone")
    public void setTelephone(String value) {
        getPatient().setPhone(value);
    }
    //患者证件号
    @XmlElement(name = "CredentialNo")
    public void setCredentialNo(String value) {
        getPatient().setCardNo(value);
    }
    //患者民族代码
    @XmlElement(name = "NationCode")
    public void setNationCode(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysDictData dict = getPatient().getNation();
            if(null == dict) { dict = new SysDictData(); }
            dict.setDictValue(value);
            getPatient().setNation(dict);
            //
            final String dictType = Const.DICT_TYPE_NATION;
            String dd = DictUtils.getDictLabel(dictType, value);
            if(StringUtils.isBlank(dd)) {
                dict.setDictType(dictType);
            }
        }
    }
    //患者民族
    @XmlElement(name = "Nation")
    public void setNation(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysDictData dict = getPatient().getNation();
            if(null == dict) { dict = new SysDictData(); }
            dict.setDictLabel(value);
            getPatient().setNation(dict);
            //
            final String dictType = Const.DICT_TYPE_NATION;
            String dd = DictUtils.getDictValue(dictType, value);
            if(StringUtils.isBlank(dd)) {
                dict.setDictType(dictType);
            }
        }
    }
    //患者职业
    @XmlElement(name = "OccupationCode")
    public void setOccupationCode(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysDictData dict = getPatient().getOccupation();
            if(null == dict) { dict = new SysDictData(); }
            dict.setDictValue(value);
            getPatient().setOccupation(dict);
            //
            final String dictType = Const.DICT_TYPE_OCCUPATION;
            String dd = DictUtils.getDictLabel(dictType, value);
            if(StringUtils.isBlank(dd)) {
                dict.setDictType(dictType);
            }
        }
    }
    @XmlElement(name = "Occupation")
    public void setOccupation(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysDictData dict = getPatient().getOccupation();
            if(null == dict) { dict = new SysDictData(); }
            dict.setDictLabel(value);
            getPatient().setOccupation(dict);
            //
            final String dictType = Const.DICT_TYPE_OCCUPATION;
            String dd = DictUtils.getDictValue(dictType, value);
            if(StringUtils.isBlank(dd)) {
                dict.setDictType(dictType);
            }
        }
    }
    //
    @XmlElement(name = "AdmNo")
    public void setAdmNo(String value) {
        examInfo.setAdmNo(value);
    }
    @XmlElement(name = "AdmSerialNum")
    public void setAdmSerialNum(String value) {
        examInfo.setAdmSeriesNum(value);
    }

    /**
     * O	门诊
     * E	急诊
     * I	住院
     * H	体检
     * N	新生儿
     * @param value
     */
    @XmlElement(name = "AdmType")
    public void setAdmType(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysDictData dict = new SysDictData();
            dict.setDictValue(value);
            //
            final String dictType = Const.DICT_TYPE_ADM_TYPE;
            String dd = DictUtils.getDictLabel(dictType, value);
            if(StringUtils.isBlank(dd)) {
                dict.setDictType(dictType);
            }
            examInfo.setInpType(dict);
        }
    }
    //收费类型
    @XmlElement(name = "FeeType")
    public void setFeeType(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysDictData dict = new SysDictData();
            dict.setDictLabel(value);
            examInfo.setExamCostType(dict);
            //
            final String dictType = Const.DICT_TYPE_COSTS_TYPE;
            String dd = DictUtils.getDictValue(dictType, value);
            dict.setDictValue(dd);
            if(StringUtils.isBlank(dd)) {
                dict.setDictType(dictType);
            }
        }
    }
    //病区
    @XmlElement(name = "WardCode")
    public void setWardCode(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysDictData dict = examInfo.getInpWard();
            if(null == dict) { dict = new SysDictData(); }
            dict.setDictValue(value);
            //
            final String dictType = Const.DICT_TYPE_INP_WARD;
            String dd = DictUtils.getDictLabel(dictType, value);
            if(StringUtils.isBlank(dd)) {
                dict.setDictType(dictType);
            }
            examInfo.setInpWard(dict);
        }
    }
    @XmlElement(name = "Ward")
    public void setWard(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysDictData dict = examInfo.getInpWard();
            if(null == dict) { dict = new SysDictData(); }
            dict.setDictLabel(value);
            final String dictType = Const.DICT_TYPE_INP_WARD;
            String dd = DictUtils.getDictValue(dictType, value);
            if(StringUtils.isBlank(dd)) {
                dict.setDictType(dictType);
            }
            examInfo.setInpWard(dict);
        }
    }
    //病房
    @XmlElement(name = "RoomCode")
    public void setRoomCode(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysDictData dict = examInfo.getInpRoom();
            if(null == dict) { dict = new SysDictData(); }
            dict.setDictValue(value);
            //
            final String dictType = Const.DICT_TYPE_INP_ROOM;
            String dd = DictUtils.getDictLabel(dictType, value);
            if(StringUtils.isBlank(dd)) {
                dict.setDictType(dictType);
            }
            examInfo.setInpRoom(dict);
        }
    }
    @XmlElement(name = "Room")
    public void setRoom(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysDictData dict = examInfo.getInpRoom();
            if(null == dict) { dict = new SysDictData(); }
            dict.setDictLabel(value);
            final String dictType = Const.DICT_TYPE_INP_ROOM;
            String dd = DictUtils.getDictValue(dictType, value);
            if(StringUtils.isBlank(dd)) {
                dict.setDictType(dictType);
            }
            examInfo.setInpRoom(dict);
        }
    }
    //床号
    @XmlElement(name = "BedNo")
    public void setBedNo(String value) {
        examInfo.setBedNo(value);
    }
    @XmlElement(name = "ClinicDiagnose")
    public void setClinicDiagnose(String value) {
        examInfo.setClinicDiagnosis(value);
    }
    @XmlElement(name = "ClinicDisease")
    public void setClinicDisease(String value) {
        examInfo.setClinicDisease(value);
    }
    @XmlElement(name = "PatSymptom")
    public void setPatSymptom(String value) {
    }
    @XmlElement(name = "OperationInfo")
    public void setOperationInfo(String value) {
        examInfo.setOperationInfo(value);
    }
    @XmlElement(name = "OtherInfo")
    public void setOtherInfo(String value) {
        examInfo.setNoteInfo(value);
    }
    @XmlElement(name = "ARNo")
    public void setARNo(String value) {
        examInfo.setApplyId(value);
    }
    @XmlElement(name = "OrdRowID")
    public void setOrdRowID(String value) {
        examInfo.setOrdId(value);
    }
    @XmlElement(name = "ARCIMCODE")
    public void setARCIMCODE(String value) {
        examInfo.setArcimCode(value);
    }
    @XmlElement(name = "OrdName")
    public void setOrdName(String value) {
        examInfo.setOrdName(value);
        //医嘱名称做部位
        List<ExamParts> eparts;
        if(StringUtils.isNotBlank(value) && (
        		null == (eparts = examInfo.getExamParts()) || eparts.isEmpty()
        		)) {
        	eparts = null != eparts? eparts : new ArrayList<>(1);
            ExamParts part = new ExamParts();
            part.setPartsName(value);
            eparts.add(part);
            examInfo.setExamParts(eparts);
        }
    }

    @XmlElement(name = "OrdBillStatus")
    public void setOrdBillStatus(String value) {
        examInfo.setOrdBillStatus(value);
    }
    @XmlElement(name = "OrdPriorityCode")
    public void setOrdPriorityCode(String value) {
        examInfo.setOrdPriorityCode(value);
    }
    @XmlElement(name = "OrdPriority")
    public void setOrdPriority(String value) {
        examInfo.setOrdPriority(value);
    }

    @XmlElement(name = "OrdLocCode")
    public void setOrdLocCode(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysDept dept = examInfo.getReqDept();
            if(null == dept) { dept = new SysDept(); }
            dept.setDeptCode(value);
            examInfo.setReqDept(dept);
        }
    }
    @XmlElement(name = "OrdLoc")
    public void setOrdLoc(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysDept dept = examInfo.getReqDept();
            if(null == dept) { dept = new SysDept(); }
            dept.setDeptName(value);
            examInfo.setReqDept(dept);
        }
    }
    @XmlElement(name = "OrdDoctorCode")
    public void setOrdDoctorCode(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysUser usr = examInfo.getReqDoctor();
            if(null == usr) { usr = new SysUser(); }
            usr.setUserName(value);
            examInfo.setReqDoctor(usr);
        }
    }
    @XmlElement(name = "OrdDoctor")
    public void setOrdDoctor(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysUser usr = examInfo.getReqDoctor();
            if(null == usr) { usr = new SysUser(); }
            usr.setNickName(value);
            examInfo.setReqDoctor(usr);
        }
    }
    private String ordDatetime;

    @XmlElement(name = "OrdDate")
    public void setOrdDate(String value) {
        if(StringUtils.isNotBlank(value)) {
            ordDatetime = StringUtils.isBlank(ordDatetime)? value : (value + " " + ordDatetime);
            if(ordDatetime.length() > 10) {
                examInfo.setReqTime(DateUtils.parseDate(ordDatetime));
            }
        }
    }
    @XmlElement(name = "OrdTime")
    public void setOrdTime(String value) {
        ordDatetime = StringUtils.isBlank(ordDatetime)? value : (ordDatetime + " " + value);
        if(ordDatetime.length() > 10) {
            examInfo.setReqTime(DateUtils.parseDate(ordDatetime));
        }
    }

    @XmlElement(name = "OrdExeLocCode")
    public void setOrdExeLocCode(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysDept dept = examInfo.getExamDept();
            if(null == dept) { dept = new SysDept(); }
            dept.setDeptCode(value);
            examInfo.setExamDept(dept);
        }
    }
    @XmlElement(name = "OrdExeLoc")
    public void setOrdExeLoc(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysDept dept = examInfo.getExamDept();
            if(null == dept) { dept = new SysDept(); }
            dept.setDeptName(value);
            examInfo.setExamDept(dept);
        }
    }
    @XmlElement(name = "SampleCode")
    public void setSampleCode(String value) {
    }
    @XmlElement(name = "SampleName")
    public void setSampleName(String value) {
    }
    @XmlElement(name = "SendFlag")
    public void setSendFlag(String value) {
    }
    @XmlElement(name = "NoteInfo")
    public void setNoteInfo(String value) {
    	examInfo.setNoteInfo(value);
    }
    @XmlElement(name = "Position")
    public void setPosition(String value) {
        if(StringUtils.isNotBlank(value)) {
            String[] parts = value.split("\\@");
            List<ExamParts> eparts = new ArrayList<>(parts.length);
            for(String seg : parts) {
                String[] prp = seg.split("\\^");
                ExamParts part = new ExamParts();
                part.setPartsName(prp[prp.length - 1]);
                if (prp.length > 1) {
                    part.setPartsCode(prp[0]);
                }
                eparts.add(part);
            }
            examInfo.setExamParts(eparts);
        }
    }
    @XmlElement(name = "Purpose")
    public void setPurpose(String value) {
        examInfo.setExamPurpose(value);
    }
    @XmlElement(name = "CurCase")
    public void setCurCase(String value) {
    }
    @XmlElement(name = "Destination")
    public void setDestination(String value) {
    }
    @XmlElement(name = "AutoFlag")
    public void setAutoFlag(String value) {
    }

    private String bookDatetime;
    @XmlElement(name = "BookDate")
    public void setBookDate(String value) {
        if(StringUtils.isNotBlank(value)) {
            bookDatetime = StringUtils.isBlank(bookDatetime)? value : (value + " " + bookDatetime);
            if(bookDatetime.length() > 10) {
                examInfo.setAppointTime(DateUtils.parseDate(bookDatetime));
            }
        }
    }
    @XmlElement(name = "BookTime")
    public void setBookTime(String value) {
        if(StringUtils.isNotBlank(value)) {
            bookDatetime = StringUtils.isBlank(bookDatetime)? value : (bookDatetime + " " + value);
            if(bookDatetime.length() > 10) {
                examInfo.setAppointTime(DateUtils.parseDate(bookDatetime));
            }
        }
    }

    private String executeDatetime;
    @XmlElement(name = "ExecutedDate")
    public void setExecutedDate(String value) {
        if(StringUtils.isNotBlank(value)) {
            executeDatetime = StringUtils.isBlank(executeDatetime)? value : (value + " " + executeDatetime);
            if(executeDatetime.length() > 10) {
                examInfo.setExamTime(DateUtils.parseDate(executeDatetime));
            }
        }
   }
    @XmlElement(name = "ExecutedTime")
    public void setExecutedTime(String value) {
        if(StringUtils.isNotBlank(value)) {
            executeDatetime = StringUtils.isBlank(executeDatetime)? value : (executeDatetime + " " + value);
            if(executeDatetime.length() > 10) {
                examInfo.setExamTime(DateUtils.parseDate(executeDatetime));
            }
        }
    }

    @XmlElement(name = "ExecutedUser")
    public void setExecutedUser(String value) {
        if(StringUtils.isNotBlank(value)) {
            SysUser usr = new SysUser();
            usr.setNickName(value);
            examInfo.setExamDoctor(usr);
        }
    }
    @XmlElement(name = "StatusCode")
    public void setStatusCode(String value) {
        examInfo.setOrdStatus(value);
    }
    @XmlElement(name = "OrdDeptCode")
    public void setOrdDeptCode(String value) {
    }
    @XmlElement(name = "OrdDeptDesc")
    public void setOrdDeptDesc(String value) {
    }
    @XmlElement(name = "PatSigns")
    public void setPatSigns(String value) {
        examInfo.setExamDesc(value);
    }
    @XmlElement(name = "EmgFlag")
    public void setEmgFlag(Integer value) {
        examInfo.setEmergency(value);
    }
    @XmlElement(name = "PAAdmPriority")
    public void setPAAdmPriority(String value) {
    }
    @XmlElement(name = "OrdSubCatDesc")
    public void setOrdSubCatDesc(String value) {
    }
    @XmlElement(name = "OrdCatDesc")
    public void setOrdCatDesc(String value) {
    }

    @XmlElement(name = "OrdPrice")
    public void setOrdPrice(Double value) {
        examInfo.setExamCost(value);
    }
}
