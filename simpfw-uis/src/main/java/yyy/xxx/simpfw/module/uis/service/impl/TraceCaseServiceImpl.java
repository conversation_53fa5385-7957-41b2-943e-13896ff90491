package yyy.xxx.simpfw.module.uis.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import yyy.xxx.simpfw.common.annotation.DataSource;
import yyy.xxx.simpfw.common.enums.DataSourceType;
import yyy.xxx.simpfw.module.uis.entity.TraceCase;
import yyy.xxx.simpfw.module.uis.mapper.TraceCaseMapper;
import yyy.xxx.simpfw.module.uis.service.TraceCaseService;

@Service
@DataSource(value = DataSourceType.SLAVE)
public class TraceCaseServiceImpl implements TraceCaseService {

	private static Logger logger = LoggerFactory.getLogger(TraceCaseServiceImpl.class);
	
    @Autowired
    private TraceCaseMapper traceCaseMapper;

	@Override
	public List<TraceCase> getList(Map<String, Object> parameters) {
		checkQueryParameters(parameters);
		List<TraceCase> list = traceCaseMapper.getList(parameters);
		return list;
	}


	@Override
	public Map<String, Object> groupCountsByTraceStatus() {
		List<Map<String, Object>> countsMapList = traceCaseMapper.groupCountsByTraceStatus();
		
		Map<String, Object> countsMap = new HashMap<String, Object>();
		if (CollectionUtils.isEmpty(countsMapList)) {
			return countsMap;
		}
		
		Long totalCount = 0L;
		for (Map<String, Object> item : countsMapList) {
			// 追踪状态：0-不追踪， 1-计划追踪， 2-正在追踪， 3-继续追踪, 4-追踪完成
			String traceStatus = (String)item.get("traceStatus");
			switch (traceStatus) {
			case "0":
			case "1":
				break;

			case "2":
			case "3":
			case "4":
				countsMap.put(traceStatus, (long)item.get("counts"));
				totalCount += (long)item.get("counts");
				break;
			default:
				break;
			}
		}
		countsMap.put("totalCount", totalCount);
		
		return countsMap;
	}


	private void checkQueryParameters(Map<String, Object> parameters) {
		// 检查日期
		String startExamDate = (String)parameters.get("startExamDate");
		if (StringUtils.isNotBlank(startExamDate)) {
			startExamDate += " 00:00:00";
		}
		
		String endExamDate = (String)parameters.get("endExamDate");
		if (StringUtils.isNotBlank(endExamDate)) {
			endExamDate += " 23:59:59";
		}
	}

	@Override
	public TraceCase findByExamNo(String examNo) {
		return traceCaseMapper.findByExamNo(examNo);
	}


	@Override
	public void saveOrUpdate(TraceCase traceCase) {
		if (traceCase == null) {
			return ;
		}
		
		Long traceCaseId = traceCase.getId();
		if (traceCaseId == null || traceCaseId <= 0) {
			// insert
			traceCase.setCreateTime(new Date());
			traceCase.setUpdateTime(new Date());
			traceCaseMapper.insert(traceCase);
		} else {
			// update
			traceCase.setUpdateTime(new Date());
			traceCaseMapper.updateByPrimaryKeyWithBLOBs(traceCase);
		}
	}
	
	
}

