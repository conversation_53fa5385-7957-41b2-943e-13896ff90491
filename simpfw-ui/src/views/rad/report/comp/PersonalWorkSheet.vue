<template>
  <el-container ref="patientListWrap" class="nested-container" style="height: 100%">
    <el-header height="unset">
      <div class="searchFormBar">
        <el-form>
          <el-select v-model="searchForm.propName" style="width: 90px"
           @change="focusPropValue">
            <el-option
              v-for="dict in searchForm.combo_props"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>              
          <el-input ref="propValue" v-model="searchForm.propValue" clearable style="width: 120px" />
              <el-select v-model="searchForm.resultStatusValues" style="width: 130px;" clearable multiple collapse-tags class="select-multi-sing" placeholder="检查进度">
                <el-option
                  v-for="dict in dict.type.uis_exam_result_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
                <el-option label="已删除" value="-2" />
              </el-select>     
              <el-select v-model="searchForm.examItemCodes" style="width: 130px;" clearable multiple collapse-tags class="select-multi-sing" placeholder="检查项目">
                  <el-option
                    v-for="dict in dict.type.uis_exam_item"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>     
                <el-tooltip class="item" effect="dark" content="查询" placement="bottom">
                  <el-button type="primary" icon="el-icon-search" @click="getList"></el-button></el-tooltip>
        </el-form>
      </div>
    </el-header>
  
    <el-main class="flex-container-column">
      <div class="flex-item-fill scroll-auto">
  
        <el-table ref="dataGrid" v-loading="loading" :data="grid.data" row-key="id"
         stripe highlight-current-row height="100%"
         @row-contextmenu="showAction"
         @current-change="handleCurrentChange"
         @row-dblclick="handleRowDblClick">
  
          <el-table-column prop="patientInfo.name" label="患者姓名" width="80" :formatter="colFmt_object" />
          <el-table-column prop="patientInfo.gender.dictLabel" label="性别" width="50"
              :formatter="colFmt_dictData" />
          <el-table-column prop="patientInfo.age" label="年龄" width="60" :formatter="colFmt_age" />
          <el-table-column prop="examNo" label="检查号" width="120" />
          <el-table-column prop="examItem.dictLabel" label="检查项目" width="100" />
          <el-table-column prop="examParts.partsName" label="检查部位" width="100" :formatter="colFmt_object" show-overflow-tooltip  /> 
          <el-table-column prop="callInfo.callNo" label="排队号" width="70" :formatter="colFmt_object" />
          <el-table-column prop="resultStatus.dictLabel" label="工作状态" width="100">
              <template slot-scope="scope" v-if="!!scope.row.resultStatus">
                  <i class="el-icon-error state-icon-err"
                      v-if="2 === scope.row.status || '10' === scope.row.resultStatus.dictValue"></i>
                  <i :class="'el-icon-success' + ' result-status-icon-' + scope.row.resultStatus.dictValue"
                      v-else></i>
                  <span style="margin-left: 4px">{{ 2 === scope.row.status? '已删除' : (!!scope.row.resultStatus?
                      scope.row.resultStatus.dictLabel : null) }}</span>
              </template>
          </el-table-column>
          <el-table-column prop="creator.nickName" label="登记人员" min-width="160" />
          <el-table-column prop="createTime" label="登记时间" min-width="160" />
          <el-table-column prop="examTime" label="检查时间" min-width="160" />
          <el-table-column prop="equipRoom.roomName" label="检查房间" width="100" :formatter="colFmt_equipRoom" />
          <el-table-column prop="patientInfo.registNo" label="登记号" min-width="140"
              :formatter="colFmt_object" />
  
          <!-- <el-table-column label="操作" align="center" width="80" class-name="button-col" fixed="right">
            <template slot-scope="scope">
              <el-button
                title="删除"
                icon="el-icon-delete"
                @click="verifyForDelete(scope.row)"
                v-hasPermi="['exam-info:delete']"
                ></el-button>
            </template>
          </el-table-column>-->
  
        </el-table>
        <!-- 右键菜单 -->
        <Contextmenu ref="tableContextmenu" :items="actions" @select="handleAction" />
        <!-- 操作密码验证 -->
        <OperationAuth ref="operAuth" @success="handleDelete" />
  
      </div>
  
      <pagination small layout="total, prev, pager, next"
        :total="grid.pager.total"
        :page.sync="searchForm.pageNum"
        :limit.sync="searchForm.pageSize"
        @pagination="getList"
      />
      
    </el-main>
  </el-container>
  
  </template>
  
  <style scoped src="@/assets/styles/pacs/exammanagement/patient/exam-info-icon.css"></style>
  <style scoped src="@/assets/styles/pacs/exammanagement/patient/Index.css"></style>
  <style scoped>
  .searchFormBar {
    margin-bottom: 4px;
  }
  .searchFormBar >>> .el-button{
    margin-left: 4px;
    padding: 10px;
  }
  </style>
  <script>
  import model from "@/assets/scripts/rad/report/comp/PersonalWorkSheet";
  export default model;
  </script>
  