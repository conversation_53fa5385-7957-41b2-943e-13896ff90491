<template>
  <div>
  
  <el-dialog title="危急值" :visible.sync="opened" width="1100px" destroy-on-close>
    <el-container>
    <!-- <el-aside width="500px">
      <el-checkbox-group v-model="checkedCriticalValues">
        <el-checkbox 
        v-for="dict in dict.type.uis_critical_values" 
        :key="dict.value"
        :label="dict.value"
        >
        {{dict.label}}</el-checkbox>
      </el-checkbox-group>
    </el-aside> -->
  
  <el-main width="500px">
    <el-form ref="mainForm" :model="mainForm" :rules="mainFormRules" label-width="90px" class="tight-form">
  
      <!-- <el-row>
        <el-col :span="24">
          <el-form-item label="阴阳性">
            <el-select v-model="mainForm.examResultProp.dictValue" clearable>
              <el-option
                v-for="dict in dict.type.uis_critical_values"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
              
            </el-select>
          </el-form-item>
        </el-col>
      </el-row> -->
    <el-col :span="12">
      <el-row height="100%">
        <el-row>
          <el-col :span="12">
            <el-form-item label="患者姓名:">
              {{mainForm.examInfo.patientInfo.name}}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="患者编号:">
              {{mainForm.examInfo.examNo}}
            </el-form-item>
          </el-col>
        </el-row>
  
        <el-row>
          <el-col :span="12">
            <el-form-item label="病历号:">
              {{mainForm.examInfo.patientInfo.medicalRecordNo}}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查流水:">
              {{mainForm.examInfo.id}}
            </el-form-item>
          </el-col>
        </el-row>
  
        <el-row>
          <el-col :span="24">
            <el-form-item label="检查项目:">
              {{mainForm.examInfo.examItem.dictLabel}}
            </el-form-item>
          </el-col>
        </el-row>
  
        <el-row>
          <el-col :span="24">
            <el-form-item label="上报内容" prop="content">
              <el-input type="textarea" :rows="8" v-model="mainForm.content" />
            </el-form-item>
          </el-col>
        </el-row>
  
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input type="textarea" :rows="4" v-model="mainForm.noteInfo" />
            </el-form-item>
          </el-col>
        </el-row>
    
      </el-row>
    </el-col>
    <el-col :span="12">
      <el-row :span="12">
        <el-col :span="10">
          <el-form-item label="通知医师">
            <el-input v-model="mainForm.noticeDoctor.nickName" size="mini" class="input-field-narr">
              <el-button slot="append" size="mini" icon="el-icon-user-solid"
               @click="toPickUser({multiple: false, target: 'noticeDoctor', posts: [{postCode:'YS'}], roles: [{roleKey:'NJYS'}]})"
               ></el-button>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item label="通知时间">
            <el-date-picker
              v-model="mainForm.noticeTime"
              type="datetime"
              placeholder="选择日期"
              class="date-picker">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="接收医师">
            <el-input v-model="mainForm.receiveDoctor.nickName" size="mini" class="input-field-narr">
              <el-button slot="append" size="mini" icon="el-icon-user-solid"
               @click="toPickUser({multiple: false, target: 'receiveDoctor', posts: [{postCode:'YS'}], roles: [{roleKey:'NJYS'}]})"
               ></el-button>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item label="接收时间">
            <el-date-picker
              v-model="mainForm.receiveTime"
              type="datetime"
              placeholder="选择日期"
              class="date-picker">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="23">
          <el-form-item label="接收意见">
            <el-input v-model="mainForm.receiveOpinions" size="mini" class="input-field-narr"/>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="处理医师">
            <el-input v-model="mainForm.handleDoctor.nickName" size="mini" class="input-field-narr">
              <el-button slot="append" size="mini" icon="el-icon-user-solid"
               @click="toPickUser({multiple: false, target: 'handleDoctor', posts: [{postCode:'YS'}], roles: [{roleKey:'NJYS'}]})"
               ></el-button>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item label="处理时间">
            <el-date-picker
              v-model="mainForm.handleTime"
              type="datetime"
              placeholder="选择日期"
              class="date-picker">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="23">
          <el-form-item label="处理意见">
            <el-input v-model="mainForm.handleOpinions" size="mini" class="input-field-narr"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :span="12">
        <el-form-item label="危急值选择">
          <el-checkbox-group v-model="checkedCriticalValues">
            <el-checkbox 
            v-for="dict in dict.type.uis_critical_values" 
            :key="dict.value"
            :label="dict.value"
            >
            {{dict.label}}</el-checkbox>
          </el-checkbox-group>
      </el-form-item>
      </el-row>
    </el-col>
  
    </el-form>
    </el-main>
  </el-container>
    <div slot="footer" class="dialog-footer">
      <div class="foot-tools">
        <el-row>
          <el-col :span="24">
            <el-button type="primary" @click="submitForm">提交</el-button>
            <el-button @click="close">关闭</el-button>
          </el-col>
        </el-row>
      </div>
    </div>
  </el-dialog>
  
    <!-- 选择医生 -->
    <UserPicker ref="userPicker" @pick="pickUser" />
  </div>
  
  
  </template>
  
  <style scoped>
  .el-aside {
      color: #333;
      line-height: 30px;
    }
  
  .date-picker {
    width: 200px;
  }
  </style>
  
  
  <script>
  import {mergeWith} from "lodash";
  import {mergeWithNotNull} from "@/utils/common";
  
  import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";
  import * as api from "@/assets/scripts/uis/report/criticalvalues/api";
  
  //选择用户
  import UserPicker from "@/views/system/user/comp/UserPicker";
  //
  function emptyForm() {
    return {
      id: null,
      examInfo: {id: null,patientInfo:{name:null},examItem:{dictLabel:null}},
      content: null,
      noteInfo: null,
      examResultProp: {dictValue: null},
      noticeDept: {},
      noticeDoctor: {nickName:null},
      receiveDoctor: {nickName:null},
      handleDoctor: {nickName:null},
      noticeTime:null,
      receiveTime:null,
      handleTime:null,
  
      receiveOpinions:null,
      handleOpinions:null,
    };
  }
  
  const model = {
    extends: BaseDialogModel,
  
    dicts: ["uis_exam_result_prop","uis_critical_values"],
  
    components: {UserPicker},
  
    data() {
      return {
        checkedCriticalValues:[],
        mainForm: emptyForm()
        , mainFormRules: {
          content: { required: true, message: '请输入上报内容。' }
        }
      }
    },
  
    methods: {
      fill(exam) {
        let vm = this;
        if(!exam || !exam.id) {
          this.$modal.alert("请选择检查。");
          return;
        }
        //
        if(2 === exam.status) {
          this.$modal.alert("该检查已删除。");
          return;
        }
        //
        const resultStatus = exam.resultStatus;
        if(resultStatus && resultStatus.dictValue && !/^[1234]$/.test(resultStatus.dictValue)) {
          this.$modal.alert("该检查无法上报，原因：检查状态为" + resultStatus.dictLabel + "。");
          return;
        }
  
        this.mainForm = emptyForm();
        this.mainForm.examInfo = exam;
        //
        api.find({examInfo: exam}).then(res => {
          const data = res? res.rows : null;
          if(data && data.length) {
            const item = data[0];
            mergeWith(this.mainForm, item, null, mergeWithNotNull);
          }
        });
  
        this.open();
      },
  
      submitForm() {
        const fm = this.mainForm;
        this.$refs.mainForm.validate(valid => {
          if(!valid) {
            this.$modal.alert("请输入标星(*)信息。");
            return;
          }
          this.$modal.confirm("是否确定提交危急值？").then(res => {
            const hanler = res => {
              if(200 === res.code) {
                this.$modal.msgSuccess("更改完成。");
                //this.triggerBind("change");
                this.close();
                return;
              }
              this.$modal.alert(res.msg);
            };
  
            if(fm.id) {
              api.update(fm).then(hanler);
            } else {
              api.save(fm).then(hanler);
            }
          });
        });
      },
  
      /**
       * 用户选择框
       * @param tar 触发选择的属性/表单元素
       * @param opts {}
       */
       toPickUser(opts) {
        const fm = this.mainForm;
        if(!fm.id) { return; }
  
        let tar = opts.target;
        let names, codes;
        if(tar in fm) {
          names = fm[tar].nickName;
          codes = fm[tar].userName;
        } else {
          names = fm[`${tar}Name`];
          codes = fm[`${tar}Code`];
        }
        let selectedUsers = [];
        if(!!codes&&!!names) {
          codes = codes.split(",");
          names = names.split(",");
          codes.forEach((e, i) => {
            selectedUsers.push({userName: e, nickName: names[i]});
          });
        }
        opts.selectedUsers = selectedUsers;
  
        this.$refs.userPicker.showPicker(opts);
      },
      pickUser(tar, users) {
        let fm = this.mainForm, names = [], codes = [];
        if(!!users.length) {
          users.forEach(e => {
            names.push(e.nickName);
            codes.push(e.userName);
          });
        } else {
          names.push(users.nickName);
          codes.push(users.userName);
        }
        names = names.join(",");
        codes = codes.join(",");
  
        if(tar in fm) {
          fm[tar].nickName = names;
          fm[tar].userName = codes;
        } else {
          fm[`${tar}Name`] = names;
          fm[`${tar}Code`] = codes;
        }
        console.log("this.mainForm",this.mainForm);
      },
    },
  
    watch:{
      "checkedCriticalValues": {
        handler (newv, oldv) {
          let vm = this;
          //危急值字典
          vm.mainForm.content = "";
          const dictData = vm.dict.type["uis_critical_values"];
          newv.forEach(e => {
              //vm.mainForm.content+=e;
              let dict = dictData.find(d => e === d.value);
              if(dict){
                vm.mainForm.content+=dict.label
              }
            });
        }
      }
    }
  };
  export default model;
  </script>
  