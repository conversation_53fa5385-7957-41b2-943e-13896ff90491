package yyy.xxx.simpfw.framework.auth.bo.gxca;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class GetDoctorInfoRequest extends BaseRequest {

    private String doctorPhone;

    private String doctorIdCard;

    private String doctorJobNo;

    private String doctorNum;

    public GetDoctorInfoRequest() {
        this.setApiPath("/portal/doctor/info");
    }
}
