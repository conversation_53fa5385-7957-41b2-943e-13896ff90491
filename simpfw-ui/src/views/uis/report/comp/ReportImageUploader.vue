<template>
  <el-dialog title="导入影像" :visible.sync="opened" width="400px" destroy-on-close>
    <el-form ref="mainForm" label-width="100px" class="tight-form">
      <div><el-alert type="warning" effect="dark" :closable="false" title="请选择影像文件，当前支持jpg和mp4格式文件"></el-alert></div>
      <el-row>
        <el-col :span="24">
          <div class="riu-file-field"><input id="imageFile" type="file" accept=".jpg, .mp4" multiple></div>
        </el-col>
      </el-row>
    </el-form>
  
    <div slot="footer" class="dialog-footer">
      <div class="foot-tools">
        <el-row>
          <el-col :span="24">
            <el-button type="primary" @click="submitForm" :loading="processing">导入</el-button>
            <el-button @click="close" :disabled="processing">关闭</el-button>
          </el-col>
        </el-row>
      </div>
    </div>
  
  </el-dialog>
</template>

<style scoped>
.riu-file-field{
  margin: 8px 0px;
}
</style>

<script>
import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";

import {resback as resbackImage} from "@/assets/scripts/pacs/image/api.imageTranster";

const model = {
  extends: BaseDialogModel,

  data() {
    return {
      report: null,
      processing: false
    }
  },

  methods: {
    prepare(report) {
      this.report = report;

      this.open();
    },

    submitForm() {
      let form = new FormData();
      const files = document.querySelector("#imageFile").files;
      if(!files || files.length === 0) {
        this.$modal.alert("请选择jpg或mp4文件。");
        return;
      }
      Array.from(files).forEach(f => {
        form.append("file", f);
      });
      //
      const rep = this.report;
      form.append("examInfoId", rep.id);
      //console.log(form);
      this.processing = true;
      resbackImage(form).then(r => {
        //console.log(r);
        this.processing = false;
        this.triggerBind("success");
        this.close();
      }).catch(err => this.processing = false);
    }
  }
};
export default model;
</script>
  