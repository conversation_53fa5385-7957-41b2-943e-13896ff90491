
-- 2022-12-13 病例追踪表
use uis;
CREATE TABLE `d_trace_case` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `exam_info_id` bigint(20) NOT NULL COMMENT '检查ID',
  `exam_conclusion` text COMMENT '检查结论',
  `auxiliary_exam` text COMMENT '辅助检查',
  `surgery_conclusion` text COMMENT '手术结论',
  `pathology_result` text COMMENT '病理结果',
  `trace_conclusion` text COMMENT '追踪结论',
  `confirm_status` int(11) DEFAULT '0' COMMENT '确认状态：0-符合，1-基本符合， 2-不符合',
  `trace_status` int(11) NOT NULL DEFAULT '0' COMMENT '追踪状态：0-不追踪， 1-计划追踪， 2-正在追踪， 3-继续追踪, 4-追踪完成',
  `trace_date` date DEFAULT NULL COMMENT '追踪日期',
  `plan_trace_date` date DEFAULT NULL COMMENT '计划追踪日期',
  `trace_doctor_code` varchar(20) DEFAULT NULL COMMENT '追踪医生code',
  `trace_doctor_name` varchar(50) DEFAULT NULL COMMENT '追踪医生姓名',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_exam_info_id` (`exam_info_id`),
  KEY `idx_trace_doctor_code` (`trace_doctor_code`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4;

-- 2022-12-13 病例追踪菜单
insert into cmbase.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) values('病例追踪-详情','0','39','traceCaseAddEdit','uis/tracecase/add_edit',NULL,'1','0','C','1','0','','guide','admin','2022-12-08 09:58:42','admin','2022-12-08 10:10:38','');
insert into cmbase.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) values('病例追踪','0','30','traceCase','uis/tracecase/index','','1','0','C','0','0','','cascader','admin','2022-11-28 14:07:07','admin','2022-12-02 17:46:29','');




