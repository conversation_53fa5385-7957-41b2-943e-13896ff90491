<template>
  <div style="height: 100%">
    <el-header height="unset">
     
            <el-button
              type="primary"
              
              @click="addScheduleUser(null)"
              >添加</el-button>

              <el-select style="margin-left: 1050px"
              clearable
                v-model="editUserGroup.searchSelectGroup"
                placeholder="请选择分组"
              >
                <el-option
                  v-for="item in editUserGroup.groupOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>

            <el-button type="primary" style="margin-left: 20px"
              @click="search ">查询</el-button
            >
            <el-button
              type="primary"
              style="margin-left: 10px"
              @click="delScheduleUser(null)"
              >删除</el-button
            >
        
    </el-header>

    <el-row style="height: 90%">
      <el-col style="height: 100%; width: 50%">
        <el-table
          v-loading="loading"
          :data="allUserGrid.data"
          row-key="id"
          width="100%"
          height="100%"
          stripe
          highlight-current-row
          @selection-change="handleAllUserSelectionChange"
        >
          <el-table-column label="科室全部人员" align="center">
            <el-table-column type="selection" />
            <el-table-column type="index" label="序号" />
            <el-table-column
              prop="userName"
              label="医生工号"
              :formatter="colFmt_object"
            />
            <el-table-column
              prop="nickName"
              label="医生姓名"
              :formatter="colFmt_object"
            />
            <el-table-column
              prop="remark"
              label="备注"
              :formatter="colFmt_object"
            />
            <el-table-column fixed="right" label="操作">
              <template slot-scope="scope">
                <el-button
                  @click="addScheduleUser(scope.row)"
                  type="text"
                  size="small"
                  >添加</el-button
                >
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col style="height: 100%; width: 50%">
        <el-table
          v-loading="loading"
          :data="grid.data"
          row-key="id"
          height="100%"
          stripe
          highlight-current-row
          @selection-change="handleUserSelectionChange"
        >
          <el-table-column label="需要排班人员" align="center">
            <el-table-column type="selection" />
            <el-table-column type="index" label="序号" />
            <el-table-column
              prop="userCode"
              label="医生工号"
              :formatter="colFmt_object"
            />
            <el-table-column
              prop="userName"
              label="医生姓名"
              :formatter="colFmt_object"
            />
            <el-table-column
              prop="group.groupName"
              label="所属组别"
              :formatter="colFmt_object"
            />
            <el-table-column
              prop="noteInfo"
              label="备注"
              :formatter="colFmt_object"
            />
            <el-table-column fixed="right" label="操作">
              <template slot-scope="scope">
                <el-button
                  @click="updateUserGroup(scope.row)"
                  type="text"
                  size="small"
                  >修改</el-button
                >
                <el-button
                  @click="delScheduleUser(scope.row)"
                  type="text"
                  size="small"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
     <!-- 修改用户组别 -->
     <el-dialog 
    :title="editUserGroup.title"
      :visible.sync="editUserGroup.isVisible"
      width="60%"
      center>
      <el-form ref="queryForm" size="small" :inline="true" label-width="88px">
        <el-row>
          <el-col :span="8">
            <el-form-item v-if="this.editUserGroup.isAdd" label=" ">
              <el-select
                v-model="editUserGroup.selectUser"
                placeholder="请选择医生"
                
              >
                <el-option
                  v-for="item in editUserGroup.userOptions"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="医生姓名：" prop="title" v-if="this.editUserGroup.isAdd==false">
                <el-input 
                  :disabled = true
                  v-model="editUserGroup.tableSelectUser.userName"
                />
              </el-form-item>

          </el-col>
          <el-col :span="8">
            <el-form-item label=" ">
              <el-select
                v-model="editUserGroup.selectGroup"
                placeholder="请选择分组"
              >
                <el-option
                  v-for="item in editUserGroup.groupOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-button @click="editUserGroup.isVisible = false"
          >取 消</el-button
        >
        <el-button type="primary" @click="userGroupConfirm"
          >确 定</el-button
        >
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>
    
<style scoped src="@/assets/styles/pacs/exammanagement/patient/Index.css"></style>
<style scoped src="@/assets/styles/pacs/exammanagement/patient/exam-info-icon.css"></style>
<script>
import model from "@/assets/scripts/rad/report/schedule/ScheduleUserManagement";
export default model;
</script>
    