package yyy.xxx.simpfw.framework.auth.bo;

import com.fasterxml.jackson.annotation.JsonIgnore;

public class LinkSignResult {
    private Integer code;
    private String message;
    private LinkSignStatus data;

    public Integer getCode() {
        return code;
    }
    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }
    public void setMessage(String message) {
        this.message = message;
    }

    public LinkSignStatus getData() {
        return data;
    }
    public void setData(LinkSignStatus data) {
        this.data = data;
    }

    @JsonIgnore
    public boolean isSuccess() {
        return null != code && 20000 == code.intValue();
    }
}
