USE mt_db_manager;
DROP PROCEDURE IF EXISTS create_index_if_not_exists;
DELIMITER $$
CREATE PROCEDURE create_index_if_not_exists(
    IN p_db_name VARCHAR(100),
    IN p_table_name VARCHAR(100),
    IN p_index_name VARCHAR(100),
    IN p_column_list VARCHAR(255)
)
BEGIN
    DECLARE index_exists INT DEFAULT 0;

    SELECT COUNT(1) INTO index_exists
    FROM information_schema.STATISTICS
    WHERE table_schema = p_db_name
      AND table_name = p_table_name
      AND index_name = p_index_name;

    IF index_exists = 0 THEN
        SET @create_stmt = CONCAT('CREATE INDEX ', p_index_name, ' ON ', p_db_name, '.', p_table_name, '(', p_column_list, ')');
        PREPARE stmt FROM @create_stmt;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END $$

DELIMITER ;

-- 使用方法
-- CALL create_index_if_not_exists('数据库名', '表名', '索引名', '列名');