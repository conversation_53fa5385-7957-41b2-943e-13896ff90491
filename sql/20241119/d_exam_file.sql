create table if not exists pacs_mtn2.d_exam_file
(
    id             bigint unsigned auto_increment comment '主键'
        primary key,
    exam_uid       varchar(32)                      not null comment '检查表UID',
    exam_item_code varchar(16)                      null comment '检查项目',
    file_name      varchar(256)                     not null comment '文件名',
    file_path      text                             not null comment '该文件的完整存储路径',
    file_md5       varchar(256)                     not null comment '文件md5值',
    upload_time    datetime                         not null comment '文件上传时间',
    file_size      bigint unsigned                  not null comment '文件大小(bytes)',
    create_time    datetime         default (now()) not null comment '创建时间',
    update_time    datetime                         null on update CURRENT_TIMESTAMP comment '更新时间',
    status         tinyint unsigned default '0'     not null comment '文件状态（0正常 1删除）'
)
    comment '检查报告文件表';

create index d_exam_file_exam_item_code_index
    on pacs_mtn2.d_exam_file (exam_item_code);

create index d_exam_file_exam_uid_index
    on pacs_mtn2.d_exam_file (exam_uid);

create index d_exam_file_file_md5_index
    on pacs_mtn2.d_exam_file (file_md5);

create index d_exam_file_file_name_index
    on pacs_mtn2.d_exam_file (file_name);