<template>
<div class="report-writer-wrap hei100">
  <iframe frameborder="0" class="nested-frame-full" :src="viewUrl"></iframe>
</div>
</template>

<style scoped>
.report-writer-image-wrap{
  user-select: none;
}
.nested-frame-full{
  width: 100%;
  height: 100%;
  border-width: 0;
}
</style>

<script>
import { mapGetters } from 'vuex';
import { getToken } from '@/utils/auth'
//
import ReportWriterImageComm from "@/assets/scripts/uis/report/comp/ReportWriterImageComm";
//检查信息接口
//import * as eiapi from "@/assets/scripts/uis/exammanagement/examinfo/api";
//影像接口
import {dcmViewer} from "@/assets/scripts/pacs/image/api";
//
const model = {
  name: "RadReportWriterImage",

  extends: ReportWriterImageComm,

  data() {
    return {
      viewUrl: "about:blank",
      viewExamReportId:null,
      studyInstanceUid:null
    }
  },

  methods: {
    /**
     * 查看影像
     * @param {*} examInfoId 指定检查
     * @param {*} studyUId 指定study
     */
    view(examInfoId, studyUId) {
      this.viewExamReportId = examInfoId
      this.studyInstanceUid = studyUId;
      dcmViewer(examInfoId, studyUId).then(res => {
        const viewer = res.viewer;
        if(!viewer) {
          this.$modal.msgWarning("尚不支持。");
          return;
        }
        const examInfo = res.examInfo, patientId = examInfo && examInfo.patientInfo? examInfo.patientInfo.registNo : "";
        const dicomStudy = res.dicomStudy || {};
        const {studyInstanceUid, dicomStudyInstanceUid} = dicomStudy;
        if(!studyInstanceUid && !dicomStudyInstanceUid) {
          this.$modal.msgWarning("该检查没有影像信息。");
          return;
        }
        //http://localhost:3081
        //临时, 有dicomInstanceUid假定为dicom否则为jpg
        const imageType = !!dicomStudyInstanceUid? "DCM" : "JPG";
        this.viewUrl = viewer + "/viewer/" + imageType + ".." + (dicomStudyInstanceUid || studyInstanceUid) 
          + "?studyApi=rhpacs&mpiid=" + (patientId || "") + "&origStudy=" + (examInfoId || "");
      })
    },

    checkSocketUrl() {
      if(!this.socketUrl) {
        this.socketUrl = this.socketBaseUrl + "?clientType=2&token=" + getToken();
      }
    },

    handleSocketOpen() {
      //
      //this.focusWindow();
    },

    handleSocketReceived(evt) {
      //console.log(evt);
      let data = event.data;
      if(!data) { return; }
      data = JSON.parse(data);
      //解析数据
      let cmd = data.cmd, url = data.url;
      if(!!url) {
        //窗口置顶
        const win = top.open(url);
        if(!!win) {
          win.close();
        }

        top.focus();
        this.focusWindow();

        top.location.replace(url);

      } else if("close" === cmd) {
        //关闭窗口
        top.close();
      }
    },

    handleTabClick() {
      //console.log(arguments);
    },
    //
    setupPage() {
      //浏览器标题
      top.document.title = this.appTitle + "-影像浏览"
    },
    //从地址栏获取检查信息
    resolveExam() {
      let query = location.search;
      if(!query) {
        return;
      }
      //去掉问号
      query = query.substring(1);
      const paramsMap = {};
      const params = query.split("&");
      params.forEach(p => {
        const pair = p.split("=");
        paramsMap[pair[0]] = pair.length === 2? pair[1] : null;
      });
      // console.log("te",paramsMap);
      //
      const examReportId = paramsMap["exam"];
      const studyInstanceUid = paramsMap["studyInstanceUid"];
      if(!examReportId&&!studyInstanceUid) {
        return;
      }
      if(examReportId!=this.viewExamReportId||studyInstanceUid!=this.studyInstanceUid) {
        this.view(examReportId,studyInstanceUid);
      }
    }
  },

  activated() {
    if(this.ofRoute.image) {
      this.setupPage();

      this.resolveExam();
    }
  },

  created() {
    if(this.ofRoute.image) {
      this.setupPage();

      this.resolveExam();
    }
  },

  computed: {
    ...mapGetters(['appTitle'])
  }

};
export default model;
</script>
