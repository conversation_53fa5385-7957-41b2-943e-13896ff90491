package yyy.xxx.simpfw.framework.auth.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.NumberUtils;
import org.springframework.web.bind.annotation.RequestParam;
import yyy.xxx.simpfw.common.core.domain.AjaxResult;
import yyy.xxx.simpfw.common.core.domain.entity.SysConfig;
import yyy.xxx.simpfw.common.core.domain.entity.SysUser;
import yyy.xxx.simpfw.common.core.domain.model.LoginUser;
import yyy.xxx.simpfw.common.core.redis.RedisCache;
import yyy.xxx.simpfw.common.core.service.HttpClientService;
import yyy.xxx.simpfw.common.utils.SecurityUtils;
import yyy.xxx.simpfw.common.utils.StringUtils;
import yyy.xxx.simpfw.framework.auth.QRAuthConst;
import yyy.xxx.simpfw.framework.auth.bo.*;
import yyy.xxx.simpfw.framework.web.service.SysPermissionService;
import yyy.xxx.simpfw.framework.web.service.TokenService;
import yyy.xxx.simpfw.system.mapper.SysConfigMapper;
import yyy.xxx.simpfw.system.service.ISysUserService;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class CertAuthService implements QRAuthConst {

    private static final Logger logger = LoggerFactory.getLogger(CertAuthService.class);

    @Autowired private HttpClientService httpClientService;

    private CertAuthProp certAuthProp;

    @Autowired private TokenService tokenService;

    @Autowired
    private ISysUserService userService;

    @Autowired private SysPermissionService permissionService;

    @Autowired
    private RedisCache redisCache;

    @Autowired private SysConfigMapper configMapper;

    //支持的扫码类型：3-App，4-微信小程序，0-都支持
    private Integer certauthQrType = 3;

//    public CertAuthService(@Value("${certauth.base:#{null}}") String base
//        , @Value("${certauth.projectID:#{null}}") String projectUid
//        , @Value("${certauth.applicationID:#{null}}") String applicationId
//        , @Value("${certauth.qr.types:#{null}}") String[] qrTypes0
//        , @Value("${certauth.servers:#{null}}") String[] servers0) {
//        Integer[] qrTypes = null != qrTypes0? new Integer[qrTypes0.length] : null;
//        for(int i = 0; null != qrTypes0 && i < qrTypes0.length; i ++) {
//            qrTypes[i] = Integer.valueOf(qrTypes0[i]);
//        }
//        //
//        CertAuthServer[] servers = new CertAuthServer[servers0.length];
//        for(int i = 0; i < servers0.length; i ++) {
//            servers[i] = new CertAuthServer((servers0[i]));
//        }
//        certAuthProp = new CertAuthProp(base, projectUid, applicationId, qrTypes, servers);
//        if(null != qrTypes) {
//            //1个表明使用指定类型，不指定或2个表明支持App和小程序小程序
//            certauthQrType = 1 == qrTypes.length? qrTypes[0] : certauthQrType;
//        }
//    }

    //接口访问参数系统参数配置
    private static final String AuthServiceConfigKey = "uis.wztsign.service";

    //用户证书查询
    private static final String url_cert_search = "%s/cert/search/2CKV1"
            , params_cert_serch = "{\"userUid\":\"%s\",\"projectUid\":%s}";//
    //客户端获取认证二维码
    private static final String url_qrcode = "%s/api/login/qrcode/nostatus/2CKV1"
            , params_qrcode = "{\"loginTypeBitValue\":16,\"type\":%d,\"projectUid\":%s,\"applicationId\":%s}";
    //轮询二维码认证结果
    private static final String url_qrcode_status = "%s/api/qrcode/status"
            , params_qrcode_status = "{\"qrCodeIdentity\":\"%s\"}";
    //获取签名图片
    private static final String url_seal_downloadsealpic = "%s/seal/downloadsealpic"
            , params_seal_downloadsealpic = "{\"userToken\":\"%s\"}";
    //查询已被授权信息
    private static final String url_authed_user = "%s/authorizeduser/certcontent/list/authorized"
            , params_authed_user = "{\"cert\": {\"certContent\": \"%s\"}}";
    //证书用户口令认证，loginUsage：0为正常身份认证，1为绑定用户时的身份认证
    private static final String url_pin_login = "%s/user/certcontent/dologin"
            , params_pin_login = "{\"loginUsage\":0,\"userLoginType\":1,\"userPin\":\"%s\",\"cert\":{\"certContent\":\"%s\"}}";
    //授权人口令认证
    private static final String url_pin_login_authUser = "%s/authorizeduser/certcontent/pin/login"
            , params_pin_login_authUser = "{\"loginUsage\":0,\"authorizedUserId\":\"%s\",\"userPin\":\"%s\",\"cert\":{\"certContent\":\"%s\"}}";
    //证书用户口令重试次数
    private  static final String url_cert_surplus_num = "%s/cert/certcontent/getSurplusNum"
            , params_cert_surplus_num = "{\"cert\": {\"certContent\": \"%s\"}}";
    //授权人口令重试次数
    private  static final String url_cert_surplus_num_authUser = "%s/authorizeduser/getSurplusNum"
            , params_cert_surplus_num_authUser = "{\"authorizedUserId\": \"%s\"}";
    //
    private static final String url_oprinfo = "%s/api/oprinfo/search/all"
            , params_oprinfo = "{\"userToken\":\"%s\"}";

    public static final String SECURITY_KEY = "$,$$,$$$";

    protected static final String ERRM_INVALID_CONFIG = "请配置接口信息。", ERRM_UNAVAIL_SERVICE = "服务不可用。";

    private static final TypeReference<CertAuthResponse<List>> typeRef_array = new TypeReference<CertAuthResponse<List>>() {};
    private static final TypeReference<CertAuthResponse<Object>> typeRef_object = new TypeReference<CertAuthResponse<Object>>() {};
    private static final TypeReference<CertAuthResponse<String>> typeRef_text = new TypeReference<CertAuthResponse<String>>() {};

    private static final TypeReference<CertAuthResponsePlain<List>> typeRef_array_plain = new TypeReference<CertAuthResponsePlain<List>>() {};
    private static final TypeReference<CertAuthResponsePlain<Integer>> typeRef_int_plain = new TypeReference<CertAuthResponsePlain<Integer>>() {};

    /**
     * `检查接口配置，确定接口是否可用
     * @return true-可用，false-不可用
     */
    public boolean checkConfig() {
        if(null == certAuthProp) {
            SysConfig scfg = configMapper.checkConfigKeyUnique(AuthServiceConfigKey);
            if (null == scfg || StringUtils.isBlank(scfg.getConfigValue())) {
                //return false;
                throw
                        new RuntimeException(ERRM_INVALID_CONFIG);
            }
            //try {
            JSONObject cfg = JSON.parseObject(scfg.getConfigValue());
            Integer[] qrTypes = new Integer[1];
            qrTypes[0] = Integer.valueOf(1);;
            certAuthProp  = new CertAuthProp(cfg.getString("base"), cfg.getString("projectID"), cfg.getString("applicationID"), qrTypes, null);
                    //} catch (Exception err) {
            //    logger.error(err.getMessage(), err);
            //}
        }
        refreshCfg();
        return StringUtils.isNotBlank(certAuthProp.getBase())
                && StringUtils.isNotBlank(certAuthProp.getProjectUid()) && StringUtils.isNotBlank(certAuthProp.getApplicationId());
    }

    /**
     * `查询用户UID拥有的证书
     * @param userUid 用户UID
     * @return {
     *    "responseResult":    {
     *       "status": 0,
     *       "msg": "成功"
     *    },
     *    "contents": {"985":    [
     *       "MIIDXzCCAwagAwIBAgILEOGfWoNWMO5mBAswCgYIKoEcz1UBg3UwYjELMAkGA1UEBhMCQ04xJDAiBgNVBAoMG05FVENBIENlcnRpZmljYXRlIEF1dGhvcml0eTEtMCsGA1UEAwwkTkVUQ0EgU00yIFRFU1QwMSBhbmQgRXZhbHVhdGlvbiBDQTAxMB4XDTIxMTIxNDA5MDgyN1oXDTI0MTIxNDA5MDgyN1owejELMAkGA1UEBhMCQ04xEjAQBgNVBAgMCUd1YW5nZG9uZzEqMCgGA1UEBwwh5bm/5Lic55yB5bm/5bee5biC6LaK56eA5Yy6MTYxMjUzMRcwFQYDVQQKDA7mnLrmnoQoMTYxMjUzKTESMBAGA1UEAwwJ5byg5Li75Lu7MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEXB/rrn07zUtmD3Ca/pWjxHVc+Bn3YwdHeu/olrS4avVtbcamcPZKOip2YXTs7i3KPyJqXYOCGPRU7t6H9UVjRaOCAYkwggGFMAwGA1UdEwEB/wQCMAAwDgYDVR0PAQH/BAQDAgbAMGsGA1UdIARkMGIwYAYKKwYBBAGBkkgNCjBSMFAGCCsGAQUFBwIBFkRodHRwOi8vd3d3LmNuY2EubmV0L2NzL2tub3dsZWRnZS93aGl0ZXBhcGVyL2Nwcy9uZXRDQXRlc3RjZXJ0Y3BzLnBkZjAfBgNVHSMEGDAWgBQMe+ticwN1+oxKJAz2jzshZX4X6TAdBgNVHQ4EFgQUGDtVTk72JMIZrDh59Jb8Q4y75a4wNAYKKwYBBAGBkkgBDgQmDCQ1NjQ1YThkMjZiNDkwY2FlOTgyYTQzMWE0NjkzZDg4MkBTMDIwgYEGCCsGAQUFBwEBBHUwczA7BggrBgEFBQcwAYYvaHR0cDovLzEwNi41Mi4yNDMuMjIyOjYxODAvb2NzcGNvbnNvbGUvY2hlY2suZG8wNAYIKwYBBQUHMAGGKGh0dHA6Ly8xOTIuMTY4LjAuNjEvb2NzcGNvbnNvbGUvY2hlY2suZG8wCgYIKoEcz1UBg3UDRwAwRAIgcc1qMzSx58KRQpqy739qzAhJwc8R2e5CRDw+277ZcDACIBWzNKXXVTBxSr+EwNXSvZFvIWWKoEHIUhwaKnMs2Dc7",
     *       "MIIDXzCCAwagAwIBAgILIIi4U33eDe922vUwCgYIKoEcz1UBg3UwYjELMAkGA1UEBhMCQ04xJDAiBgNVBAoMG05FVENBIENlcnRpZmljYXRlIEF1dGhvcml0eTEtMCsGA1UEAwwkTkVUQ0EgU00yIFRFU1QwMSBhbmQgRXZhbHVhdGlvbiBDQTAxMB4XDTIxMTIxNDA5MDgyN1oXDTI0MTIxNDA5MDgyN1owejELMAkGA1UEBhMCQ04xEjAQBgNVBAgMCUd1YW5nZG9uZzEqMCgGA1UEBwwh5bm/5Lic55yB5bm/5bee5biC6LaK56eA5Yy6MTYxMjUzMRcwFQYDVQQKDA7mnLrmnoQoMTYxMjUzKTESMBAGA1UEAwwJ5byg5Li75Lu7MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAERkGw2/C4DJ8PgiBotJ4Xqix/qI2BG1eQQj6eq/vdGoOoTjyQ75LkvQkZfakHxRoCzPAL4r8HEnp9r4aetbpFz6OCAYkwggGFMAwGA1UdEwEB/wQCMAAwDgYDVR0PAQH/BAQDAgM4MGsGA1UdIARkMGIwYAYKKwYBBAGBkkgNCjBSMFAGCCsGAQUFBwIBFkRodHRwOi8vd3d3LmNuY2EubmV0L2NzL2tub3dsZWRnZS93aGl0ZXBhcGVyL2Nwcy9uZXRDQXRlc3RjZXJ0Y3BzLnBkZjAfBgNVHSMEGDAWgBQMe+ticwN1+oxKJAz2jzshZX4X6TAdBgNVHQ4EFgQU/r4BHTekwC0wLzW6j0XNXBqxnNAwNAYKKwYBBAGBkkgBDgQmDCQ1NjQ1YThkMjZiNDkwY2FlOTgyYTQzMWE0NjkzZDg4MkBTMDIwgYEGCCsGAQUFBwEBBHUwczA7BggrBgEFBQcwAYYvaHR0cDovLzEwNi41Mi4yNDMuMjIyOjYxODAvb2NzcGNvbnNvbGUvY2hlY2suZG8wNAYIKwYBBQUHMAGGKGh0dHA6Ly8xOTIuMTY4LjAuNjEvb2NzcGNvbnNvbGUvY2hlY2suZG8wCgYIKoEcz1UBg3UDRwAwRAIgKOr2HVjTvdTQQfRyzz5mhlJh9hNni2BT4GhGin+XfpYCICpUl5tpEj3mIEZ8f3YLC8xylWkdckmQtz1HhTolyxPd"
     *    ]}
     * }
     */
    public CertAuthResponsePlain<List> queryForCerts(final String userUid) {
        /*
         * 1.获取用户（userUid）拥有的证书（cert）
         * 2.获取相应证书的二维码
         */
        //
        String url, postData;
        //1.获取AccessToken
        refreshCfg();
        url = String.format(url_cert_search, certAuthProp.getBase());
        postData = String.format(params_cert_serch, userUid, certAuthProp.getFmtProjectUid());//, certauthProjectId
        String resText = httpClientService.ajaxPost(url, postData, charsetUTF8);
        //
        if(logger.isDebugEnabled()) { logger.debug("ca证书={}", resText); }
        return JSON.parseObject(resText, typeRef_array_plain);
    }

    /**
     * `获取证书二维码
     * @return {
     *    "responseResult":    {
     *       "status": 0,
     *       "msg": "成功"
     *    },
     *    "contents":    {
     *       "qrCodeExpireDate": 120,
     *       "qrCodeIdentity": "F2BD5551",
     *       "qrCodes":       [
     *                   {
     *             "type": 3,
     *             "qrCodeBase64": "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",
     *             "qrCodeContent": "netcack3://{\"q\":\"F2BD5551\",\"u\":\"https://106.52.243.222:61443/\",\"l\":16}"
     *          },
     *                   {
     *             "type": 4,
     *             "qrCodeBase64": "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",
     *             "qrCodeContent": "00F2BD5551test.cnca.net:61443"
     *          }
     *       ]
     *    }
     * }
     */
    public CertAuthResponse<Object> queryForQrcode() {
        //2.获取二维码
        refreshCfg();
        String url = String.format(url_qrcode, certAuthProp.getBase());
        String postData = String.format(params_qrcode, certauthQrType
                , certAuthProp.getFmtProjectUid()
                , certAuthProp.getFmtApplicationId());
        if(logger.isDebugEnabled()) { logger.debug("CA二维码请求 url:{},data:{}", url,postData); }
        String resText = httpClientService.ajaxPost(url, postData, charsetUTF8);
        if(logger.isDebugEnabled()) { logger.debug("CA二维码结果 {}", resText); }
        return JSON.parseObject(resText, typeRef_object);
    }

    /**
     * `查询二维码扫码结果
     * @param codeId 二维码唯一标识
     * @return {
     *     "responseResult": {
     *         "status": 0,
     *         "msg": "成功"
     *     },
     *     "contents": {
     *         "verifyStatus": 1,
     *         "userToken": null,
     *         "errorMsg": null
     *     }
     * }
     */
    public CertAuthResponse<Object> queryForQrcodeStatus(final String codeId) {
        //CA接口
        String url, postData;
        //调用接口获取
        refreshCfg();
        url = String.format(url_qrcode_status, certAuthProp.getBase());
        postData = String.format(params_qrcode_status, codeId);
        if(logger.isDebugEnabled()) { logger.debug("CA获取token请求 url:{},data:{}", url,postData); }
        String resText = httpClientService.ajaxPost(url, postData, charsetUTF8);
        //resText = "{\"responseResult\": {\"status\": 0,\"msg\": \"成功\"},\"contents\": {\"verifyStatus\": 0,\"userToken\": \"2B05DCDC6E37EDD1E1D804B091B021D91321\",\"errorMsg\": null}}";
        if(logger.isDebugEnabled()) { logger.debug("CA获取token结果 {}", resText); }
        /*
         * verifyStatus=
         * 0 = 认证成功。
         * 1 = 用户未开始认证，需要继续轮询
         * -1 = 认证失败，有错误信息在errorMsg
         */
        return JSON.parseObject(resText, typeRef_object);
    }

    private void refreshCfg(){
        SysConfig scfg = configMapper.checkConfigKeyUnique(AuthServiceConfigKey);
        JSONObject cfg = JSON.parseObject(scfg.getConfigValue());
        certAuthProp.refresh(cfg.getString("base"), cfg.getString("projectID"), cfg.getString("applicationID"));
    }
    /**
     * `下载签名图片
     * @token token
     */
    public String downloadsealpic(final String token) {
        if(!checkConfig()) {
            logger.error("请配置接口信息: wztsignBase.");
            throw new RuntimeException("获取签名图片异常,请配置接口信息: wztsignBase");
        }

        //取得待认证参数：二维码 qrCodeIdentity
        String userToken = redisCache.getCacheObject(String.format(cacheTokenPattern, token));
        if(null == userToken) {
            //throw new RuntimeException(errorInvalidSession);
            throw new IllegalStateException(QRAuthConst.ERRM_NOAUTH);
        }

        //CA接口
        String url, postData;
        //调用接口获取
        refreshCfg();
        url = String.format(url_seal_downloadsealpic, certAuthProp.getBase());
        postData = String.format(params_seal_downloadsealpic, userToken);
        if(logger.isDebugEnabled()) { logger.debug("CA下载签名图片请求 url:{} data:{}", url,postData); }
        String resText = httpClientService.ajaxPost(url, postData, charsetUTF8);
        //resText = "{\"responseResult\": {\"status\": 0,\"msg\": \"成功\"},\"contents\": {\"verifyStatus\": 0,\"userToken\": \"2B05DCDC6E37EDD1E1D804B091B021D91321\",\"errorMsg\": null}}";
        if(logger.isDebugEnabled()) { logger.debug("CA下载签名图片结果 {}", resText); }
        /*
         * verifyStatus=
         * 0 = 认证成功。
         * 1 = 用户未开始认证，需要继续轮询
         * -1 = 认证失败，有错误信息在errorMsg
         */
        CertAuthResponse<Object> resData = JSON.parseObject(resText, typeRef_object);

        if(-2==resData.getResponseResult().getStatus()){
            throw new IllegalStateException(QRAuthConst.ERRM_NOAUTH);
        }

        Map<String, Object> resDataContents = null != resData? resData.getContents() : null;
        if(null == resDataContents) {
            throw new RuntimeException("获取签名结果异常");
        }
        if(null == resDataContents.get("picBase64")) {
            throw new RuntimeException("没有签名图片");
        }

        return resDataContents.get("picBase64").toString();
    }

    /**
     * `查询证书授权信息
     * @param cert 证书
     * @return {
     *     "responseResult": {
     *         "status": 0,
     *         "msg": "成功"
     *     },
     *     "contents": {
     *         "total": 2,
     *         "currentRowsSize": 2,
     *         "rows": [
     *             {
     *                 "id": 59,
     *                 "name": "1111"
     *             },
     *             {
     *                 "id": 61,
     *                 "name": "cxytesttestesttes"
     *             }
     *         ]
     *     }
     * }
     */
    public CertAuthResponse<Object> queryForAuthedUsers(final String cert) {

        refreshCfg();
        String url = String.format(url_authed_user, certAuthProp.getBase());
        String postData = String.format(params_authed_user, cert);
        String resText = httpClientService.ajaxPost(url, postData, charsetUTF8);
        if(logger.isDebugEnabled()) { logger.debug("CA授权信息 {}", resText); }
        return JSON.parseObject(resText, typeRef_object);
    }

    /**
     * `获取口令或扫码的用户信息
     * @param userToken token
     * @return {
     *    "responseResult":    {
     *       "status": 0,
     *       "msg": "成功"
     *    },
     *    "contents":    {
     *       "name": "张主任",
     *       "uid": "985",
     *       "projectUid": "ZhongshanPeopleHospital-ZSPH",
     *       "phone": "15099955982",
     *       "userType": 0,
     *       "certContentInfo":       {
     *          "signCert": "MIIDXzCCAwagAwIBAgILEOGfWoNWMO5mBAswCgYIKoEcz1UBg3UwYjELMAkGA1UEBhMCQ04xJDAiBgNVBAoMG05FVENBIENlcnRpZmljYXRlIEF1dGhvcml0eTEtMCsGA1UEAwwkTkVUQ0EgU00yIFRFU1QwMSBhbmQgRXZhbHVhdGlvbiBDQTAxMB4XDTIxMTIxNDA5MDgyN1oXDTI0MTIxNDA5MDgyN1owejELMAkGA1UEBhMCQ04xEjAQBgNVBAgMCUd1YW5nZG9uZzEqMCgGA1UEBwwh5bm/5Lic55yB5bm/5bee5biC6LaK56eA5Yy6MTYxMjUzMRcwFQYDVQQKDA7mnLrmnoQoMTYxMjUzKTESMBAGA1UEAwwJ5byg5Li75Lu7MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEXB/rrn07zUtmD3Ca/pWjxHVc+Bn3YwdHeu/olrS4avVtbcamcPZKOip2YXTs7i3KPyJqXYOCGPRU7t6H9UVjRaOCAYkwggGFMAwGA1UdEwEB/wQCMAAwDgYDVR0PAQH/BAQDAgbAMGsGA1UdIARkMGIwYAYKKwYBBAGBkkgNCjBSMFAGCCsGAQUFBwIBFkRodHRwOi8vd3d3LmNuY2EubmV0L2NzL2tub3dsZWRnZS93aGl0ZXBhcGVyL2Nwcy9uZXRDQXRlc3RjZXJ0Y3BzLnBkZjAfBgNVHSMEGDAWgBQMe+ticwN1+oxKJAz2jzshZX4X6TAdBgNVHQ4EFgQUGDtVTk72JMIZrDh59Jb8Q4y75a4wNAYKKwYBBAGBkkgBDgQmDCQ1NjQ1YThkMjZiNDkwY2FlOTgyYTQzMWE0NjkzZDg4MkBTMDIwgYEGCCsGAQUFBwEBBHUwczA7BggrBgEFBQcwAYYvaHR0cDovLzEwNi41Mi4yNDMuMjIyOjYxODAvb2NzcGNvbnNvbGUvY2hlY2suZG8wNAYIKwYBBQUHMAGGKGh0dHA6Ly8xOTIuMTY4LjAuNjEvb2NzcGNvbnNvbGUvY2hlY2suZG8wCgYIKoEcz1UBg3UDRwAwRAIgcc1qMzSx58KRQpqy739qzAhJwc8R2e5CRDw+277ZcDACIBWzNKXXVTBxSr+EwNXSvZFvIWWKoEHIUhwaKnMs2Dc7",
     *          "encCert": "MIIDXzCCAwagAwIBAgILIIi4U33eDe922vUwCgYIKoEcz1UBg3UwYjELMAkGA1UEBhMCQ04xJDAiBgNVBAoMG05FVENBIENlcnRpZmljYXRlIEF1dGhvcml0eTEtMCsGA1UEAwwkTkVUQ0EgU00yIFRFU1QwMSBhbmQgRXZhbHVhdGlvbiBDQTAxMB4XDTIxMTIxNDA5MDgyN1oXDTI0MTIxNDA5MDgyN1owejELMAkGA1UEBhMCQ04xEjAQBgNVBAgMCUd1YW5nZG9uZzEqMCgGA1UEBwwh5bm/5Lic55yB5bm/5bee5biC6LaK56eA5Yy6MTYxMjUzMRcwFQYDVQQKDA7mnLrmnoQoMTYxMjUzKTESMBAGA1UEAwwJ5byg5Li75Lu7MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAERkGw2/C4DJ8PgiBotJ4Xqix/qI2BG1eQQj6eq/vdGoOoTjyQ75LkvQkZfakHxRoCzPAL4r8HEnp9r4aetbpFz6OCAYkwggGFMAwGA1UdEwEB/wQCMAAwDgYDVR0PAQH/BAQDAgM4MGsGA1UdIARkMGIwYAYKKwYBBAGBkkgNCjBSMFAGCCsGAQUFBwIBFkRodHRwOi8vd3d3LmNuY2EubmV0L2NzL2tub3dsZWRnZS93aGl0ZXBhcGVyL2Nwcy9uZXRDQXRlc3RjZXJ0Y3BzLnBkZjAfBgNVHSMEGDAWgBQMe+ticwN1+oxKJAz2jzshZX4X6TAdBgNVHQ4EFgQU/r4BHTekwC0wLzW6j0XNXBqxnNAwNAYKKwYBBAGBkkgBDgQmDCQ1NjQ1YThkMjZiNDkwY2FlOTgyYTQzMWE0NjkzZDg4MkBTMDIwgYEGCCsGAQUFBwEBBHUwczA7BggrBgEFBQcwAYYvaHR0cDovLzEwNi41Mi4yNDMuMjIyOjYxODAvb2NzcGNvbnNvbGUvY2hlY2suZG8wNAYIKwYBBQUHMAGGKGh0dHA6Ly8xOTIuMTY4LjAuNjEvb2NzcGNvbnNvbGUvY2hlY2suZG8wCgYIKoEcz1UBg3UDRwAwRAIgKOr2HVjTvdTQQfRyzz5mhlJh9hNni2BT4GhGin+XfpYCICpUl5tpEj3mIEZ8f3YLC8xylWkdckmQtz1HhTolyxPd"
     *       }
     *    }
     * }
     */
    public CertAuthResponse<Object> getTokenUser(final String userToken) {
        if(!checkConfig()) {
            logger.error("请配置接口信息: wztsignBase.");
            return null;
        }

        //取得待认证参数：二维码 qrCodeIdentity
        String token = redisCache.getCacheObject(String.format(cacheTokenPattern, userToken));
        if(null == token) {
            //throw new RuntimeException(errorInvalidSession);
            throw new IllegalStateException(QRAuthConst.ERRM_NOAUTH);
        }
        refreshCfg();
        String url = String.format(url_oprinfo, certAuthProp.getBase());
        String postData = String.format(params_oprinfo, token);
        String resText = httpClientService.ajaxPost(url, postData, charsetUTF8);
        if(logger.isDebugEnabled()) { logger.debug("CA操作人信息 {}", resText); }
        return JSON.parseObject(resText, typeRef_object);
    }

    /**
     * `口令认证
     * @param userUid Des加密后的用户UID
     * @param pin Des加密后的用户口令
     * @return {
     *     "responseResult": {
     *         "status": -1,
     *         "msg": "该被授权用户不存在"
     *     },
     *     "contents": null
     * }
     */
    public CertAuthResponse<String> getTokenByPin(int userType, String userUid, String pin) {
        if(!checkConfig()) {
            logger.error("请配置接口信息: wztsignBase.");
            return null;
        }
        CertAuthResponse<String> res = new CertAuthResponse<>();
        CertAuthResponseResult resRes = new CertAuthResponseResult();
        res.setResponseResult(resRes);

        if(!StringUtils.isNotBlank(userUid) || !StringUtils.isNotBlank(pin)) {
            resRes.setStatus(CertAuthResponseResult.Status.PARAMSERR.getCode()).setMsg("请提供用户ID和口令.");
            return res;
        }
        //用户UID解密
        userUid = DesUtils.decode(userUid, SECURITY_KEY);
        //口令解密并Base64编码
        pin = DesUtils.decode(pin, SECURITY_KEY);
        try {
            pin = Base64.encodeBase64String(pin.getBytes(charsetUTF8));
        } catch (Exception err) { logger.error(err.toString(), err); }
        //使用的证书
        String cert;
        try {
            cert = affectedCert(userUid, queryForCerts(userUid));
        } catch (Exception err) {
            final String errM = err.toString();
            logger.error(errM, err);
            resRes.setStatus(CertAuthResponseResult.Status.UN.getCode()).setMsg(errM);
            return res;
        }
        //
        boolean isAuthedUser = CAUserType.auth.getType() == userType;
        //调用CA接口
        refreshCfg();
        String url, postData;
        refreshCfg();
        if(!isAuthedUser) {
            url = String.format(url_pin_login, certAuthProp.getBase());
            postData = String.format(params_pin_login, pin, cert);
        } else {
            url = String.format(url_pin_login_authUser, certAuthProp.getBase());
            postData = String.format(params_pin_login_authUser, userUid, pin, cert);
        }
        String resText = httpClientService.ajaxPost(url, postData, charsetUTF8);
        if(logger.isDebugEnabled()) { logger.debug("CA口令认证结果 {}={}", postData, resText); }
        CertAuthResponse<String> pinRes = JSON.parseObject(resText, typeRef_text);
        //返回无token，认证 失败
        if(null == pinRes || null == pinRes.getContents() || null == pinRes.getContents().get("userToken")) {
            //错误
            if(null == pinRes || null == pinRes.getResponseResult()) {
                resRes.setStatus(CertAuthResponseResult.Status.UN.getCode()).setMsg("未知错误.");
                return res;
            }
            //获取口令尝试次数
            refreshCfg();
            if(!isAuthedUser) {
                url = String.format(url_cert_surplus_num, certAuthProp.getBase());
                postData = String.format(params_cert_surplus_num, cert);
            } else {
                url = String.format(url_cert_surplus_num_authUser, certAuthProp.getBase());
                postData = String.format(params_cert_surplus_num_authUser, userUid);
            }
            resText = httpClientService.ajaxPost(url, postData, charsetUTF8);
            if(logger.isDebugEnabled()) { logger.debug("CA口令尝试词素 {}={}", postData, resText); }
            CertAuthResponsePlain<Integer> csnRes = JSON.parseObject(resText, typeRef_int_plain);
            if(null != csnRes && null != csnRes.getContents()) {
                int surplusNum = csnRes.getContents().intValue();
                String attachErrM;
                if(-1 == surplusNum) {
                    attachErrM = "用户口令已锁死";
                } else {
                    attachErrM = "（可尝试错误次数 " + csnRes.getContents() + "）";
                }
                CertAuthResponseResult pinResRes = pinRes.getResponseResult();
                //用户[张主任]口令错误
                pinResRes.setMsg(pinResRes.getMsg() + attachErrM);
            }
        } else {
            //认证成功
            pinRes.getContents().put("certContent", cert);
        }
        //
        return pinRes;
    }

    /**
     * `使用的证书
     * @param userUid 用户UID
     * @param certsRes 证书查询结果
     * @return 签名证书
     */
    public String affectedCert(final String userUid, CertAuthResponsePlain<List> certsRes) {
        if(StringUtils.isNotBlank(userUid) && null != certsRes) {
            List<Map> certs;
            if(null == certsRes || null == (certs = certsRes.getContents()) || certs.isEmpty()) {
                throw new RuntimeException("无证书数据: " +  certsRes);
            }
            //[签名证书, 加密证书]
            return (String)((Map)certs.get(0)).get("signCert");
        }

        return null;
    }

    /**
     * `生成会话
     * @param sessionId 会话ID
     * @return SSO用户
     */
    public LoginUser makeSession(final String sessionId, final String token,Boolean forRel) {

        String _sessionId = sessionId, _ver = sessionTailZero;
        if(_sessionId.endsWith(_ver)) {
            _sessionId = _sessionId.substring(0, _sessionId.length() - _ver.length());
        }
        //读取授权信息
        CertAuthResponse<Object> resUser = getTokenUser(token);
        Map<String, Object> certUser = null != resUser? resUser.getContents() : null;
        if(null == certUser) {
            throw new IllegalStateException("无法获取操作人信息.");
        }
        //缓存到Redis
        SysUser user = new SysUser();
        user.setUserName(certUser.get("uid").toString());
        user.setNickName((String)certUser.get("name"));
        //xxlUser.setUsercode(_sessionId);//authCode.getUserUid()
        ///xxlUser.setVersion(zero);

        SysUser user0 = userService.selectUserByUserName(user.getUserName());
        if(null == user0) {
            throw new IllegalArgumentException("当前系统不存在该用户信息");
//            SysConfig scfg = configMapper.checkConfigKeyUnique("sys.user.initPassword");
//            if(null != scfg) {
//                user.setPassword(scfg.getConfigValue());
//            }
//            userService.insertUser(user);
        } else {
            user.setUserId(user0.getUserId());
            if(null != user0.getDept()) {
                user.setDept(user0.getDept());
                user.setDeptId(user0.getDeptId());
            }
            if(null != user0.getPosts()) {
                user.setPostIds(user0.getPostIds());
                user.setPosts(user0.getPosts());
            }
            if(null != user0.getRoles()) {
                user.setRoleId(user0.getRoleId());
                user.setRoleIds(user0.getRoleIds());
                user.setRoles(user0.getRoles());
            }
        }

        LoginUser luser = null;

        //缓存到Redis
        luser = new LoginUser();
        //luser.getDeptId()
        luser.setLoginTime(System.currentTimeMillis());
        //授权有效时长，秒
        int expireTime = expiredTime;
        luser.setExpireTime(System.currentTimeMillis() + (1000L * expireTime));
        //
        luser.setUserId(user.getUserId());
        luser.setUser(user);
        luser.setPermissions(permissionService.getMenuPermission(user));
        String tokenU = tokenService.createToken(luser);//jwt
        //更换cache sessionId
        final String origToken = luser.getToken();
        //
        if(!forRel) {
            //临时修改，前端使用jwt
            luser.setToken(tokenU);//sessionId
        }
        //医信签token
        //luser.setLoginLocation(sessionId);
        //tokenService.setUserAgent(luser);
        //tokenService.refreshToken(luser);

        //更换cache sessionId
        final String origCacheKey = String.format(cacheKeyPattern, sessionId);
        CertAuthCode authCode = redisCache.getCacheObject(origCacheKey);
        cacheAccessToken(origToken, authCode);
        redisCache.deleteObject(origCacheKey);

        return luser;
    }

    /**
     * 将Token相关信息放到redis，以后网证通的调用参数从这里取
     * @param sessionId 开始前端生成传入，扫码认证后由后端生成
     */
    protected void cacheAccessToken(String sessionId, CertAuthCode certAuthCode) {
        redisCache.setCacheObject(String.format(cacheKeyPattern, sessionId), certAuthCode, 5 * 60 * 60, TimeUnit.SECONDS);
    }

    /**
     * `给定sessionId授权状态
     * @param sessionId 用于获取用户信息token
     * @param forRel 用于常规账号+扫码登录
     * @return
     */
    public LoginUser authcheck(String sessionId, Boolean forRel) {

        if(logger.isDebugEnabled()) { logger.debug("sessionId={}", sessionId); }
        if(StringUtils.isBlank(sessionId)) {
            //logger.warn("医信签授权检查登录错误: 无法获取sessionId");
            throw new RuntimeException("缺失session凭证。");
        }

        LoginUser luser = null;
        //是否已登录
        if(!forRel) {
            try {
                luser = SecurityUtils.getLoginUser();
            } catch (Exception err) {
                logger.error(err.getMessage(), err);
            }
        }
        //redis是否有签名信息
        if(null != luser) {
            if(luser.getExpireTime() < System.currentTimeMillis()) {
                if(logger.isDebugEnabled()) { logger.debug("医信签授权过期 {}, {}, {}"
                        , luser.getUserId(), luser.getToken(), luser.getExpireTime()); }
                redisCache.deleteObject(String.format(cacheKeyPattern, sessionId));
                throw new RuntimeException(ERRM_sessionExpired);
            }
        } else {
            try {
                luser = getOauthStatus(sessionId, forRel);
                if (null == luser) {
                    if (null == redisCache.getCacheObject(String.format(cacheKeyPattern, sessionId))) {
                        throw new RuntimeException(ERRM_sessionInvalid);
                    }
                    //logger.warn("医信签授权检查登录错误: 无法获取身份信息.");
                    //throw new RuntimeException("无认证信息。");
                    return null;
                }
                return luser;
            } catch (Exception err) {
                logger.error(err.getMessage(), err);
                throw err;
            }
        }

        return luser;
    }

    public AjaxResult pinLogin(String userType,String userUid,String pin) {
        CertAuthResponse<String> res = getTokenByPin(Integer.parseInt(userType), userUid, pin);
        CertAuthResponseResult resRes;
        if(null != res && null != (resRes = res.getResponseResult())) {
            if(resRes.getStatus() == CertAuthResponseResult.Status.SUCCESS.getCode()) {
                Map<String, String> resContents = res.getContents();
                //sessionId：CA::PIN.{{token}}_0
                final String userToken = resContents.get("userToken");
                String sessionId = String.format(QRAuthConst.sessionTpl, QRAuthConst.sessionLeadCAPin, userToken);
                //缓存token
                LoginUser luser = SecurityUtils.getLoginUser();
                redisCache.setCacheObject(String.format(cacheTokenPattern, luser.getToken()), userToken, expiredTime * 60, TimeUnit.SECONDS);//expiredTime * 1000
                //生成会话
                makeSession(sessionId, luser.getToken(),true);

                AjaxResult ajaxResult = AjaxResult.success();

                return ajaxResult.put(AjaxResult.DATA_TAG, sessionId);
            }
            return AjaxResult.error(40100, resRes.getMsg());
        }

        return AjaxResult.error(500, "认证错误！");
    }

    /**
     * `获取授权状态
     * @param sessionId
     * @return
     */
    private LoginUser getOauthStatus(String sessionId, Boolean forRel) {
        if(!checkConfig()) {
            logger.error("请配置接口信息: wztsignBase.");
            return null;
        }
        //取得待认证参数：二维码 qrCodeIdentity
        CertAuthCode authCode = (CertAuthCode)redisCache.getCacheObject(String.format(cacheKeyPattern, sessionId));;
        if(null == authCode) {
            //throw new RuntimeException(errorInvalidSession);
            return null;
        }
        //CA接口
        CertAuthResponse<Object> resData = queryForQrcodeStatus(authCode.getId());
        Map<String, Object> resDataContents = null != resData? resData.getContents() : null;
        if(null == resDataContents) {
            return null;
        }
        Object val = resDataContents.get("verifyStatus");
        int authStatus = null != val? NumberUtils.parseNumber(String.valueOf(val), Integer.class) : invalidStatus;
        //
        if(0 != authStatus) {
            return null;
        }

        //缓存扫描token
        final String userToken = (String)resDataContents.get("userToken");
        LoginUser luser = SecurityUtils.getLoginUser();
        redisCache.setCacheObject(String.format(cacheTokenPattern, luser.getToken()), userToken, expiredTime * 60, TimeUnit.SECONDS);//expiredTime * 1000
        return makeSession(sessionId, luser.getToken(),forRel);
    }
}
