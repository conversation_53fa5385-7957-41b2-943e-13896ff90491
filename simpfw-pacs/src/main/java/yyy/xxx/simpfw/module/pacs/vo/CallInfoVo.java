package yyy.xxx.simpfw.module.pacs.vo;

import yyy.xxx.simpfw.module.pacs.entity.CallInfo;

import java.util.List;

public class CallInfoVo extends CallInfo {
    //过号
    public static final int STATUS_PAST = 3;
    //是否过号
    public static boolean isPast(CallInfo item) {
        return null != item.getStatus() && STATUS_PAST == item.getStatus().intValue();
    }
    //查询指定工作状态
    private List<String> resultStatusCodes;
	public List<String> getResultStatusCodes() {
		return resultStatusCodes;
	}
	public void setResultStatusCodes(List<String> resultStatusCodes) {
		this.resultStatusCodes = resultStatusCodes;
	}
    
    //查询指定检查项目
    private List<String> examItemCodes;
    public List<String> getExamItemCodes() {
        return examItemCodes;
    }
    public void setExamItemCodes(List<String> examItemCodes) {
        this.examItemCodes = examItemCodes;
    }
    //是否已呼叫
    private Short callStatus;
    public Short getCallStatus() {
        return callStatus;
    }
    public void setCallStatus(Short callStatus) {
        this.callStatus = callStatus;
    }

    /**
     * 检查类型
     */
    private List<String> examModalitiesCodes;
    public List<String> getExamModalitiesCodes() {
        return examModalitiesCodes;
    }
    public void setExamModalitiesCodes(List<String> examModalitiesCode) {
        this.examModalitiesCodes = examModalitiesCode;
    }
}
