package yyy.xxx.simpfw.framework.auth.web;

import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import yyy.xxx.simpfw.common.core.domain.AjaxResult;
import yyy.xxx.simpfw.common.core.domain.model.LoginUser;
import yyy.xxx.simpfw.common.utils.SecurityUtils;
import yyy.xxx.simpfw.framework.auth.QRAuthConst;
import yyy.xxx.simpfw.framework.auth.bo.CertAuthResponse;
import yyy.xxx.simpfw.framework.auth.bo.CertAuthResponseResult;
import yyy.xxx.simpfw.framework.auth.service.CertAuthService;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * `CA口令认证
 */
@Controller
@RequestMapping(value = "/ca")
public class CertAuthController {

    @Autowired
    private CertAuthService service;

    @RequestMapping(value = "/pinLogin")
    @ResponseBody
    public AjaxResult pinLogin(@RequestParam("userType") String userType
            , @RequestParam("username") String userUid
            , @RequestParam("password") String pin) {
        return service.pinLogin(userType,userUid,pin);
    }
}
