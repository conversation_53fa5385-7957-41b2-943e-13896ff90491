<template>
  <el-form label-width="100px" class="tight-form exam-view-form">
    <el-row style="height: 50%; width: 100%">  
      <el-form-item label="检查所见" class="hei100 fhta">
        <el-input name="examDiagnosis" v-model="examInfo.examDiagnosis" readonly type="textarea" :rows="6"/>
      </el-form-item>
    </el-row>

  <el-row style="height: 50%; width: 100%">  
    <el-form-item label="检查诊断" class="hei100 fhta">
      <el-input name="examDiagnosis" v-model="examInfo.examDiagnosis"  readonly type="textarea" :rows="6"/>
    </el-form-item>
  </el-row>

  </el-form>

</template>

<style scoped src="@/assets/styles/pacs/exammanagement/patient/Regist.css"></style>

<script>

import * as eiapi from "@/assets/scripts/uis/exammanagement/examinfo/api";

const model = {
  name: "ReportViewer",

  data() {
    return {
      examInfo: {}
    }
  },

  methods: {
    view(exam) {
      if (exam == undefined){
        // this.$modal.msg("请选择一个患者");
        return;
      }
      eiapi.get(exam.id).then(res => {
        this.examInfo = res.data;
      })
    }
  }
};
export default model;
</script>

<style scoped>
.exam-view-form >>> .el-form-item__content{
  font-weight: bold;
}
</style>