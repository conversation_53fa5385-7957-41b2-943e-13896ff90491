package yyy.xxx.simpfw.module.uis.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import yyy.xxx.simpfw.common.annotation.DataSource;
import yyy.xxx.simpfw.common.enums.DataSourceType;
import yyy.xxx.simpfw.common.utils.SecurityUtils;
import yyy.xxx.simpfw.module.uis.entity.CriticalValues;
import yyy.xxx.simpfw.module.uis.mapper.CriticalValuesMapper;
import yyy.xxx.simpfw.module.uis.service.CriticalValuesService;
import yyy.xxx.simpfw.module.uis.vo.CriticalValuesVo;

@Service
@DataSource(value = DataSourceType.SLAVE)
public class CriticalValuesServiceImpl implements CriticalValuesService {

    @Autowired
    private CriticalValuesMapper mapper;

    public int insertOrUpdate(CriticalValues entity) {
        //
        if(!entity.isNew()) {
            return mapper.update(entity);
        }
        entity.setSubmitDoctor(SecurityUtils.getLoginUser().getUser());
        return mapper.insert(entity);
    }

    @Override
    public CriticalValuesVo selectOne(CriticalValues param) {
        return mapper.selectOne(param);
    }

    @Override
    public List<CriticalValuesVo> selectList(CriticalValues param) {
        return mapper.selectList(param);
    }

    public int delete(Long id) {
        return mapper.delete(id);
    }
}
