package yyy.xxx.simpfw.module.pacs.ws;

import com.sun.xml.internal.bind.marshaller.CharacterEscapeHandler;
import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;
import java.io.Writer;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

public class JAXB {

    /**
     *
     */
    public static String string(Object root, Class clazz, String encoding) throws JAXBException {
        StringWriter writer = new StringWriter();
        createMarshaller(clazz, encoding).marshal(root, writer);
        return writer.toString();
    }
    /**
     *
     */
    @SuppressWarnings("unchecked")
    public static <T> T parse(String xml, Class<T> clazz) throws JAXBException {
        StringReader reader = new StringReader(xml);
        return (T) createUnmarshaller(clazz).unmarshal(reader);
    }
    /**
     * 创建Marshaller
     */
    public static Marshaller createMarshaller(Class clazz, String encoding) throws JAXBException {
        JAXBContext jaxbContext = getJaxbContext(clazz);

        Marshaller marshaller = jaxbContext.createMarshaller();

        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);

        if (StringUtils.isNotBlank(encoding)) {
            marshaller.setProperty(Marshaller.JAXB_ENCODING, encoding);
        }

        marshaller.setProperty(CharacterEscapeHandler.class.getName(),
                new CharacterEscapeHandler() {
                    @Override
                    public void escape(char[] ac, int i, int j, boolean flag,
                                       Writer writer) throws IOException {
                        writer.write(ac, i, j);
                    }
                });
        return marshaller;
    }

    /**
     * 创建UnMarshaller.
     */
    public static Unmarshaller createUnmarshaller(Class clazz) throws JAXBException {
        JAXBContext jaxbContext = getJaxbContext(clazz);
        return jaxbContext.createUnmarshaller();
    }

    private static ConcurrentMap<Class, JAXBContext> jaxbContexts = new ConcurrentHashMap<Class, JAXBContext>();

    protected static JAXBContext getJaxbContext(Class clazz) throws JAXBException {
        if (clazz == null){
            throw new RuntimeException("'clazz' must not be null");
        }
        JAXBContext jaxbContext = jaxbContexts.get(clazz);
        if (jaxbContext == null) {
            jaxbContext = JAXBContext.newInstance(clazz);
            jaxbContexts.putIfAbsent(clazz, jaxbContext);
        }
        return jaxbContext;
    }

}
