USE pacs_mt;
ALTER TABLE d_dicom_info
    CHANGE COLUMN dataSource data_source_dict_value varchar(100) NULL DEFAULT NULL;

ALTER TABLE d_dicom_info
    ADD COLUMN device_no VARCHAR(32) COMMENT '设备编码';

UPDATE d_dicom_info
SET device_no = modality_code
WHERE device_no IS NULL;

ALTER TABLE d_dicom_info
    MODIFY COLUMN device_no VARCHAR(32) NOT NULL COMMENT '设备编码',
    ADD UNIQUE INDEX idx_device_no_unique (device_no);

CREATE TABLE r_dicom_info_exam_item_ref
(
    object_id_1  bigint                             not null comment '检查设备表主键ID',
    object_id_2  bigint                             not null comment '字典表检查项目主键dict_code',
    create_time  datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    constraint r_dicom_info_exam_item_ref_pk
        unique (object_id_1, object_id_2)
)
    comment '检查设备检查项目关系表';

alter table d_exam_file
       add data_source_dict_value varchar(100) NULL DEFAULT NULL comment '数据源字典value',
       add device_no VARCHAR(32) NULL DEFAULT NULL comment '设备编码';