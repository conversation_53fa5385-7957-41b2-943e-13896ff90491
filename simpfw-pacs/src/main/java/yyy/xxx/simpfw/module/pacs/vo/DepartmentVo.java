package yyy.xxx.simpfw.module.pacs.vo;

import yyy.xxx.simpfw.module.pacs.entity.Department;
import yyy.xxx.simpfw.module.pacs.entity.Region;
import yyy.xxx.simpfw.common.core.domain.TreeSelect;

public class DepartmentVo extends Department {
    public TreeSelect convTreeNode() {
        TreeSelect node = new TreeSelect();
        node.setId(getId());
        node.setLabel(getDeptName());
        node.setData(this);

        return node;
    }
}
