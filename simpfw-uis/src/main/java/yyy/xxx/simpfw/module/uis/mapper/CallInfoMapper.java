package yyy.xxx.simpfw.module.uis.mapper;

import org.apache.ibatis.annotations.Param;
import yyy.xxx.simpfw.module.pacs.entity.CallInfo;
import yyy.xxx.simpfw.module.pacs.vo.CallInfoVo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface CallInfoMapper {
    CallInfoVo selectOne(CallInfo param);

    List<CallInfoVo> selectList(CallInfo param);

    int insert(CallInfo entity);

    int update(CallInfo entity);

    int delete(@Param("id") Long id);

    CallInfoVo selectByExam(@Param("examId") Long examId);

    List<String> selectByRules(@Param("registDate") Date registDate, @Param("appointDate") Date appointDate
            , @Param("reservedNos") Collection<String> nos
            , @Param("examModality") String examModality
            , @Param("examItem") String examItem);

    String selectLastByRules(@Param("registDate") Date registDate, @Param("appointDate") Date appointDate
            , @Param("reservedNos") Collection<String> nos
            , @Param("examModality") String examModality
            , @Param("examItem") String examItem);

    int past(CallInfo entity);

    int changeEquipRoom(@Param("fromId") Long fromId
            , @Param("fromEquipRoomCode") String fromEquipRoomCode
            , @Param("toEquipRoomCode") String toEquipRoomCode);
}
