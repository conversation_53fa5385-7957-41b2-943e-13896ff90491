ALTER TABLE `rhpacs`.`d_template`
    ADD COLUMN `template_type` tinyint NULL COMMENT '模板类型(pacs_template_type)' AFTER `template_name`;

INSERT INTO `cmb`.`sys_dict_type`( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '模板类型', 'pacs_template_type', '0', 'admin', '2023-04-13 10:58:34', '', NULL, NULL);

INSERT INTO `cmb`.`sys_dict_data`( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '普通模板', '1', 'pacs_template_type', 0, NULL, 'default', 'N', '0', 'admin', '2023-04-13 10:58:57', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '整体模板', '2', 'pacs_template_type', 0, NULL, 'default', 'N', '0', 'admin', '2023-04-13 11:00:55', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

