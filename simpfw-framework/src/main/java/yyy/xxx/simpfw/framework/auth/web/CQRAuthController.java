package yyy.xxx.simpfw.framework.auth.web;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.NumberUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import yyy.xxx.simpfw.common.core.domain.AjaxResult;
import yyy.xxx.simpfw.common.core.domain.model.LoginUser;
import yyy.xxx.simpfw.common.utils.StringUtils;
import yyy.xxx.simpfw.framework.auth.QRAuthConst;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;

import org.apache.commons.codec.binary.Base64;

import yyy.xxx.simpfw.common.core.redis.RedisCache;
import yyy.xxx.simpfw.framework.auth.bo.CertAuthCode;
import yyy.xxx.simpfw.framework.auth.bo.CertAuthResponse;
import yyy.xxx.simpfw.framework.auth.service.CertAuthService;

/**
 * `CA扫码认证
 */
@Controller
@RequestMapping(value = "/cqrauth")
public class CQRAuthController implements QRAuthConst {

    private static final Logger logger = LoggerFactory.getLogger(CQRAuthController.class);

    private static final String paramSessionId = "sessionId", paramUUID = "uuid";

    @Autowired
    private CertAuthService service;

	@Autowired protected RedisCache redisCache;

    /**
     * `获取二维码
     * @param request 请求
     */
    @RequestMapping(value = "/code")
	@ResponseBody
    public AjaxResult code(HttpServletRequest request) {
    	if(!service.checkConfig()) {
    		logger.error("请配置接口信息: wztsignBase.");
			return AjaxResult.error(500, "不支持扫码认证。");
    	}
    	String sessionId = request.getParameter(paramSessionId);
        if(!StringUtils.isNotBlank(sessionId)) {
            logger.error("请提供SessionId.");
			return AjaxResult.error(500, "状态错误，请重试。");
        }
    	//获取二维码
        CertAuthResponse<Object> qrResp = service.queryForQrcode();
        Map<String, Object> qrContents = null != qrResp? qrResp.getContents() : null;
        List qrCodes = null != qrContents? (List)qrContents.get("qrCodes") : null;
        if(null == qrCodes || qrCodes.isEmpty()) {
        	logger.warn("CA二维码出错: {}", qrResp);
			return AjaxResult.error(500, "无法获取二维码。");
        }

		//返回需要的信息
		String[] reservedProps = new String[]{"type", "qrCodeBase64"};
		List qrCodes0 = new ArrayList();

		for(Object o : qrCodes) {
			Map<String, Object> code = (Map<String, Object>)o, code0 = new HashMap<>();
			for(String prop : reservedProps) {
				code0.put(prop, code.get(prop));
			}
			qrCodes0.add(code0);
		}


		//用于轮询扫码结果
		try {
			String codeId = (String)qrContents.get("qrCodeIdentity");
			int expireDate= NumberUtils.parseNumber(String.valueOf(qrContents.get("qrCodeExpireDate")), Integer.class);
			CertAuthCode authCode = new CertAuthCode(codeId, null, (System.currentTimeMillis() + 1000 * expireDate));
			redisCache.setCacheObject(String.format(cacheKeyPattern, sessionId), authCode, expiredTime * 60, TimeUnit.SECONDS);//expireDate
		} catch (Exception err) {
			logger.error(err.toString(), err);
		}


		AjaxResult res = AjaxResult.success();

		Map<String, Object> code0  = (Map<String, Object>)qrCodes0.get(0);
		String codeData = code0.get("qrCodeBase64").toString();
		res.put(AjaxResult.DATA_TAG, Base64.decodeBase64(codeData));

		return res;
    }
    /**
     * `给定sessionId授权状态
     * @param request
     * @return
     */
    @RequestMapping(value = "/authcheck")
    @ResponseBody
    public AjaxResult authcheck(HttpServletRequest request) {

    	String sessionId = request.getParameter(paramSessionId);
		String forRel = request.getParameter("forRel");

		LoginUser luser;
		try {
			luser = service.authcheck(sessionId, "true".equals(forRel));
		} catch (Exception err) {
			logger.error(err.getMessage(), err);
			return AjaxResult.error(err.getMessage());
		}

		AjaxResult ret = AjaxResult.success();
		if(null != luser) {
			ret.put("token", luser.getToken());
		}
		return ret;
    }

    /**
     * `检查配置
     * @return
     */
    @RequestMapping(value = "/cfg")
    @ResponseBody
    public AjaxResult config(HttpServletRequest request) {
		try {
			if (!service.checkConfig()) {
				return AjaxResult.error();
			}

			return AjaxResult.success();
		} catch (Exception err) {
			return AjaxResult.error(err.getMessage());
		}
    }
}