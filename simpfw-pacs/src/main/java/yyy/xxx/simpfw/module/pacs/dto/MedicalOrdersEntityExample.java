package yyy.xxx.simpfw.module.pacs.dto;

import lombok.Data;
import yyy.xxx.simpfw.module.pacs.constants.TableMetadata;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

@Data
public class MedicalOrdersEntityExample {

    private String tableName = TableMetadata.D_MEDICAL_ORDERS_TABLE_NAME;
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table d_medical_orders
     *
     * @mbg.generated Wed Dec 25 09:52:34 CST 2024
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table d_medical_orders
     *
     * @mbg.generated Wed Dec 25 09:52:34 CST 2024
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table d_medical_orders
     *
     * @mbg.generated Wed Dec 25 09:52:34 CST 2024
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table d_medical_orders
     *
     * @mbg.generated Wed Dec 25 09:52:34 CST 2024
     */
    public MedicalOrdersEntityExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table d_medical_orders
     *
     * @mbg.generated Wed Dec 25 09:52:34 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table d_medical_orders
     *
     * @mbg.generated Wed Dec 25 09:52:34 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table d_medical_orders
     *
     * @mbg.generated Wed Dec 25 09:52:34 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table d_medical_orders
     *
     * @mbg.generated Wed Dec 25 09:52:34 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table d_medical_orders
     *
     * @mbg.generated Wed Dec 25 09:52:34 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table d_medical_orders
     *
     * @mbg.generated Wed Dec 25 09:52:34 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table d_medical_orders
     *
     * @mbg.generated Wed Dec 25 09:52:34 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table d_medical_orders
     *
     * @mbg.generated Wed Dec 25 09:52:34 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table d_medical_orders
     *
     * @mbg.generated Wed Dec 25 09:52:34 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table d_medical_orders
     *
     * @mbg.generated Wed Dec 25 09:52:34 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table d_medical_orders
     *
     * @mbg.generated Wed Dec 25 09:52:34 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        protected void addCriterionForJDBCTime(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Time(value.getTime()), property);
        }

        protected void addCriterionForJDBCTime(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Time> timeList = new ArrayList<>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                timeList.add(new java.sql.Time(iter.next().getTime()));
            }
            addCriterion(condition, timeList, property);
        }

        protected void addCriterionForJDBCTime(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Time(value1.getTime()), new java.sql.Time(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("ID is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("ID is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("ID =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("ID <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("ID >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ID >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("ID <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("ID <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("ID in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("ID not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("ID between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("ID not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andHospitalcodeIsNull() {
            addCriterion("HospitalCode is null");
            return (Criteria) this;
        }

        public Criteria andHospitalcodeIsNotNull() {
            addCriterion("HospitalCode is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalcodeEqualTo(String value) {
            addCriterion("HospitalCode =", value, "hospitalcode");
            return (Criteria) this;
        }

        public Criteria andHospitalcodeNotEqualTo(String value) {
            addCriterion("HospitalCode <>", value, "hospitalcode");
            return (Criteria) this;
        }

        public Criteria andHospitalcodeGreaterThan(String value) {
            addCriterion("HospitalCode >", value, "hospitalcode");
            return (Criteria) this;
        }

        public Criteria andHospitalcodeGreaterThanOrEqualTo(String value) {
            addCriterion("HospitalCode >=", value, "hospitalcode");
            return (Criteria) this;
        }

        public Criteria andHospitalcodeLessThan(String value) {
            addCriterion("HospitalCode <", value, "hospitalcode");
            return (Criteria) this;
        }

        public Criteria andHospitalcodeLessThanOrEqualTo(String value) {
            addCriterion("HospitalCode <=", value, "hospitalcode");
            return (Criteria) this;
        }

        public Criteria andHospitalcodeLike(String value) {
            addCriterion("HospitalCode like", value, "hospitalcode");
            return (Criteria) this;
        }

        public Criteria andHospitalcodeNotLike(String value) {
            addCriterion("HospitalCode not like", value, "hospitalcode");
            return (Criteria) this;
        }

        public Criteria andHospitalcodeIn(List<String> values) {
            addCriterion("HospitalCode in", values, "hospitalcode");
            return (Criteria) this;
        }

        public Criteria andHospitalcodeNotIn(List<String> values) {
            addCriterion("HospitalCode not in", values, "hospitalcode");
            return (Criteria) this;
        }

        public Criteria andHospitalcodeBetween(String value1, String value2) {
            addCriterion("HospitalCode between", value1, value2, "hospitalcode");
            return (Criteria) this;
        }

        public Criteria andHospitalcodeNotBetween(String value1, String value2) {
            addCriterion("HospitalCode not between", value1, value2, "hospitalcode");
            return (Criteria) this;
        }

        public Criteria andPatpatientidIsNull() {
            addCriterion("PATPatientID is null");
            return (Criteria) this;
        }

        public Criteria andPatpatientidIsNotNull() {
            addCriterion("PATPatientID is not null");
            return (Criteria) this;
        }

        public Criteria andPatpatientidEqualTo(String value) {
            addCriterion("PATPatientID =", value, "patpatientid");
            return (Criteria) this;
        }

        public Criteria andPatpatientidNotEqualTo(String value) {
            addCriterion("PATPatientID <>", value, "patpatientid");
            return (Criteria) this;
        }

        public Criteria andPatpatientidGreaterThan(String value) {
            addCriterion("PATPatientID >", value, "patpatientid");
            return (Criteria) this;
        }

        public Criteria andPatpatientidGreaterThanOrEqualTo(String value) {
            addCriterion("PATPatientID >=", value, "patpatientid");
            return (Criteria) this;
        }

        public Criteria andPatpatientidLessThan(String value) {
            addCriterion("PATPatientID <", value, "patpatientid");
            return (Criteria) this;
        }

        public Criteria andPatpatientidLessThanOrEqualTo(String value) {
            addCriterion("PATPatientID <=", value, "patpatientid");
            return (Criteria) this;
        }

        public Criteria andPatpatientidLike(String value) {
            addCriterion("PATPatientID like", value, "patpatientid");
            return (Criteria) this;
        }

        public Criteria andPatpatientidNotLike(String value) {
            addCriterion("PATPatientID not like", value, "patpatientid");
            return (Criteria) this;
        }

        public Criteria andPatpatientidIn(List<String> values) {
            addCriterion("PATPatientID in", values, "patpatientid");
            return (Criteria) this;
        }

        public Criteria andPatpatientidNotIn(List<String> values) {
            addCriterion("PATPatientID not in", values, "patpatientid");
            return (Criteria) this;
        }

        public Criteria andPatpatientidBetween(String value1, String value2) {
            addCriterion("PATPatientID between", value1, value2, "patpatientid");
            return (Criteria) this;
        }

        public Criteria andPatpatientidNotBetween(String value1, String value2) {
            addCriterion("PATPatientID not between", value1, value2, "patpatientid");
            return (Criteria) this;
        }

        public Criteria andPatdocumentnoIsNull() {
            addCriterion("PATDocumentNo is null");
            return (Criteria) this;
        }

        public Criteria andPatdocumentnoIsNotNull() {
            addCriterion("PATDocumentNo is not null");
            return (Criteria) this;
        }

        public Criteria andPatdocumentnoEqualTo(String value) {
            addCriterion("PATDocumentNo =", value, "patdocumentno");
            return (Criteria) this;
        }

        public Criteria andPatdocumentnoNotEqualTo(String value) {
            addCriterion("PATDocumentNo <>", value, "patdocumentno");
            return (Criteria) this;
        }

        public Criteria andPatdocumentnoGreaterThan(String value) {
            addCriterion("PATDocumentNo >", value, "patdocumentno");
            return (Criteria) this;
        }

        public Criteria andPatdocumentnoGreaterThanOrEqualTo(String value) {
            addCriterion("PATDocumentNo >=", value, "patdocumentno");
            return (Criteria) this;
        }

        public Criteria andPatdocumentnoLessThan(String value) {
            addCriterion("PATDocumentNo <", value, "patdocumentno");
            return (Criteria) this;
        }

        public Criteria andPatdocumentnoLessThanOrEqualTo(String value) {
            addCriterion("PATDocumentNo <=", value, "patdocumentno");
            return (Criteria) this;
        }

        public Criteria andPatdocumentnoLike(String value) {
            addCriterion("PATDocumentNo like", value, "patdocumentno");
            return (Criteria) this;
        }

        public Criteria andPatdocumentnoNotLike(String value) {
            addCriterion("PATDocumentNo not like", value, "patdocumentno");
            return (Criteria) this;
        }

        public Criteria andPatdocumentnoIn(List<String> values) {
            addCriterion("PATDocumentNo in", values, "patdocumentno");
            return (Criteria) this;
        }

        public Criteria andPatdocumentnoNotIn(List<String> values) {
            addCriterion("PATDocumentNo not in", values, "patdocumentno");
            return (Criteria) this;
        }

        public Criteria andPatdocumentnoBetween(String value1, String value2) {
            addCriterion("PATDocumentNo between", value1, value2, "patdocumentno");
            return (Criteria) this;
        }

        public Criteria andPatdocumentnoNotBetween(String value1, String value2) {
            addCriterion("PATDocumentNo not between", value1, value2, "patdocumentno");
            return (Criteria) this;
        }

        public Criteria andPathealthcardidIsNull() {
            addCriterion("PATHealthCardID is null");
            return (Criteria) this;
        }

        public Criteria andPathealthcardidIsNotNull() {
            addCriterion("PATHealthCardID is not null");
            return (Criteria) this;
        }

        public Criteria andPathealthcardidEqualTo(String value) {
            addCriterion("PATHealthCardID =", value, "pathealthcardid");
            return (Criteria) this;
        }

        public Criteria andPathealthcardidNotEqualTo(String value) {
            addCriterion("PATHealthCardID <>", value, "pathealthcardid");
            return (Criteria) this;
        }

        public Criteria andPathealthcardidGreaterThan(String value) {
            addCriterion("PATHealthCardID >", value, "pathealthcardid");
            return (Criteria) this;
        }

        public Criteria andPathealthcardidGreaterThanOrEqualTo(String value) {
            addCriterion("PATHealthCardID >=", value, "pathealthcardid");
            return (Criteria) this;
        }

        public Criteria andPathealthcardidLessThan(String value) {
            addCriterion("PATHealthCardID <", value, "pathealthcardid");
            return (Criteria) this;
        }

        public Criteria andPathealthcardidLessThanOrEqualTo(String value) {
            addCriterion("PATHealthCardID <=", value, "pathealthcardid");
            return (Criteria) this;
        }

        public Criteria andPathealthcardidLike(String value) {
            addCriterion("PATHealthCardID_reverse LIKE REVERSE('%" + value + "')");
            return (Criteria) this;
        }

        public Criteria andPathealthcardidNotLike(String value) {
            addCriterion("PATHealthCardID not like", value, "pathealthcardid");
            return (Criteria) this;
        }

        public Criteria andPathealthcardidIn(List<String> values) {
            addCriterion("PATHealthCardID in", values, "pathealthcardid");
            return (Criteria) this;
        }

        public Criteria andPathealthcardidNotIn(List<String> values) {
            addCriterion("PATHealthCardID not in", values, "pathealthcardid");
            return (Criteria) this;
        }

        public Criteria andPathealthcardidBetween(String value1, String value2) {
            addCriterion("PATHealthCardID between", value1, value2, "pathealthcardid");
            return (Criteria) this;
        }

        public Criteria andPathealthcardidNotBetween(String value1, String value2) {
            addCriterion("PATHealthCardID not between", value1, value2, "pathealthcardid");
            return (Criteria) this;
        }

        public Criteria andPatcardtypeIsNull() {
            addCriterion("PATCardType is null");
            return (Criteria) this;
        }

        public Criteria andPatcardtypeIsNotNull() {
            addCriterion("PATCardType is not null");
            return (Criteria) this;
        }

        public Criteria andPatcardtypeEqualTo(String value) {
            addCriterion("PATCardType =", value, "patcardtype");
            return (Criteria) this;
        }

        public Criteria andPatcardtypeNotEqualTo(String value) {
            addCriterion("PATCardType <>", value, "patcardtype");
            return (Criteria) this;
        }

        public Criteria andPatcardtypeGreaterThan(String value) {
            addCriterion("PATCardType >", value, "patcardtype");
            return (Criteria) this;
        }

        public Criteria andPatcardtypeGreaterThanOrEqualTo(String value) {
            addCriterion("PATCardType >=", value, "patcardtype");
            return (Criteria) this;
        }

        public Criteria andPatcardtypeLessThan(String value) {
            addCriterion("PATCardType <", value, "patcardtype");
            return (Criteria) this;
        }

        public Criteria andPatcardtypeLessThanOrEqualTo(String value) {
            addCriterion("PATCardType <=", value, "patcardtype");
            return (Criteria) this;
        }

        public Criteria andPatcardtypeLike(String value) {
            addCriterion("PATCardType like", value, "patcardtype");
            return (Criteria) this;
        }

        public Criteria andPatcardtypeNotLike(String value) {
            addCriterion("PATCardType not like", value, "patcardtype");
            return (Criteria) this;
        }

        public Criteria andPatcardtypeIn(List<String> values) {
            addCriterion("PATCardType in", values, "patcardtype");
            return (Criteria) this;
        }

        public Criteria andPatcardtypeNotIn(List<String> values) {
            addCriterion("PATCardType not in", values, "patcardtype");
            return (Criteria) this;
        }

        public Criteria andPatcardtypeBetween(String value1, String value2) {
            addCriterion("PATCardType between", value1, value2, "patcardtype");
            return (Criteria) this;
        }

        public Criteria andPatcardtypeNotBetween(String value1, String value2) {
            addCriterion("PATCardType not between", value1, value2, "patcardtype");
            return (Criteria) this;
        }

        public Criteria andPatnameIsNull() {
            addCriterion("PATName is null");
            return (Criteria) this;
        }

        public Criteria andPatnameIsNotNull() {
            addCriterion("PATName is not null");
            return (Criteria) this;
        }

        public Criteria andPatnameEqualTo(String value) {
            addCriterion("PATName =", value, "patname");
            return (Criteria) this;
        }

        public Criteria andPatnameNotEqualTo(String value) {
            addCriterion("PATName <>", value, "patname");
            return (Criteria) this;
        }

        public Criteria andPatnameGreaterThan(String value) {
            addCriterion("PATName >", value, "patname");
            return (Criteria) this;
        }

        public Criteria andPatnameGreaterThanOrEqualTo(String value) {
            addCriterion("PATName >=", value, "patname");
            return (Criteria) this;
        }

        public Criteria andPatnameLessThan(String value) {
            addCriterion("PATName <", value, "patname");
            return (Criteria) this;
        }

        public Criteria andPatnameLessThanOrEqualTo(String value) {
            addCriterion("PATName <=", value, "patname");
            return (Criteria) this;
        }

        public Criteria andPatnameLike(String value) {
            addCriterion("PATName like", value, "patname");
            return (Criteria) this;
        }

        public Criteria andPatnameNotLike(String value) {
            addCriterion("PATName not like", value, "patname");
            return (Criteria) this;
        }

        public Criteria andPatnameIn(List<String> values) {
            addCriterion("PATName in", values, "patname");
            return (Criteria) this;
        }

        public Criteria andPatnameNotIn(List<String> values) {
            addCriterion("PATName not in", values, "patname");
            return (Criteria) this;
        }

        public Criteria andPatnameBetween(String value1, String value2) {
            addCriterion("PATName between", value1, value2, "patname");
            return (Criteria) this;
        }

        public Criteria andPatnameNotBetween(String value1, String value2) {
            addCriterion("PATName not between", value1, value2, "patname");
            return (Criteria) this;
        }

        public Criteria andPatdobIsNull() {
            addCriterion("PATDob is null");
            return (Criteria) this;
        }

        public Criteria andPatdobIsNotNull() {
            addCriterion("PATDob is not null");
            return (Criteria) this;
        }

        public Criteria andPatdobEqualTo(Date value) {
            addCriterionForJDBCDate("PATDob =", value, "patdob");
            return (Criteria) this;
        }

        public Criteria andPatdobNotEqualTo(Date value) {
            addCriterionForJDBCDate("PATDob <>", value, "patdob");
            return (Criteria) this;
        }

        public Criteria andPatdobGreaterThan(Date value) {
            addCriterionForJDBCDate("PATDob >", value, "patdob");
            return (Criteria) this;
        }

        public Criteria andPatdobGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("PATDob >=", value, "patdob");
            return (Criteria) this;
        }

        public Criteria andPatdobLessThan(Date value) {
            addCriterionForJDBCDate("PATDob <", value, "patdob");
            return (Criteria) this;
        }

        public Criteria andPatdobLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("PATDob <=", value, "patdob");
            return (Criteria) this;
        }

        public Criteria andPatdobIn(List<Date> values) {
            addCriterionForJDBCDate("PATDob in", values, "patdob");
            return (Criteria) this;
        }

        public Criteria andPatdobNotIn(List<Date> values) {
            addCriterionForJDBCDate("PATDob not in", values, "patdob");
            return (Criteria) this;
        }

        public Criteria andPatdobBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("PATDob between", value1, value2, "patdob");
            return (Criteria) this;
        }

        public Criteria andPatdobNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("PATDob not between", value1, value2, "patdob");
            return (Criteria) this;
        }

        public Criteria andPatsexcodeIsNull() {
            addCriterion("PATSexCode is null");
            return (Criteria) this;
        }

        public Criteria andPatsexcodeIsNotNull() {
            addCriterion("PATSexCode is not null");
            return (Criteria) this;
        }

        public Criteria andPatsexcodeEqualTo(String value) {
            addCriterion("PATSexCode =", value, "patsexcode");
            return (Criteria) this;
        }

        public Criteria andPatsexcodeNotEqualTo(String value) {
            addCriterion("PATSexCode <>", value, "patsexcode");
            return (Criteria) this;
        }

        public Criteria andPatsexcodeGreaterThan(String value) {
            addCriterion("PATSexCode >", value, "patsexcode");
            return (Criteria) this;
        }

        public Criteria andPatsexcodeGreaterThanOrEqualTo(String value) {
            addCriterion("PATSexCode >=", value, "patsexcode");
            return (Criteria) this;
        }

        public Criteria andPatsexcodeLessThan(String value) {
            addCriterion("PATSexCode <", value, "patsexcode");
            return (Criteria) this;
        }

        public Criteria andPatsexcodeLessThanOrEqualTo(String value) {
            addCriterion("PATSexCode <=", value, "patsexcode");
            return (Criteria) this;
        }

        public Criteria andPatsexcodeLike(String value) {
            addCriterion("PATSexCode like", value, "patsexcode");
            return (Criteria) this;
        }

        public Criteria andPatsexcodeNotLike(String value) {
            addCriterion("PATSexCode not like", value, "patsexcode");
            return (Criteria) this;
        }

        public Criteria andPatsexcodeIn(List<String> values) {
            addCriterion("PATSexCode in", values, "patsexcode");
            return (Criteria) this;
        }

        public Criteria andPatsexcodeNotIn(List<String> values) {
            addCriterion("PATSexCode not in", values, "patsexcode");
            return (Criteria) this;
        }

        public Criteria andPatsexcodeBetween(String value1, String value2) {
            addCriterion("PATSexCode between", value1, value2, "patsexcode");
            return (Criteria) this;
        }

        public Criteria andPatsexcodeNotBetween(String value1, String value2) {
            addCriterion("PATSexCode not between", value1, value2, "patsexcode");
            return (Criteria) this;
        }

        public Criteria andPatsexdescIsNull() {
            addCriterion("PATSexDesc is null");
            return (Criteria) this;
        }

        public Criteria andPatsexdescIsNotNull() {
            addCriterion("PATSexDesc is not null");
            return (Criteria) this;
        }

        public Criteria andPatsexdescEqualTo(String value) {
            addCriterion("PATSexDesc =", value, "patsexdesc");
            return (Criteria) this;
        }

        public Criteria andPatsexdescNotEqualTo(String value) {
            addCriterion("PATSexDesc <>", value, "patsexdesc");
            return (Criteria) this;
        }

        public Criteria andPatsexdescGreaterThan(String value) {
            addCriterion("PATSexDesc >", value, "patsexdesc");
            return (Criteria) this;
        }

        public Criteria andPatsexdescGreaterThanOrEqualTo(String value) {
            addCriterion("PATSexDesc >=", value, "patsexdesc");
            return (Criteria) this;
        }

        public Criteria andPatsexdescLessThan(String value) {
            addCriterion("PATSexDesc <", value, "patsexdesc");
            return (Criteria) this;
        }

        public Criteria andPatsexdescLessThanOrEqualTo(String value) {
            addCriterion("PATSexDesc <=", value, "patsexdesc");
            return (Criteria) this;
        }

        public Criteria andPatsexdescLike(String value) {
            addCriterion("PATSexDesc like", value, "patsexdesc");
            return (Criteria) this;
        }

        public Criteria andPatsexdescNotLike(String value) {
            addCriterion("PATSexDesc not like", value, "patsexdesc");
            return (Criteria) this;
        }

        public Criteria andPatsexdescIn(List<String> values) {
            addCriterion("PATSexDesc in", values, "patsexdesc");
            return (Criteria) this;
        }

        public Criteria andPatsexdescNotIn(List<String> values) {
            addCriterion("PATSexDesc not in", values, "patsexdesc");
            return (Criteria) this;
        }

        public Criteria andPatsexdescBetween(String value1, String value2) {
            addCriterion("PATSexDesc between", value1, value2, "patsexdesc");
            return (Criteria) this;
        }

        public Criteria andPatsexdescNotBetween(String value1, String value2) {
            addCriterion("PATSexDesc not between", value1, value2, "patsexdesc");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatuscodeIsNull() {
            addCriterion("PATMaritalStatusCode is null");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatuscodeIsNotNull() {
            addCriterion("PATMaritalStatusCode is not null");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatuscodeEqualTo(String value) {
            addCriterion("PATMaritalStatusCode =", value, "patmaritalstatuscode");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatuscodeNotEqualTo(String value) {
            addCriterion("PATMaritalStatusCode <>", value, "patmaritalstatuscode");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatuscodeGreaterThan(String value) {
            addCriterion("PATMaritalStatusCode >", value, "patmaritalstatuscode");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatuscodeGreaterThanOrEqualTo(String value) {
            addCriterion("PATMaritalStatusCode >=", value, "patmaritalstatuscode");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatuscodeLessThan(String value) {
            addCriterion("PATMaritalStatusCode <", value, "patmaritalstatuscode");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatuscodeLessThanOrEqualTo(String value) {
            addCriterion("PATMaritalStatusCode <=", value, "patmaritalstatuscode");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatuscodeLike(String value) {
            addCriterion("PATMaritalStatusCode like", value, "patmaritalstatuscode");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatuscodeNotLike(String value) {
            addCriterion("PATMaritalStatusCode not like", value, "patmaritalstatuscode");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatuscodeIn(List<String> values) {
            addCriterion("PATMaritalStatusCode in", values, "patmaritalstatuscode");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatuscodeNotIn(List<String> values) {
            addCriterion("PATMaritalStatusCode not in", values, "patmaritalstatuscode");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatuscodeBetween(String value1, String value2) {
            addCriterion("PATMaritalStatusCode between", value1, value2, "patmaritalstatuscode");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatuscodeNotBetween(String value1, String value2) {
            addCriterion("PATMaritalStatusCode not between", value1, value2, "patmaritalstatuscode");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatusdescIsNull() {
            addCriterion("PATMaritalStatusDesc is null");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatusdescIsNotNull() {
            addCriterion("PATMaritalStatusDesc is not null");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatusdescEqualTo(String value) {
            addCriterion("PATMaritalStatusDesc =", value, "patmaritalstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatusdescNotEqualTo(String value) {
            addCriterion("PATMaritalStatusDesc <>", value, "patmaritalstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatusdescGreaterThan(String value) {
            addCriterion("PATMaritalStatusDesc >", value, "patmaritalstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatusdescGreaterThanOrEqualTo(String value) {
            addCriterion("PATMaritalStatusDesc >=", value, "patmaritalstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatusdescLessThan(String value) {
            addCriterion("PATMaritalStatusDesc <", value, "patmaritalstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatusdescLessThanOrEqualTo(String value) {
            addCriterion("PATMaritalStatusDesc <=", value, "patmaritalstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatusdescLike(String value) {
            addCriterion("PATMaritalStatusDesc like", value, "patmaritalstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatusdescNotLike(String value) {
            addCriterion("PATMaritalStatusDesc not like", value, "patmaritalstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatusdescIn(List<String> values) {
            addCriterion("PATMaritalStatusDesc in", values, "patmaritalstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatusdescNotIn(List<String> values) {
            addCriterion("PATMaritalStatusDesc not in", values, "patmaritalstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatusdescBetween(String value1, String value2) {
            addCriterion("PATMaritalStatusDesc between", value1, value2, "patmaritalstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPatmaritalstatusdescNotBetween(String value1, String value2) {
            addCriterion("PATMaritalStatusDesc not between", value1, value2, "patmaritalstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPatnationcodeIsNull() {
            addCriterion("PATNationCode is null");
            return (Criteria) this;
        }

        public Criteria andPatnationcodeIsNotNull() {
            addCriterion("PATNationCode is not null");
            return (Criteria) this;
        }

        public Criteria andPatnationcodeEqualTo(String value) {
            addCriterion("PATNationCode =", value, "patnationcode");
            return (Criteria) this;
        }

        public Criteria andPatnationcodeNotEqualTo(String value) {
            addCriterion("PATNationCode <>", value, "patnationcode");
            return (Criteria) this;
        }

        public Criteria andPatnationcodeGreaterThan(String value) {
            addCriterion("PATNationCode >", value, "patnationcode");
            return (Criteria) this;
        }

        public Criteria andPatnationcodeGreaterThanOrEqualTo(String value) {
            addCriterion("PATNationCode >=", value, "patnationcode");
            return (Criteria) this;
        }

        public Criteria andPatnationcodeLessThan(String value) {
            addCriterion("PATNationCode <", value, "patnationcode");
            return (Criteria) this;
        }

        public Criteria andPatnationcodeLessThanOrEqualTo(String value) {
            addCriterion("PATNationCode <=", value, "patnationcode");
            return (Criteria) this;
        }

        public Criteria andPatnationcodeLike(String value) {
            addCriterion("PATNationCode like", value, "patnationcode");
            return (Criteria) this;
        }

        public Criteria andPatnationcodeNotLike(String value) {
            addCriterion("PATNationCode not like", value, "patnationcode");
            return (Criteria) this;
        }

        public Criteria andPatnationcodeIn(List<String> values) {
            addCriterion("PATNationCode in", values, "patnationcode");
            return (Criteria) this;
        }

        public Criteria andPatnationcodeNotIn(List<String> values) {
            addCriterion("PATNationCode not in", values, "patnationcode");
            return (Criteria) this;
        }

        public Criteria andPatnationcodeBetween(String value1, String value2) {
            addCriterion("PATNationCode between", value1, value2, "patnationcode");
            return (Criteria) this;
        }

        public Criteria andPatnationcodeNotBetween(String value1, String value2) {
            addCriterion("PATNationCode not between", value1, value2, "patnationcode");
            return (Criteria) this;
        }

        public Criteria andPatnationdescIsNull() {
            addCriterion("PATNationDesc is null");
            return (Criteria) this;
        }

        public Criteria andPatnationdescIsNotNull() {
            addCriterion("PATNationDesc is not null");
            return (Criteria) this;
        }

        public Criteria andPatnationdescEqualTo(String value) {
            addCriterion("PATNationDesc =", value, "patnationdesc");
            return (Criteria) this;
        }

        public Criteria andPatnationdescNotEqualTo(String value) {
            addCriterion("PATNationDesc <>", value, "patnationdesc");
            return (Criteria) this;
        }

        public Criteria andPatnationdescGreaterThan(String value) {
            addCriterion("PATNationDesc >", value, "patnationdesc");
            return (Criteria) this;
        }

        public Criteria andPatnationdescGreaterThanOrEqualTo(String value) {
            addCriterion("PATNationDesc >=", value, "patnationdesc");
            return (Criteria) this;
        }

        public Criteria andPatnationdescLessThan(String value) {
            addCriterion("PATNationDesc <", value, "patnationdesc");
            return (Criteria) this;
        }

        public Criteria andPatnationdescLessThanOrEqualTo(String value) {
            addCriterion("PATNationDesc <=", value, "patnationdesc");
            return (Criteria) this;
        }

        public Criteria andPatnationdescLike(String value) {
            addCriterion("PATNationDesc like", value, "patnationdesc");
            return (Criteria) this;
        }

        public Criteria andPatnationdescNotLike(String value) {
            addCriterion("PATNationDesc not like", value, "patnationdesc");
            return (Criteria) this;
        }

        public Criteria andPatnationdescIn(List<String> values) {
            addCriterion("PATNationDesc in", values, "patnationdesc");
            return (Criteria) this;
        }

        public Criteria andPatnationdescNotIn(List<String> values) {
            addCriterion("PATNationDesc not in", values, "patnationdesc");
            return (Criteria) this;
        }

        public Criteria andPatnationdescBetween(String value1, String value2) {
            addCriterion("PATNationDesc between", value1, value2, "patnationdesc");
            return (Criteria) this;
        }

        public Criteria andPatnationdescNotBetween(String value1, String value2) {
            addCriterion("PATNationDesc not between", value1, value2, "patnationdesc");
            return (Criteria) this;
        }

        public Criteria andPatcountrycodeIsNull() {
            addCriterion("PATCountryCode is null");
            return (Criteria) this;
        }

        public Criteria andPatcountrycodeIsNotNull() {
            addCriterion("PATCountryCode is not null");
            return (Criteria) this;
        }

        public Criteria andPatcountrycodeEqualTo(String value) {
            addCriterion("PATCountryCode =", value, "patcountrycode");
            return (Criteria) this;
        }

        public Criteria andPatcountrycodeNotEqualTo(String value) {
            addCriterion("PATCountryCode <>", value, "patcountrycode");
            return (Criteria) this;
        }

        public Criteria andPatcountrycodeGreaterThan(String value) {
            addCriterion("PATCountryCode >", value, "patcountrycode");
            return (Criteria) this;
        }

        public Criteria andPatcountrycodeGreaterThanOrEqualTo(String value) {
            addCriterion("PATCountryCode >=", value, "patcountrycode");
            return (Criteria) this;
        }

        public Criteria andPatcountrycodeLessThan(String value) {
            addCriterion("PATCountryCode <", value, "patcountrycode");
            return (Criteria) this;
        }

        public Criteria andPatcountrycodeLessThanOrEqualTo(String value) {
            addCriterion("PATCountryCode <=", value, "patcountrycode");
            return (Criteria) this;
        }

        public Criteria andPatcountrycodeLike(String value) {
            addCriterion("PATCountryCode like", value, "patcountrycode");
            return (Criteria) this;
        }

        public Criteria andPatcountrycodeNotLike(String value) {
            addCriterion("PATCountryCode not like", value, "patcountrycode");
            return (Criteria) this;
        }

        public Criteria andPatcountrycodeIn(List<String> values) {
            addCriterion("PATCountryCode in", values, "patcountrycode");
            return (Criteria) this;
        }

        public Criteria andPatcountrycodeNotIn(List<String> values) {
            addCriterion("PATCountryCode not in", values, "patcountrycode");
            return (Criteria) this;
        }

        public Criteria andPatcountrycodeBetween(String value1, String value2) {
            addCriterion("PATCountryCode between", value1, value2, "patcountrycode");
            return (Criteria) this;
        }

        public Criteria andPatcountrycodeNotBetween(String value1, String value2) {
            addCriterion("PATCountryCode not between", value1, value2, "patcountrycode");
            return (Criteria) this;
        }

        public Criteria andPatcountrydescIsNull() {
            addCriterion("PATCountryDesc is null");
            return (Criteria) this;
        }

        public Criteria andPatcountrydescIsNotNull() {
            addCriterion("PATCountryDesc is not null");
            return (Criteria) this;
        }

        public Criteria andPatcountrydescEqualTo(String value) {
            addCriterion("PATCountryDesc =", value, "patcountrydesc");
            return (Criteria) this;
        }

        public Criteria andPatcountrydescNotEqualTo(String value) {
            addCriterion("PATCountryDesc <>", value, "patcountrydesc");
            return (Criteria) this;
        }

        public Criteria andPatcountrydescGreaterThan(String value) {
            addCriterion("PATCountryDesc >", value, "patcountrydesc");
            return (Criteria) this;
        }

        public Criteria andPatcountrydescGreaterThanOrEqualTo(String value) {
            addCriterion("PATCountryDesc >=", value, "patcountrydesc");
            return (Criteria) this;
        }

        public Criteria andPatcountrydescLessThan(String value) {
            addCriterion("PATCountryDesc <", value, "patcountrydesc");
            return (Criteria) this;
        }

        public Criteria andPatcountrydescLessThanOrEqualTo(String value) {
            addCriterion("PATCountryDesc <=", value, "patcountrydesc");
            return (Criteria) this;
        }

        public Criteria andPatcountrydescLike(String value) {
            addCriterion("PATCountryDesc like", value, "patcountrydesc");
            return (Criteria) this;
        }

        public Criteria andPatcountrydescNotLike(String value) {
            addCriterion("PATCountryDesc not like", value, "patcountrydesc");
            return (Criteria) this;
        }

        public Criteria andPatcountrydescIn(List<String> values) {
            addCriterion("PATCountryDesc in", values, "patcountrydesc");
            return (Criteria) this;
        }

        public Criteria andPatcountrydescNotIn(List<String> values) {
            addCriterion("PATCountryDesc not in", values, "patcountrydesc");
            return (Criteria) this;
        }

        public Criteria andPatcountrydescBetween(String value1, String value2) {
            addCriterion("PATCountryDesc between", value1, value2, "patcountrydesc");
            return (Criteria) this;
        }

        public Criteria andPatcountrydescNotBetween(String value1, String value2) {
            addCriterion("PATCountryDesc not between", value1, value2, "patcountrydesc");
            return (Criteria) this;
        }

        public Criteria andPatmotheridIsNull() {
            addCriterion("PATMotherID is null");
            return (Criteria) this;
        }

        public Criteria andPatmotheridIsNotNull() {
            addCriterion("PATMotherID is not null");
            return (Criteria) this;
        }

        public Criteria andPatmotheridEqualTo(String value) {
            addCriterion("PATMotherID =", value, "patmotherid");
            return (Criteria) this;
        }

        public Criteria andPatmotheridNotEqualTo(String value) {
            addCriterion("PATMotherID <>", value, "patmotherid");
            return (Criteria) this;
        }

        public Criteria andPatmotheridGreaterThan(String value) {
            addCriterion("PATMotherID >", value, "patmotherid");
            return (Criteria) this;
        }

        public Criteria andPatmotheridGreaterThanOrEqualTo(String value) {
            addCriterion("PATMotherID >=", value, "patmotherid");
            return (Criteria) this;
        }

        public Criteria andPatmotheridLessThan(String value) {
            addCriterion("PATMotherID <", value, "patmotherid");
            return (Criteria) this;
        }

        public Criteria andPatmotheridLessThanOrEqualTo(String value) {
            addCriterion("PATMotherID <=", value, "patmotherid");
            return (Criteria) this;
        }

        public Criteria andPatmotheridLike(String value) {
            addCriterion("PATMotherID like", value, "patmotherid");
            return (Criteria) this;
        }

        public Criteria andPatmotheridNotLike(String value) {
            addCriterion("PATMotherID not like", value, "patmotherid");
            return (Criteria) this;
        }

        public Criteria andPatmotheridIn(List<String> values) {
            addCriterion("PATMotherID in", values, "patmotherid");
            return (Criteria) this;
        }

        public Criteria andPatmotheridNotIn(List<String> values) {
            addCriterion("PATMotherID not in", values, "patmotherid");
            return (Criteria) this;
        }

        public Criteria andPatmotheridBetween(String value1, String value2) {
            addCriterion("PATMotherID between", value1, value2, "patmotherid");
            return (Criteria) this;
        }

        public Criteria andPatmotheridNotBetween(String value1, String value2) {
            addCriterion("PATMotherID not between", value1, value2, "patmotherid");
            return (Criteria) this;
        }

        public Criteria andPatdeceaseddateIsNull() {
            addCriterion("PATDeceasedDate is null");
            return (Criteria) this;
        }

        public Criteria andPatdeceaseddateIsNotNull() {
            addCriterion("PATDeceasedDate is not null");
            return (Criteria) this;
        }

        public Criteria andPatdeceaseddateEqualTo(Date value) {
            addCriterionForJDBCDate("PATDeceasedDate =", value, "patdeceaseddate");
            return (Criteria) this;
        }

        public Criteria andPatdeceaseddateNotEqualTo(Date value) {
            addCriterionForJDBCDate("PATDeceasedDate <>", value, "patdeceaseddate");
            return (Criteria) this;
        }

        public Criteria andPatdeceaseddateGreaterThan(Date value) {
            addCriterionForJDBCDate("PATDeceasedDate >", value, "patdeceaseddate");
            return (Criteria) this;
        }

        public Criteria andPatdeceaseddateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("PATDeceasedDate >=", value, "patdeceaseddate");
            return (Criteria) this;
        }

        public Criteria andPatdeceaseddateLessThan(Date value) {
            addCriterionForJDBCDate("PATDeceasedDate <", value, "patdeceaseddate");
            return (Criteria) this;
        }

        public Criteria andPatdeceaseddateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("PATDeceasedDate <=", value, "patdeceaseddate");
            return (Criteria) this;
        }

        public Criteria andPatdeceaseddateIn(List<Date> values) {
            addCriterionForJDBCDate("PATDeceasedDate in", values, "patdeceaseddate");
            return (Criteria) this;
        }

        public Criteria andPatdeceaseddateNotIn(List<Date> values) {
            addCriterionForJDBCDate("PATDeceasedDate not in", values, "patdeceaseddate");
            return (Criteria) this;
        }

        public Criteria andPatdeceaseddateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("PATDeceasedDate between", value1, value2, "patdeceaseddate");
            return (Criteria) this;
        }

        public Criteria andPatdeceaseddateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("PATDeceasedDate not between", value1, value2, "patdeceaseddate");
            return (Criteria) this;
        }

        public Criteria andPatdeceasedtimeIsNull() {
            addCriterion("PATDeceasedTime is null");
            return (Criteria) this;
        }

        public Criteria andPatdeceasedtimeIsNotNull() {
            addCriterion("PATDeceasedTime is not null");
            return (Criteria) this;
        }

        public Criteria andPatdeceasedtimeEqualTo(Date value) {
            addCriterionForJDBCTime("PATDeceasedTime =", value, "patdeceasedtime");
            return (Criteria) this;
        }

        public Criteria andPatdeceasedtimeNotEqualTo(Date value) {
            addCriterionForJDBCTime("PATDeceasedTime <>", value, "patdeceasedtime");
            return (Criteria) this;
        }

        public Criteria andPatdeceasedtimeGreaterThan(Date value) {
            addCriterionForJDBCTime("PATDeceasedTime >", value, "patdeceasedtime");
            return (Criteria) this;
        }

        public Criteria andPatdeceasedtimeGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("PATDeceasedTime >=", value, "patdeceasedtime");
            return (Criteria) this;
        }

        public Criteria andPatdeceasedtimeLessThan(Date value) {
            addCriterionForJDBCTime("PATDeceasedTime <", value, "patdeceasedtime");
            return (Criteria) this;
        }

        public Criteria andPatdeceasedtimeLessThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("PATDeceasedTime <=", value, "patdeceasedtime");
            return (Criteria) this;
        }

        public Criteria andPatdeceasedtimeIn(List<Date> values) {
            addCriterionForJDBCTime("PATDeceasedTime in", values, "patdeceasedtime");
            return (Criteria) this;
        }

        public Criteria andPatdeceasedtimeNotIn(List<Date> values) {
            addCriterionForJDBCTime("PATDeceasedTime not in", values, "patdeceasedtime");
            return (Criteria) this;
        }

        public Criteria andPatdeceasedtimeBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("PATDeceasedTime between", value1, value2, "patdeceasedtime");
            return (Criteria) this;
        }

        public Criteria andPatdeceasedtimeNotBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("PATDeceasedTime not between", value1, value2, "patdeceasedtime");
            return (Criteria) this;
        }

        public Criteria andPattelephoneIsNull() {
            addCriterion("PATTelephone is null");
            return (Criteria) this;
        }

        public Criteria andPattelephoneIsNotNull() {
            addCriterion("PATTelephone is not null");
            return (Criteria) this;
        }

        public Criteria andPattelephoneEqualTo(String value) {
            addCriterion("PATTelephone =", value, "pattelephone");
            return (Criteria) this;
        }

        public Criteria andPattelephoneNotEqualTo(String value) {
            addCriterion("PATTelephone <>", value, "pattelephone");
            return (Criteria) this;
        }

        public Criteria andPattelephoneGreaterThan(String value) {
            addCriterion("PATTelephone >", value, "pattelephone");
            return (Criteria) this;
        }

        public Criteria andPattelephoneGreaterThanOrEqualTo(String value) {
            addCriterion("PATTelephone >=", value, "pattelephone");
            return (Criteria) this;
        }

        public Criteria andPattelephoneLessThan(String value) {
            addCriterion("PATTelephone <", value, "pattelephone");
            return (Criteria) this;
        }

        public Criteria andPattelephoneLessThanOrEqualTo(String value) {
            addCriterion("PATTelephone <=", value, "pattelephone");
            return (Criteria) this;
        }

        public Criteria andPattelephoneLike(String value) {
            addCriterion("PATTelephone like", value, "pattelephone");
            return (Criteria) this;
        }

        public Criteria andPattelephoneNotLike(String value) {
            addCriterion("PATTelephone not like", value, "pattelephone");
            return (Criteria) this;
        }

        public Criteria andPattelephoneIn(List<String> values) {
            addCriterion("PATTelephone in", values, "pattelephone");
            return (Criteria) this;
        }

        public Criteria andPattelephoneNotIn(List<String> values) {
            addCriterion("PATTelephone not in", values, "pattelephone");
            return (Criteria) this;
        }

        public Criteria andPattelephoneBetween(String value1, String value2) {
            addCriterion("PATTelephone between", value1, value2, "pattelephone");
            return (Criteria) this;
        }

        public Criteria andPattelephoneNotBetween(String value1, String value2) {
            addCriterion("PATTelephone not between", value1, value2, "pattelephone");
            return (Criteria) this;
        }

        public Criteria andPatoccupationcodeIsNull() {
            addCriterion("PATOccupationCode is null");
            return (Criteria) this;
        }

        public Criteria andPatoccupationcodeIsNotNull() {
            addCriterion("PATOccupationCode is not null");
            return (Criteria) this;
        }

        public Criteria andPatoccupationcodeEqualTo(String value) {
            addCriterion("PATOccupationCode =", value, "patoccupationcode");
            return (Criteria) this;
        }

        public Criteria andPatoccupationcodeNotEqualTo(String value) {
            addCriterion("PATOccupationCode <>", value, "patoccupationcode");
            return (Criteria) this;
        }

        public Criteria andPatoccupationcodeGreaterThan(String value) {
            addCriterion("PATOccupationCode >", value, "patoccupationcode");
            return (Criteria) this;
        }

        public Criteria andPatoccupationcodeGreaterThanOrEqualTo(String value) {
            addCriterion("PATOccupationCode >=", value, "patoccupationcode");
            return (Criteria) this;
        }

        public Criteria andPatoccupationcodeLessThan(String value) {
            addCriterion("PATOccupationCode <", value, "patoccupationcode");
            return (Criteria) this;
        }

        public Criteria andPatoccupationcodeLessThanOrEqualTo(String value) {
            addCriterion("PATOccupationCode <=", value, "patoccupationcode");
            return (Criteria) this;
        }

        public Criteria andPatoccupationcodeLike(String value) {
            addCriterion("PATOccupationCode like", value, "patoccupationcode");
            return (Criteria) this;
        }

        public Criteria andPatoccupationcodeNotLike(String value) {
            addCriterion("PATOccupationCode not like", value, "patoccupationcode");
            return (Criteria) this;
        }

        public Criteria andPatoccupationcodeIn(List<String> values) {
            addCriterion("PATOccupationCode in", values, "patoccupationcode");
            return (Criteria) this;
        }

        public Criteria andPatoccupationcodeNotIn(List<String> values) {
            addCriterion("PATOccupationCode not in", values, "patoccupationcode");
            return (Criteria) this;
        }

        public Criteria andPatoccupationcodeBetween(String value1, String value2) {
            addCriterion("PATOccupationCode between", value1, value2, "patoccupationcode");
            return (Criteria) this;
        }

        public Criteria andPatoccupationcodeNotBetween(String value1, String value2) {
            addCriterion("PATOccupationCode not between", value1, value2, "patoccupationcode");
            return (Criteria) this;
        }

        public Criteria andPatoccupationdescIsNull() {
            addCriterion("PATOccupationDesc is null");
            return (Criteria) this;
        }

        public Criteria andPatoccupationdescIsNotNull() {
            addCriterion("PATOccupationDesc is not null");
            return (Criteria) this;
        }

        public Criteria andPatoccupationdescEqualTo(String value) {
            addCriterion("PATOccupationDesc =", value, "patoccupationdesc");
            return (Criteria) this;
        }

        public Criteria andPatoccupationdescNotEqualTo(String value) {
            addCriterion("PATOccupationDesc <>", value, "patoccupationdesc");
            return (Criteria) this;
        }

        public Criteria andPatoccupationdescGreaterThan(String value) {
            addCriterion("PATOccupationDesc >", value, "patoccupationdesc");
            return (Criteria) this;
        }

        public Criteria andPatoccupationdescGreaterThanOrEqualTo(String value) {
            addCriterion("PATOccupationDesc >=", value, "patoccupationdesc");
            return (Criteria) this;
        }

        public Criteria andPatoccupationdescLessThan(String value) {
            addCriterion("PATOccupationDesc <", value, "patoccupationdesc");
            return (Criteria) this;
        }

        public Criteria andPatoccupationdescLessThanOrEqualTo(String value) {
            addCriterion("PATOccupationDesc <=", value, "patoccupationdesc");
            return (Criteria) this;
        }

        public Criteria andPatoccupationdescLike(String value) {
            addCriterion("PATOccupationDesc like", value, "patoccupationdesc");
            return (Criteria) this;
        }

        public Criteria andPatoccupationdescNotLike(String value) {
            addCriterion("PATOccupationDesc not like", value, "patoccupationdesc");
            return (Criteria) this;
        }

        public Criteria andPatoccupationdescIn(List<String> values) {
            addCriterion("PATOccupationDesc in", values, "patoccupationdesc");
            return (Criteria) this;
        }

        public Criteria andPatoccupationdescNotIn(List<String> values) {
            addCriterion("PATOccupationDesc not in", values, "patoccupationdesc");
            return (Criteria) this;
        }

        public Criteria andPatoccupationdescBetween(String value1, String value2) {
            addCriterion("PATOccupationDesc between", value1, value2, "patoccupationdesc");
            return (Criteria) this;
        }

        public Criteria andPatoccupationdescNotBetween(String value1, String value2) {
            addCriterion("PATOccupationDesc not between", value1, value2, "patoccupationdesc");
            return (Criteria) this;
        }

        public Criteria andPatworkplacetelnumIsNull() {
            addCriterion("PATWorkPlaceTelNum is null");
            return (Criteria) this;
        }

        public Criteria andPatworkplacetelnumIsNotNull() {
            addCriterion("PATWorkPlaceTelNum is not null");
            return (Criteria) this;
        }

        public Criteria andPatworkplacetelnumEqualTo(String value) {
            addCriterion("PATWorkPlaceTelNum =", value, "patworkplacetelnum");
            return (Criteria) this;
        }

        public Criteria andPatworkplacetelnumNotEqualTo(String value) {
            addCriterion("PATWorkPlaceTelNum <>", value, "patworkplacetelnum");
            return (Criteria) this;
        }

        public Criteria andPatworkplacetelnumGreaterThan(String value) {
            addCriterion("PATWorkPlaceTelNum >", value, "patworkplacetelnum");
            return (Criteria) this;
        }

        public Criteria andPatworkplacetelnumGreaterThanOrEqualTo(String value) {
            addCriterion("PATWorkPlaceTelNum >=", value, "patworkplacetelnum");
            return (Criteria) this;
        }

        public Criteria andPatworkplacetelnumLessThan(String value) {
            addCriterion("PATWorkPlaceTelNum <", value, "patworkplacetelnum");
            return (Criteria) this;
        }

        public Criteria andPatworkplacetelnumLessThanOrEqualTo(String value) {
            addCriterion("PATWorkPlaceTelNum <=", value, "patworkplacetelnum");
            return (Criteria) this;
        }

        public Criteria andPatworkplacetelnumLike(String value) {
            addCriterion("PATWorkPlaceTelNum like", value, "patworkplacetelnum");
            return (Criteria) this;
        }

        public Criteria andPatworkplacetelnumNotLike(String value) {
            addCriterion("PATWorkPlaceTelNum not like", value, "patworkplacetelnum");
            return (Criteria) this;
        }

        public Criteria andPatworkplacetelnumIn(List<String> values) {
            addCriterion("PATWorkPlaceTelNum in", values, "patworkplacetelnum");
            return (Criteria) this;
        }

        public Criteria andPatworkplacetelnumNotIn(List<String> values) {
            addCriterion("PATWorkPlaceTelNum not in", values, "patworkplacetelnum");
            return (Criteria) this;
        }

        public Criteria andPatworkplacetelnumBetween(String value1, String value2) {
            addCriterion("PATWorkPlaceTelNum between", value1, value2, "patworkplacetelnum");
            return (Criteria) this;
        }

        public Criteria andPatworkplacetelnumNotBetween(String value1, String value2) {
            addCriterion("PATWorkPlaceTelNum not between", value1, value2, "patworkplacetelnum");
            return (Criteria) this;
        }

        public Criteria andPatidentitynumIsNull() {
            addCriterion("PATIdentityNum is null");
            return (Criteria) this;
        }

        public Criteria andPatidentitynumIsNotNull() {
            addCriterion("PATIdentityNum is not null");
            return (Criteria) this;
        }

        public Criteria andPatidentitynumEqualTo(String value) {
            addCriterion("PATIdentityNum =", value, "patidentitynum");
            return (Criteria) this;
        }

        public Criteria andPatidentitynumNotEqualTo(String value) {
            addCriterion("PATIdentityNum <>", value, "patidentitynum");
            return (Criteria) this;
        }

        public Criteria andPatidentitynumGreaterThan(String value) {
            addCriterion("PATIdentityNum >", value, "patidentitynum");
            return (Criteria) this;
        }

        public Criteria andPatidentitynumGreaterThanOrEqualTo(String value) {
            addCriterion("PATIdentityNum >=", value, "patidentitynum");
            return (Criteria) this;
        }

        public Criteria andPatidentitynumLessThan(String value) {
            addCriterion("PATIdentityNum <", value, "patidentitynum");
            return (Criteria) this;
        }

        public Criteria andPatidentitynumLessThanOrEqualTo(String value) {
            addCriterion("PATIdentityNum <=", value, "patidentitynum");
            return (Criteria) this;
        }

        public Criteria andPatidentitynumLike(String value) {
            addCriterion("PATIdentityNum like", value, "patidentitynum");
            return (Criteria) this;
        }

        public Criteria andPatidentitynumNotLike(String value) {
            addCriterion("PATIdentityNum not like", value, "patidentitynum");
            return (Criteria) this;
        }

        public Criteria andPatidentitynumIn(List<String> values) {
            addCriterion("PATIdentityNum in", values, "patidentitynum");
            return (Criteria) this;
        }

        public Criteria andPatidentitynumNotIn(List<String> values) {
            addCriterion("PATIdentityNum not in", values, "patidentitynum");
            return (Criteria) this;
        }

        public Criteria andPatidentitynumBetween(String value1, String value2) {
            addCriterion("PATIdentityNum between", value1, value2, "patidentitynum");
            return (Criteria) this;
        }

        public Criteria andPatidentitynumNotBetween(String value1, String value2) {
            addCriterion("PATIdentityNum not between", value1, value2, "patidentitynum");
            return (Criteria) this;
        }

        public Criteria andPatidtypecodeIsNull() {
            addCriterion("PATIdTypeCode is null");
            return (Criteria) this;
        }

        public Criteria andPatidtypecodeIsNotNull() {
            addCriterion("PATIdTypeCode is not null");
            return (Criteria) this;
        }

        public Criteria andPatidtypecodeEqualTo(String value) {
            addCriterion("PATIdTypeCode =", value, "patidtypecode");
            return (Criteria) this;
        }

        public Criteria andPatidtypecodeNotEqualTo(String value) {
            addCriterion("PATIdTypeCode <>", value, "patidtypecode");
            return (Criteria) this;
        }

        public Criteria andPatidtypecodeGreaterThan(String value) {
            addCriterion("PATIdTypeCode >", value, "patidtypecode");
            return (Criteria) this;
        }

        public Criteria andPatidtypecodeGreaterThanOrEqualTo(String value) {
            addCriterion("PATIdTypeCode >=", value, "patidtypecode");
            return (Criteria) this;
        }

        public Criteria andPatidtypecodeLessThan(String value) {
            addCriterion("PATIdTypeCode <", value, "patidtypecode");
            return (Criteria) this;
        }

        public Criteria andPatidtypecodeLessThanOrEqualTo(String value) {
            addCriterion("PATIdTypeCode <=", value, "patidtypecode");
            return (Criteria) this;
        }

        public Criteria andPatidtypecodeLike(String value) {
            addCriterion("PATIdTypeCode like", value, "patidtypecode");
            return (Criteria) this;
        }

        public Criteria andPatidtypecodeNotLike(String value) {
            addCriterion("PATIdTypeCode not like", value, "patidtypecode");
            return (Criteria) this;
        }

        public Criteria andPatidtypecodeIn(List<String> values) {
            addCriterion("PATIdTypeCode in", values, "patidtypecode");
            return (Criteria) this;
        }

        public Criteria andPatidtypecodeNotIn(List<String> values) {
            addCriterion("PATIdTypeCode not in", values, "patidtypecode");
            return (Criteria) this;
        }

        public Criteria andPatidtypecodeBetween(String value1, String value2) {
            addCriterion("PATIdTypeCode between", value1, value2, "patidtypecode");
            return (Criteria) this;
        }

        public Criteria andPatidtypecodeNotBetween(String value1, String value2) {
            addCriterion("PATIdTypeCode not between", value1, value2, "patidtypecode");
            return (Criteria) this;
        }

        public Criteria andPatidtypedescIsNull() {
            addCriterion("PATIdTypeDesc is null");
            return (Criteria) this;
        }

        public Criteria andPatidtypedescIsNotNull() {
            addCriterion("PATIdTypeDesc is not null");
            return (Criteria) this;
        }

        public Criteria andPatidtypedescEqualTo(String value) {
            addCriterion("PATIdTypeDesc =", value, "patidtypedesc");
            return (Criteria) this;
        }

        public Criteria andPatidtypedescNotEqualTo(String value) {
            addCriterion("PATIdTypeDesc <>", value, "patidtypedesc");
            return (Criteria) this;
        }

        public Criteria andPatidtypedescGreaterThan(String value) {
            addCriterion("PATIdTypeDesc >", value, "patidtypedesc");
            return (Criteria) this;
        }

        public Criteria andPatidtypedescGreaterThanOrEqualTo(String value) {
            addCriterion("PATIdTypeDesc >=", value, "patidtypedesc");
            return (Criteria) this;
        }

        public Criteria andPatidtypedescLessThan(String value) {
            addCriterion("PATIdTypeDesc <", value, "patidtypedesc");
            return (Criteria) this;
        }

        public Criteria andPatidtypedescLessThanOrEqualTo(String value) {
            addCriterion("PATIdTypeDesc <=", value, "patidtypedesc");
            return (Criteria) this;
        }

        public Criteria andPatidtypedescLike(String value) {
            addCriterion("PATIdTypeDesc like", value, "patidtypedesc");
            return (Criteria) this;
        }

        public Criteria andPatidtypedescNotLike(String value) {
            addCriterion("PATIdTypeDesc not like", value, "patidtypedesc");
            return (Criteria) this;
        }

        public Criteria andPatidtypedescIn(List<String> values) {
            addCriterion("PATIdTypeDesc in", values, "patidtypedesc");
            return (Criteria) this;
        }

        public Criteria andPatidtypedescNotIn(List<String> values) {
            addCriterion("PATIdTypeDesc not in", values, "patidtypedesc");
            return (Criteria) this;
        }

        public Criteria andPatidtypedescBetween(String value1, String value2) {
            addCriterion("PATIdTypeDesc between", value1, value2, "patidtypedesc");
            return (Criteria) this;
        }

        public Criteria andPatidtypedescNotBetween(String value1, String value2) {
            addCriterion("PATIdTypeDesc not between", value1, value2, "patidtypedesc");
            return (Criteria) this;
        }

        public Criteria andPatrelationnameIsNull() {
            addCriterion("PATRelationName is null");
            return (Criteria) this;
        }

        public Criteria andPatrelationnameIsNotNull() {
            addCriterion("PATRelationName is not null");
            return (Criteria) this;
        }

        public Criteria andPatrelationnameEqualTo(String value) {
            addCriterion("PATRelationName =", value, "patrelationname");
            return (Criteria) this;
        }

        public Criteria andPatrelationnameNotEqualTo(String value) {
            addCriterion("PATRelationName <>", value, "patrelationname");
            return (Criteria) this;
        }

        public Criteria andPatrelationnameGreaterThan(String value) {
            addCriterion("PATRelationName >", value, "patrelationname");
            return (Criteria) this;
        }

        public Criteria andPatrelationnameGreaterThanOrEqualTo(String value) {
            addCriterion("PATRelationName >=", value, "patrelationname");
            return (Criteria) this;
        }

        public Criteria andPatrelationnameLessThan(String value) {
            addCriterion("PATRelationName <", value, "patrelationname");
            return (Criteria) this;
        }

        public Criteria andPatrelationnameLessThanOrEqualTo(String value) {
            addCriterion("PATRelationName <=", value, "patrelationname");
            return (Criteria) this;
        }

        public Criteria andPatrelationnameLike(String value) {
            addCriterion("PATRelationName like", value, "patrelationname");
            return (Criteria) this;
        }

        public Criteria andPatrelationnameNotLike(String value) {
            addCriterion("PATRelationName not like", value, "patrelationname");
            return (Criteria) this;
        }

        public Criteria andPatrelationnameIn(List<String> values) {
            addCriterion("PATRelationName in", values, "patrelationname");
            return (Criteria) this;
        }

        public Criteria andPatrelationnameNotIn(List<String> values) {
            addCriterion("PATRelationName not in", values, "patrelationname");
            return (Criteria) this;
        }

        public Criteria andPatrelationnameBetween(String value1, String value2) {
            addCriterion("PATRelationName between", value1, value2, "patrelationname");
            return (Criteria) this;
        }

        public Criteria andPatrelationnameNotBetween(String value1, String value2) {
            addCriterion("PATRelationName not between", value1, value2, "patrelationname");
            return (Criteria) this;
        }

        public Criteria andPatrelationphoneIsNull() {
            addCriterion("PATRelationPhone is null");
            return (Criteria) this;
        }

        public Criteria andPatrelationphoneIsNotNull() {
            addCriterion("PATRelationPhone is not null");
            return (Criteria) this;
        }

        public Criteria andPatrelationphoneEqualTo(String value) {
            addCriterion("PATRelationPhone =", value, "patrelationphone");
            return (Criteria) this;
        }

        public Criteria andPatrelationphoneNotEqualTo(String value) {
            addCriterion("PATRelationPhone <>", value, "patrelationphone");
            return (Criteria) this;
        }

        public Criteria andPatrelationphoneGreaterThan(String value) {
            addCriterion("PATRelationPhone >", value, "patrelationphone");
            return (Criteria) this;
        }

        public Criteria andPatrelationphoneGreaterThanOrEqualTo(String value) {
            addCriterion("PATRelationPhone >=", value, "patrelationphone");
            return (Criteria) this;
        }

        public Criteria andPatrelationphoneLessThan(String value) {
            addCriterion("PATRelationPhone <", value, "patrelationphone");
            return (Criteria) this;
        }

        public Criteria andPatrelationphoneLessThanOrEqualTo(String value) {
            addCriterion("PATRelationPhone <=", value, "patrelationphone");
            return (Criteria) this;
        }

        public Criteria andPatrelationphoneLike(String value) {
            addCriterion("PATRelationPhone like", value, "patrelationphone");
            return (Criteria) this;
        }

        public Criteria andPatrelationphoneNotLike(String value) {
            addCriterion("PATRelationPhone not like", value, "patrelationphone");
            return (Criteria) this;
        }

        public Criteria andPatrelationphoneIn(List<String> values) {
            addCriterion("PATRelationPhone in", values, "patrelationphone");
            return (Criteria) this;
        }

        public Criteria andPatrelationphoneNotIn(List<String> values) {
            addCriterion("PATRelationPhone not in", values, "patrelationphone");
            return (Criteria) this;
        }

        public Criteria andPatrelationphoneBetween(String value1, String value2) {
            addCriterion("PATRelationPhone between", value1, value2, "patrelationphone");
            return (Criteria) this;
        }

        public Criteria andPatrelationphoneNotBetween(String value1, String value2) {
            addCriterion("PATRelationPhone not between", value1, value2, "patrelationphone");
            return (Criteria) this;
        }

        public Criteria andPaadmvisitnumberIsNull() {
            addCriterion("PAADMVisitNumber is null");
            return (Criteria) this;
        }

        public Criteria andPaadmvisitnumberIsNotNull() {
            addCriterion("PAADMVisitNumber is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmvisitnumberEqualTo(String value) {
            addCriterion("PAADMVisitNumber =", value, "paadmvisitnumber");
            return (Criteria) this;
        }

        public Criteria andPaadmvisitnumberNotEqualTo(String value) {
            addCriterion("PAADMVisitNumber <>", value, "paadmvisitnumber");
            return (Criteria) this;
        }

        public Criteria andPaadmvisitnumberGreaterThan(String value) {
            addCriterion("PAADMVisitNumber >", value, "paadmvisitnumber");
            return (Criteria) this;
        }

        public Criteria andPaadmvisitnumberGreaterThanOrEqualTo(String value) {
            addCriterion("PAADMVisitNumber >=", value, "paadmvisitnumber");
            return (Criteria) this;
        }

        public Criteria andPaadmvisitnumberLessThan(String value) {
            addCriterion("PAADMVisitNumber <", value, "paadmvisitnumber");
            return (Criteria) this;
        }

        public Criteria andPaadmvisitnumberLessThanOrEqualTo(String value) {
            addCriterion("PAADMVisitNumber <=", value, "paadmvisitnumber");
            return (Criteria) this;
        }

        public Criteria andPaadmvisitnumberLike(String value) {
            addCriterion("PAADMVisitNumber like", value, "paadmvisitnumber");
            return (Criteria) this;
        }

        public Criteria andPaadmvisitnumberNotLike(String value) {
            addCriterion("PAADMVisitNumber not like", value, "paadmvisitnumber");
            return (Criteria) this;
        }

        public Criteria andPaadmvisitnumberIn(List<String> values) {
            addCriterion("PAADMVisitNumber in", values, "paadmvisitnumber");
            return (Criteria) this;
        }

        public Criteria andPaadmvisitnumberNotIn(List<String> values) {
            addCriterion("PAADMVisitNumber not in", values, "paadmvisitnumber");
            return (Criteria) this;
        }

        public Criteria andPaadmvisitnumberBetween(String value1, String value2) {
            addCriterion("PAADMVisitNumber between", value1, value2, "paadmvisitnumber");
            return (Criteria) this;
        }

        public Criteria andPaadmvisitnumberNotBetween(String value1, String value2) {
            addCriterion("PAADMVisitNumber not between", value1, value2, "paadmvisitnumber");
            return (Criteria) this;
        }

        public Criteria andPaadmvisittimesIsNull() {
            addCriterion("PAADMVisitTimes is null");
            return (Criteria) this;
        }

        public Criteria andPaadmvisittimesIsNotNull() {
            addCriterion("PAADMVisitTimes is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmvisittimesEqualTo(Integer value) {
            addCriterion("PAADMVisitTimes =", value, "paadmvisittimes");
            return (Criteria) this;
        }

        public Criteria andPaadmvisittimesNotEqualTo(Integer value) {
            addCriterion("PAADMVisitTimes <>", value, "paadmvisittimes");
            return (Criteria) this;
        }

        public Criteria andPaadmvisittimesGreaterThan(Integer value) {
            addCriterion("PAADMVisitTimes >", value, "paadmvisittimes");
            return (Criteria) this;
        }

        public Criteria andPaadmvisittimesGreaterThanOrEqualTo(Integer value) {
            addCriterion("PAADMVisitTimes >=", value, "paadmvisittimes");
            return (Criteria) this;
        }

        public Criteria andPaadmvisittimesLessThan(Integer value) {
            addCriterion("PAADMVisitTimes <", value, "paadmvisittimes");
            return (Criteria) this;
        }

        public Criteria andPaadmvisittimesLessThanOrEqualTo(Integer value) {
            addCriterion("PAADMVisitTimes <=", value, "paadmvisittimes");
            return (Criteria) this;
        }

        public Criteria andPaadmvisittimesIn(List<Integer> values) {
            addCriterion("PAADMVisitTimes in", values, "paadmvisittimes");
            return (Criteria) this;
        }

        public Criteria andPaadmvisittimesNotIn(List<Integer> values) {
            addCriterion("PAADMVisitTimes not in", values, "paadmvisittimes");
            return (Criteria) this;
        }

        public Criteria andPaadmvisittimesBetween(Integer value1, Integer value2) {
            addCriterion("PAADMVisitTimes between", value1, value2, "paadmvisittimes");
            return (Criteria) this;
        }

        public Criteria andPaadmvisittimesNotBetween(Integer value1, Integer value2) {
            addCriterion("PAADMVisitTimes not between", value1, value2, "paadmvisittimes");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptcodeIsNull() {
            addCriterion("PAADMDeptCode is null");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptcodeIsNotNull() {
            addCriterion("PAADMDeptCode is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptcodeEqualTo(String value) {
            addCriterion("PAADMDeptCode =", value, "paadmdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptcodeNotEqualTo(String value) {
            addCriterion("PAADMDeptCode <>", value, "paadmdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptcodeGreaterThan(String value) {
            addCriterion("PAADMDeptCode >", value, "paadmdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptcodeGreaterThanOrEqualTo(String value) {
            addCriterion("PAADMDeptCode >=", value, "paadmdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptcodeLessThan(String value) {
            addCriterion("PAADMDeptCode <", value, "paadmdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptcodeLessThanOrEqualTo(String value) {
            addCriterion("PAADMDeptCode <=", value, "paadmdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptcodeLike(String value) {
            addCriterion("PAADMDeptCode like", value, "paadmdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptcodeNotLike(String value) {
            addCriterion("PAADMDeptCode not like", value, "paadmdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptcodeIn(List<String> values) {
            addCriterion("PAADMDeptCode in", values, "paadmdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptcodeNotIn(List<String> values) {
            addCriterion("PAADMDeptCode not in", values, "paadmdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptcodeBetween(String value1, String value2) {
            addCriterion("PAADMDeptCode between", value1, value2, "paadmdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptcodeNotBetween(String value1, String value2) {
            addCriterion("PAADMDeptCode not between", value1, value2, "paadmdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptdescIsNull() {
            addCriterion("PAADMDeptDesc is null");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptdescIsNotNull() {
            addCriterion("PAADMDeptDesc is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptdescEqualTo(String value) {
            addCriterion("PAADMDeptDesc =", value, "paadmdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptdescNotEqualTo(String value) {
            addCriterion("PAADMDeptDesc <>", value, "paadmdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptdescGreaterThan(String value) {
            addCriterion("PAADMDeptDesc >", value, "paadmdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptdescGreaterThanOrEqualTo(String value) {
            addCriterion("PAADMDeptDesc >=", value, "paadmdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptdescLessThan(String value) {
            addCriterion("PAADMDeptDesc <", value, "paadmdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptdescLessThanOrEqualTo(String value) {
            addCriterion("PAADMDeptDesc <=", value, "paadmdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptdescLike(String value) {
            addCriterion("PAADMDeptDesc like", value, "paadmdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptdescNotLike(String value) {
            addCriterion("PAADMDeptDesc not like", value, "paadmdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptdescIn(List<String> values) {
            addCriterion("PAADMDeptDesc in", values, "paadmdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptdescNotIn(List<String> values) {
            addCriterion("PAADMDeptDesc not in", values, "paadmdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptdescBetween(String value1, String value2) {
            addCriterion("PAADMDeptDesc between", value1, value2, "paadmdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdeptdescNotBetween(String value1, String value2) {
            addCriterion("PAADMDeptDesc not between", value1, value2, "paadmdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwardcodeIsNull() {
            addCriterion("PAADMAdmWardCode is null");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwardcodeIsNotNull() {
            addCriterion("PAADMAdmWardCode is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwardcodeEqualTo(String value) {
            addCriterion("PAADMAdmWardCode =", value, "paadmadmwardcode");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwardcodeNotEqualTo(String value) {
            addCriterion("PAADMAdmWardCode <>", value, "paadmadmwardcode");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwardcodeGreaterThan(String value) {
            addCriterion("PAADMAdmWardCode >", value, "paadmadmwardcode");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwardcodeGreaterThanOrEqualTo(String value) {
            addCriterion("PAADMAdmWardCode >=", value, "paadmadmwardcode");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwardcodeLessThan(String value) {
            addCriterion("PAADMAdmWardCode <", value, "paadmadmwardcode");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwardcodeLessThanOrEqualTo(String value) {
            addCriterion("PAADMAdmWardCode <=", value, "paadmadmwardcode");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwardcodeLike(String value) {
            addCriterion("PAADMAdmWardCode like", value, "paadmadmwardcode");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwardcodeNotLike(String value) {
            addCriterion("PAADMAdmWardCode not like", value, "paadmadmwardcode");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwardcodeIn(List<String> values) {
            addCriterion("PAADMAdmWardCode in", values, "paadmadmwardcode");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwardcodeNotIn(List<String> values) {
            addCriterion("PAADMAdmWardCode not in", values, "paadmadmwardcode");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwardcodeBetween(String value1, String value2) {
            addCriterion("PAADMAdmWardCode between", value1, value2, "paadmadmwardcode");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwardcodeNotBetween(String value1, String value2) {
            addCriterion("PAADMAdmWardCode not between", value1, value2, "paadmadmwardcode");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwarddescIsNull() {
            addCriterion("PAADMAdmWardDesc is null");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwarddescIsNotNull() {
            addCriterion("PAADMAdmWardDesc is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwarddescEqualTo(String value) {
            addCriterion("PAADMAdmWardDesc =", value, "paadmadmwarddesc");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwarddescNotEqualTo(String value) {
            addCriterion("PAADMAdmWardDesc <>", value, "paadmadmwarddesc");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwarddescGreaterThan(String value) {
            addCriterion("PAADMAdmWardDesc >", value, "paadmadmwarddesc");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwarddescGreaterThanOrEqualTo(String value) {
            addCriterion("PAADMAdmWardDesc >=", value, "paadmadmwarddesc");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwarddescLessThan(String value) {
            addCriterion("PAADMAdmWardDesc <", value, "paadmadmwarddesc");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwarddescLessThanOrEqualTo(String value) {
            addCriterion("PAADMAdmWardDesc <=", value, "paadmadmwarddesc");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwarddescLike(String value) {
            addCriterion("PAADMAdmWardDesc like", value, "paadmadmwarddesc");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwarddescNotLike(String value) {
            addCriterion("PAADMAdmWardDesc not like", value, "paadmadmwarddesc");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwarddescIn(List<String> values) {
            addCriterion("PAADMAdmWardDesc in", values, "paadmadmwarddesc");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwarddescNotIn(List<String> values) {
            addCriterion("PAADMAdmWardDesc not in", values, "paadmadmwarddesc");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwarddescBetween(String value1, String value2) {
            addCriterion("PAADMAdmWardDesc between", value1, value2, "paadmadmwarddesc");
            return (Criteria) this;
        }

        public Criteria andPaadmadmwarddescNotBetween(String value1, String value2) {
            addCriterion("PAADMAdmWardDesc not between", value1, value2, "paadmadmwarddesc");
            return (Criteria) this;
        }

        public Criteria andPaadmcurbednoIsNull() {
            addCriterion("PAADMCurBedNo is null");
            return (Criteria) this;
        }

        public Criteria andPaadmcurbednoIsNotNull() {
            addCriterion("PAADMCurBedNo is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmcurbednoEqualTo(String value) {
            addCriterion("PAADMCurBedNo =", value, "paadmcurbedno");
            return (Criteria) this;
        }

        public Criteria andPaadmcurbednoNotEqualTo(String value) {
            addCriterion("PAADMCurBedNo <>", value, "paadmcurbedno");
            return (Criteria) this;
        }

        public Criteria andPaadmcurbednoGreaterThan(String value) {
            addCriterion("PAADMCurBedNo >", value, "paadmcurbedno");
            return (Criteria) this;
        }

        public Criteria andPaadmcurbednoGreaterThanOrEqualTo(String value) {
            addCriterion("PAADMCurBedNo >=", value, "paadmcurbedno");
            return (Criteria) this;
        }

        public Criteria andPaadmcurbednoLessThan(String value) {
            addCriterion("PAADMCurBedNo <", value, "paadmcurbedno");
            return (Criteria) this;
        }

        public Criteria andPaadmcurbednoLessThanOrEqualTo(String value) {
            addCriterion("PAADMCurBedNo <=", value, "paadmcurbedno");
            return (Criteria) this;
        }

        public Criteria andPaadmcurbednoLike(String value) {
            addCriterion("PAADMCurBedNo like", value, "paadmcurbedno");
            return (Criteria) this;
        }

        public Criteria andPaadmcurbednoNotLike(String value) {
            addCriterion("PAADMCurBedNo not like", value, "paadmcurbedno");
            return (Criteria) this;
        }

        public Criteria andPaadmcurbednoIn(List<String> values) {
            addCriterion("PAADMCurBedNo in", values, "paadmcurbedno");
            return (Criteria) this;
        }

        public Criteria andPaadmcurbednoNotIn(List<String> values) {
            addCriterion("PAADMCurBedNo not in", values, "paadmcurbedno");
            return (Criteria) this;
        }

        public Criteria andPaadmcurbednoBetween(String value1, String value2) {
            addCriterion("PAADMCurBedNo between", value1, value2, "paadmcurbedno");
            return (Criteria) this;
        }

        public Criteria andPaadmcurbednoNotBetween(String value1, String value2) {
            addCriterion("PAADMCurBedNo not between", value1, value2, "paadmcurbedno");
            return (Criteria) this;
        }

        public Criteria andPaadmhoscodeIsNull() {
            addCriterion("PAADMHosCode is null");
            return (Criteria) this;
        }

        public Criteria andPaadmhoscodeIsNotNull() {
            addCriterion("PAADMHosCode is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmhoscodeEqualTo(String value) {
            addCriterion("PAADMHosCode =", value, "paadmhoscode");
            return (Criteria) this;
        }

        public Criteria andPaadmhoscodeNotEqualTo(String value) {
            addCriterion("PAADMHosCode <>", value, "paadmhoscode");
            return (Criteria) this;
        }

        public Criteria andPaadmhoscodeGreaterThan(String value) {
            addCriterion("PAADMHosCode >", value, "paadmhoscode");
            return (Criteria) this;
        }

        public Criteria andPaadmhoscodeGreaterThanOrEqualTo(String value) {
            addCriterion("PAADMHosCode >=", value, "paadmhoscode");
            return (Criteria) this;
        }

        public Criteria andPaadmhoscodeLessThan(String value) {
            addCriterion("PAADMHosCode <", value, "paadmhoscode");
            return (Criteria) this;
        }

        public Criteria andPaadmhoscodeLessThanOrEqualTo(String value) {
            addCriterion("PAADMHosCode <=", value, "paadmhoscode");
            return (Criteria) this;
        }

        public Criteria andPaadmhoscodeLike(String value) {
            addCriterion("PAADMHosCode like", value, "paadmhoscode");
            return (Criteria) this;
        }

        public Criteria andPaadmhoscodeNotLike(String value) {
            addCriterion("PAADMHosCode not like", value, "paadmhoscode");
            return (Criteria) this;
        }

        public Criteria andPaadmhoscodeIn(List<String> values) {
            addCriterion("PAADMHosCode in", values, "paadmhoscode");
            return (Criteria) this;
        }

        public Criteria andPaadmhoscodeNotIn(List<String> values) {
            addCriterion("PAADMHosCode not in", values, "paadmhoscode");
            return (Criteria) this;
        }

        public Criteria andPaadmhoscodeBetween(String value1, String value2) {
            addCriterion("PAADMHosCode between", value1, value2, "paadmhoscode");
            return (Criteria) this;
        }

        public Criteria andPaadmhoscodeNotBetween(String value1, String value2) {
            addCriterion("PAADMHosCode not between", value1, value2, "paadmhoscode");
            return (Criteria) this;
        }

        public Criteria andPaadmdoccodeIsNull() {
            addCriterion("PAADMDocCode is null");
            return (Criteria) this;
        }

        public Criteria andPaadmdoccodeIsNotNull() {
            addCriterion("PAADMDocCode is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmdoccodeEqualTo(String value) {
            addCriterion("PAADMDocCode =", value, "paadmdoccode");
            return (Criteria) this;
        }

        public Criteria andPaadmdoccodeNotEqualTo(String value) {
            addCriterion("PAADMDocCode <>", value, "paadmdoccode");
            return (Criteria) this;
        }

        public Criteria andPaadmdoccodeGreaterThan(String value) {
            addCriterion("PAADMDocCode >", value, "paadmdoccode");
            return (Criteria) this;
        }

        public Criteria andPaadmdoccodeGreaterThanOrEqualTo(String value) {
            addCriterion("PAADMDocCode >=", value, "paadmdoccode");
            return (Criteria) this;
        }

        public Criteria andPaadmdoccodeLessThan(String value) {
            addCriterion("PAADMDocCode <", value, "paadmdoccode");
            return (Criteria) this;
        }

        public Criteria andPaadmdoccodeLessThanOrEqualTo(String value) {
            addCriterion("PAADMDocCode <=", value, "paadmdoccode");
            return (Criteria) this;
        }

        public Criteria andPaadmdoccodeLike(String value) {
            addCriterion("PAADMDocCode like", value, "paadmdoccode");
            return (Criteria) this;
        }

        public Criteria andPaadmdoccodeNotLike(String value) {
            addCriterion("PAADMDocCode not like", value, "paadmdoccode");
            return (Criteria) this;
        }

        public Criteria andPaadmdoccodeIn(List<String> values) {
            addCriterion("PAADMDocCode in", values, "paadmdoccode");
            return (Criteria) this;
        }

        public Criteria andPaadmdoccodeNotIn(List<String> values) {
            addCriterion("PAADMDocCode not in", values, "paadmdoccode");
            return (Criteria) this;
        }

        public Criteria andPaadmdoccodeBetween(String value1, String value2) {
            addCriterion("PAADMDocCode between", value1, value2, "paadmdoccode");
            return (Criteria) this;
        }

        public Criteria andPaadmdoccodeNotBetween(String value1, String value2) {
            addCriterion("PAADMDocCode not between", value1, value2, "paadmdoccode");
            return (Criteria) this;
        }

        public Criteria andPaadmdocdescIsNull() {
            addCriterion("PAADMDocDesc is null");
            return (Criteria) this;
        }

        public Criteria andPaadmdocdescIsNotNull() {
            addCriterion("PAADMDocDesc is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmdocdescEqualTo(String value) {
            addCriterion("PAADMDocDesc =", value, "paadmdocdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdocdescNotEqualTo(String value) {
            addCriterion("PAADMDocDesc <>", value, "paadmdocdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdocdescGreaterThan(String value) {
            addCriterion("PAADMDocDesc >", value, "paadmdocdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdocdescGreaterThanOrEqualTo(String value) {
            addCriterion("PAADMDocDesc >=", value, "paadmdocdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdocdescLessThan(String value) {
            addCriterion("PAADMDocDesc <", value, "paadmdocdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdocdescLessThanOrEqualTo(String value) {
            addCriterion("PAADMDocDesc <=", value, "paadmdocdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdocdescLike(String value) {
            addCriterion("PAADMDocDesc like", value, "paadmdocdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdocdescNotLike(String value) {
            addCriterion("PAADMDocDesc not like", value, "paadmdocdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdocdescIn(List<String> values) {
            addCriterion("PAADMDocDesc in", values, "paadmdocdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdocdescNotIn(List<String> values) {
            addCriterion("PAADMDocDesc not in", values, "paadmdocdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdocdescBetween(String value1, String value2) {
            addCriterion("PAADMDocDesc between", value1, value2, "paadmdocdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmdocdescNotBetween(String value1, String value2) {
            addCriterion("PAADMDocDesc not between", value1, value2, "paadmdocdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmstartdateIsNull() {
            addCriterion("PAADMStartDate is null");
            return (Criteria) this;
        }

        public Criteria andPaadmstartdateIsNotNull() {
            addCriterion("PAADMStartDate is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmstartdateEqualTo(Date value) {
            addCriterionForJDBCDate("PAADMStartDate =", value, "paadmstartdate");
            return (Criteria) this;
        }

        public Criteria andPaadmstartdateNotEqualTo(Date value) {
            addCriterionForJDBCDate("PAADMStartDate <>", value, "paadmstartdate");
            return (Criteria) this;
        }

        public Criteria andPaadmstartdateGreaterThan(Date value) {
            addCriterionForJDBCDate("PAADMStartDate >", value, "paadmstartdate");
            return (Criteria) this;
        }

        public Criteria andPaadmstartdateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("PAADMStartDate >=", value, "paadmstartdate");
            return (Criteria) this;
        }

        public Criteria andPaadmstartdateLessThan(Date value) {
            addCriterionForJDBCDate("PAADMStartDate <", value, "paadmstartdate");
            return (Criteria) this;
        }

        public Criteria andPaadmstartdateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("PAADMStartDate <=", value, "paadmstartdate");
            return (Criteria) this;
        }

        public Criteria andPaadmstartdateIn(List<Date> values) {
            addCriterionForJDBCDate("PAADMStartDate in", values, "paadmstartdate");
            return (Criteria) this;
        }

        public Criteria andPaadmstartdateNotIn(List<Date> values) {
            addCriterionForJDBCDate("PAADMStartDate not in", values, "paadmstartdate");
            return (Criteria) this;
        }

        public Criteria andPaadmstartdateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("PAADMStartDate between", value1, value2, "paadmstartdate");
            return (Criteria) this;
        }

        public Criteria andPaadmstartdateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("PAADMStartDate not between", value1, value2, "paadmstartdate");
            return (Criteria) this;
        }

        public Criteria andPaadmstarttimeIsNull() {
            addCriterion("PAADMStartTime is null");
            return (Criteria) this;
        }

        public Criteria andPaadmstarttimeIsNotNull() {
            addCriterion("PAADMStartTime is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmstarttimeEqualTo(Date value) {
            addCriterionForJDBCTime("PAADMStartTime =", value, "paadmstarttime");
            return (Criteria) this;
        }

        public Criteria andPaadmstarttimeNotEqualTo(Date value) {
            addCriterionForJDBCTime("PAADMStartTime <>", value, "paadmstarttime");
            return (Criteria) this;
        }

        public Criteria andPaadmstarttimeGreaterThan(Date value) {
            addCriterionForJDBCTime("PAADMStartTime >", value, "paadmstarttime");
            return (Criteria) this;
        }

        public Criteria andPaadmstarttimeGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("PAADMStartTime >=", value, "paadmstarttime");
            return (Criteria) this;
        }

        public Criteria andPaadmstarttimeLessThan(Date value) {
            addCriterionForJDBCTime("PAADMStartTime <", value, "paadmstarttime");
            return (Criteria) this;
        }

        public Criteria andPaadmstarttimeLessThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("PAADMStartTime <=", value, "paadmstarttime");
            return (Criteria) this;
        }

        public Criteria andPaadmstarttimeIn(List<Date> values) {
            addCriterionForJDBCTime("PAADMStartTime in", values, "paadmstarttime");
            return (Criteria) this;
        }

        public Criteria andPaadmstarttimeNotIn(List<Date> values) {
            addCriterionForJDBCTime("PAADMStartTime not in", values, "paadmstarttime");
            return (Criteria) this;
        }

        public Criteria andPaadmstarttimeBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("PAADMStartTime between", value1, value2, "paadmstarttime");
            return (Criteria) this;
        }

        public Criteria andPaadmstarttimeNotBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("PAADMStartTime not between", value1, value2, "paadmstarttime");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptcodeIsNull() {
            addCriterion("PAADMCurDeptCode is null");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptcodeIsNotNull() {
            addCriterion("PAADMCurDeptCode is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptcodeEqualTo(String value) {
            addCriterion("PAADMCurDeptCode =", value, "paadmcurdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptcodeNotEqualTo(String value) {
            addCriterion("PAADMCurDeptCode <>", value, "paadmcurdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptcodeGreaterThan(String value) {
            addCriterion("PAADMCurDeptCode >", value, "paadmcurdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptcodeGreaterThanOrEqualTo(String value) {
            addCriterion("PAADMCurDeptCode >=", value, "paadmcurdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptcodeLessThan(String value) {
            addCriterion("PAADMCurDeptCode <", value, "paadmcurdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptcodeLessThanOrEqualTo(String value) {
            addCriterion("PAADMCurDeptCode <=", value, "paadmcurdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptcodeLike(String value) {
            addCriterion("PAADMCurDeptCode like", value, "paadmcurdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptcodeNotLike(String value) {
            addCriterion("PAADMCurDeptCode not like", value, "paadmcurdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptcodeIn(List<String> values) {
            addCriterion("PAADMCurDeptCode in", values, "paadmcurdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptcodeNotIn(List<String> values) {
            addCriterion("PAADMCurDeptCode not in", values, "paadmcurdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptcodeBetween(String value1, String value2) {
            addCriterion("PAADMCurDeptCode between", value1, value2, "paadmcurdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptcodeNotBetween(String value1, String value2) {
            addCriterion("PAADMCurDeptCode not between", value1, value2, "paadmcurdeptcode");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptdescIsNull() {
            addCriterion("PAADMCurDeptDesc is null");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptdescIsNotNull() {
            addCriterion("PAADMCurDeptDesc is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptdescEqualTo(String value) {
            addCriterion("PAADMCurDeptDesc =", value, "paadmcurdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptdescNotEqualTo(String value) {
            addCriterion("PAADMCurDeptDesc <>", value, "paadmcurdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptdescGreaterThan(String value) {
            addCriterion("PAADMCurDeptDesc >", value, "paadmcurdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptdescGreaterThanOrEqualTo(String value) {
            addCriterion("PAADMCurDeptDesc >=", value, "paadmcurdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptdescLessThan(String value) {
            addCriterion("PAADMCurDeptDesc <", value, "paadmcurdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptdescLessThanOrEqualTo(String value) {
            addCriterion("PAADMCurDeptDesc <=", value, "paadmcurdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptdescLike(String value) {
            addCriterion("PAADMCurDeptDesc like", value, "paadmcurdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptdescNotLike(String value) {
            addCriterion("PAADMCurDeptDesc not like", value, "paadmcurdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptdescIn(List<String> values) {
            addCriterion("PAADMCurDeptDesc in", values, "paadmcurdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptdescNotIn(List<String> values) {
            addCriterion("PAADMCurDeptDesc not in", values, "paadmcurdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptdescBetween(String value1, String value2) {
            addCriterion("PAADMCurDeptDesc between", value1, value2, "paadmcurdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmcurdeptdescNotBetween(String value1, String value2) {
            addCriterion("PAADMCurDeptDesc not between", value1, value2, "paadmcurdeptdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmstatuscodeIsNull() {
            addCriterion("PAAdmStatusCode is null");
            return (Criteria) this;
        }

        public Criteria andPaadmstatuscodeIsNotNull() {
            addCriterion("PAAdmStatusCode is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmstatuscodeEqualTo(String value) {
            addCriterion("PAAdmStatusCode =", value, "paadmstatuscode");
            return (Criteria) this;
        }

        public Criteria andPaadmstatuscodeNotEqualTo(String value) {
            addCriterion("PAAdmStatusCode <>", value, "paadmstatuscode");
            return (Criteria) this;
        }

        public Criteria andPaadmstatuscodeGreaterThan(String value) {
            addCriterion("PAAdmStatusCode >", value, "paadmstatuscode");
            return (Criteria) this;
        }

        public Criteria andPaadmstatuscodeGreaterThanOrEqualTo(String value) {
            addCriterion("PAAdmStatusCode >=", value, "paadmstatuscode");
            return (Criteria) this;
        }

        public Criteria andPaadmstatuscodeLessThan(String value) {
            addCriterion("PAAdmStatusCode <", value, "paadmstatuscode");
            return (Criteria) this;
        }

        public Criteria andPaadmstatuscodeLessThanOrEqualTo(String value) {
            addCriterion("PAAdmStatusCode <=", value, "paadmstatuscode");
            return (Criteria) this;
        }

        public Criteria andPaadmstatuscodeLike(String value) {
            addCriterion("PAAdmStatusCode like", value, "paadmstatuscode");
            return (Criteria) this;
        }

        public Criteria andPaadmstatuscodeNotLike(String value) {
            addCriterion("PAAdmStatusCode not like", value, "paadmstatuscode");
            return (Criteria) this;
        }

        public Criteria andPaadmstatuscodeIn(List<String> values) {
            addCriterion("PAAdmStatusCode in", values, "paadmstatuscode");
            return (Criteria) this;
        }

        public Criteria andPaadmstatuscodeNotIn(List<String> values) {
            addCriterion("PAAdmStatusCode not in", values, "paadmstatuscode");
            return (Criteria) this;
        }

        public Criteria andPaadmstatuscodeBetween(String value1, String value2) {
            addCriterion("PAAdmStatusCode between", value1, value2, "paadmstatuscode");
            return (Criteria) this;
        }

        public Criteria andPaadmstatuscodeNotBetween(String value1, String value2) {
            addCriterion("PAAdmStatusCode not between", value1, value2, "paadmstatuscode");
            return (Criteria) this;
        }

        public Criteria andPaadmstatusdescIsNull() {
            addCriterion("PAAdmStatusDesc is null");
            return (Criteria) this;
        }

        public Criteria andPaadmstatusdescIsNotNull() {
            addCriterion("PAAdmStatusDesc is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmstatusdescEqualTo(String value) {
            addCriterion("PAAdmStatusDesc =", value, "paadmstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmstatusdescNotEqualTo(String value) {
            addCriterion("PAAdmStatusDesc <>", value, "paadmstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmstatusdescGreaterThan(String value) {
            addCriterion("PAAdmStatusDesc >", value, "paadmstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmstatusdescGreaterThanOrEqualTo(String value) {
            addCriterion("PAAdmStatusDesc >=", value, "paadmstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmstatusdescLessThan(String value) {
            addCriterion("PAAdmStatusDesc <", value, "paadmstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmstatusdescLessThanOrEqualTo(String value) {
            addCriterion("PAAdmStatusDesc <=", value, "paadmstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmstatusdescLike(String value) {
            addCriterion("PAAdmStatusDesc like", value, "paadmstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmstatusdescNotLike(String value) {
            addCriterion("PAAdmStatusDesc not like", value, "paadmstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmstatusdescIn(List<String> values) {
            addCriterion("PAAdmStatusDesc in", values, "paadmstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmstatusdescNotIn(List<String> values) {
            addCriterion("PAAdmStatusDesc not in", values, "paadmstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmstatusdescBetween(String value1, String value2) {
            addCriterion("PAAdmStatusDesc between", value1, value2, "paadmstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmstatusdescNotBetween(String value1, String value2) {
            addCriterion("PAAdmStatusDesc not between", value1, value2, "paadmstatusdesc");
            return (Criteria) this;
        }

        public Criteria andPaadmtypecodeIsNull() {
            addCriterion("PAADMTypeCode is null");
            return (Criteria) this;
        }

        public Criteria andPaadmtypecodeIsNotNull() {
            addCriterion("PAADMTypeCode is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmtypecodeEqualTo(String value) {
            addCriterion("PAADMTypeCode =", value, "paadmtypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmtypecodeNotEqualTo(String value) {
            addCriterion("PAADMTypeCode <>", value, "paadmtypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmtypecodeGreaterThan(String value) {
            addCriterion("PAADMTypeCode >", value, "paadmtypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmtypecodeGreaterThanOrEqualTo(String value) {
            addCriterion("PAADMTypeCode >=", value, "paadmtypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmtypecodeLessThan(String value) {
            addCriterion("PAADMTypeCode <", value, "paadmtypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmtypecodeLessThanOrEqualTo(String value) {
            addCriterion("PAADMTypeCode <=", value, "paadmtypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmtypecodeLike(String value) {
            addCriterion("PAADMTypeCode like", value, "paadmtypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmtypecodeNotLike(String value) {
            addCriterion("PAADMTypeCode not like", value, "paadmtypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmtypecodeIn(List<String> values) {
            addCriterion("PAADMTypeCode in", values, "paadmtypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmtypecodeNotIn(List<String> values) {
            addCriterion("PAADMTypeCode not in", values, "paadmtypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmtypecodeBetween(String value1, String value2) {
            addCriterion("PAADMTypeCode between", value1, value2, "paadmtypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmtypecodeNotBetween(String value1, String value2) {
            addCriterion("PAADMTypeCode not between", value1, value2, "paadmtypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmtypedescIsNull() {
            addCriterion("PAADMTypeDesc is null");
            return (Criteria) this;
        }

        public Criteria andPaadmtypedescIsNotNull() {
            addCriterion("PAADMTypeDesc is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmtypedescEqualTo(String value) {
            addCriterion("PAADMTypeDesc =", value, "paadmtypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmtypedescNotEqualTo(String value) {
            addCriterion("PAADMTypeDesc <>", value, "paadmtypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmtypedescGreaterThan(String value) {
            addCriterion("PAADMTypeDesc >", value, "paadmtypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmtypedescGreaterThanOrEqualTo(String value) {
            addCriterion("PAADMTypeDesc >=", value, "paadmtypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmtypedescLessThan(String value) {
            addCriterion("PAADMTypeDesc <", value, "paadmtypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmtypedescLessThanOrEqualTo(String value) {
            addCriterion("PAADMTypeDesc <=", value, "paadmtypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmtypedescLike(String value) {
            addCriterion("PAADMTypeDesc like", value, "paadmtypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmtypedescNotLike(String value) {
            addCriterion("PAADMTypeDesc not like", value, "paadmtypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmtypedescIn(List<String> values) {
            addCriterion("PAADMTypeDesc in", values, "paadmtypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmtypedescNotIn(List<String> values) {
            addCriterion("PAADMTypeDesc not in", values, "paadmtypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmtypedescBetween(String value1, String value2) {
            addCriterion("PAADMTypeDesc between", value1, value2, "paadmtypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmtypedescNotBetween(String value1, String value2) {
            addCriterion("PAADMTypeDesc not between", value1, value2, "paadmtypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypecodeIsNull() {
            addCriterion("PAADMFeeTypeCode is null");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypecodeIsNotNull() {
            addCriterion("PAADMFeeTypeCode is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypecodeEqualTo(String value) {
            addCriterion("PAADMFeeTypeCode =", value, "paadmfeetypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypecodeNotEqualTo(String value) {
            addCriterion("PAADMFeeTypeCode <>", value, "paadmfeetypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypecodeGreaterThan(String value) {
            addCriterion("PAADMFeeTypeCode >", value, "paadmfeetypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypecodeGreaterThanOrEqualTo(String value) {
            addCriterion("PAADMFeeTypeCode >=", value, "paadmfeetypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypecodeLessThan(String value) {
            addCriterion("PAADMFeeTypeCode <", value, "paadmfeetypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypecodeLessThanOrEqualTo(String value) {
            addCriterion("PAADMFeeTypeCode <=", value, "paadmfeetypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypecodeLike(String value) {
            addCriterion("PAADMFeeTypeCode like", value, "paadmfeetypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypecodeNotLike(String value) {
            addCriterion("PAADMFeeTypeCode not like", value, "paadmfeetypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypecodeIn(List<String> values) {
            addCriterion("PAADMFeeTypeCode in", values, "paadmfeetypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypecodeNotIn(List<String> values) {
            addCriterion("PAADMFeeTypeCode not in", values, "paadmfeetypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypecodeBetween(String value1, String value2) {
            addCriterion("PAADMFeeTypeCode between", value1, value2, "paadmfeetypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypecodeNotBetween(String value1, String value2) {
            addCriterion("PAADMFeeTypeCode not between", value1, value2, "paadmfeetypecode");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypedescIsNull() {
            addCriterion("PAADMFeeTypeDesc is null");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypedescIsNotNull() {
            addCriterion("PAADMFeeTypeDesc is not null");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypedescEqualTo(String value) {
            addCriterion("PAADMFeeTypeDesc =", value, "paadmfeetypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypedescNotEqualTo(String value) {
            addCriterion("PAADMFeeTypeDesc <>", value, "paadmfeetypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypedescGreaterThan(String value) {
            addCriterion("PAADMFeeTypeDesc >", value, "paadmfeetypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypedescGreaterThanOrEqualTo(String value) {
            addCriterion("PAADMFeeTypeDesc >=", value, "paadmfeetypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypedescLessThan(String value) {
            addCriterion("PAADMFeeTypeDesc <", value, "paadmfeetypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypedescLessThanOrEqualTo(String value) {
            addCriterion("PAADMFeeTypeDesc <=", value, "paadmfeetypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypedescLike(String value) {
            addCriterion("PAADMFeeTypeDesc like", value, "paadmfeetypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypedescNotLike(String value) {
            addCriterion("PAADMFeeTypeDesc not like", value, "paadmfeetypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypedescIn(List<String> values) {
            addCriterion("PAADMFeeTypeDesc in", values, "paadmfeetypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypedescNotIn(List<String> values) {
            addCriterion("PAADMFeeTypeDesc not in", values, "paadmfeetypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypedescBetween(String value1, String value2) {
            addCriterion("PAADMFeeTypeDesc between", value1, value2, "paadmfeetypedesc");
            return (Criteria) this;
        }

        public Criteria andPaadmfeetypedescNotBetween(String value1, String value2) {
            addCriterion("PAADMFeeTypeDesc not between", value1, value2, "paadmfeetypedesc");
            return (Criteria) this;
        }

        public Criteria andChargerIsNull() {
            addCriterion("Charger is null");
            return (Criteria) this;
        }

        public Criteria andChargerIsNotNull() {
            addCriterion("Charger is not null");
            return (Criteria) this;
        }

        public Criteria andChargerEqualTo(String value) {
            addCriterion("Charger =", value, "charger");
            return (Criteria) this;
        }

        public Criteria andChargerNotEqualTo(String value) {
            addCriterion("Charger <>", value, "charger");
            return (Criteria) this;
        }

        public Criteria andChargerGreaterThan(String value) {
            addCriterion("Charger >", value, "charger");
            return (Criteria) this;
        }

        public Criteria andChargerGreaterThanOrEqualTo(String value) {
            addCriterion("Charger >=", value, "charger");
            return (Criteria) this;
        }

        public Criteria andChargerLessThan(String value) {
            addCriterion("Charger <", value, "charger");
            return (Criteria) this;
        }

        public Criteria andChargerLessThanOrEqualTo(String value) {
            addCriterion("Charger <=", value, "charger");
            return (Criteria) this;
        }

        public Criteria andChargerLike(String value) {
            addCriterion("Charger like", value, "charger");
            return (Criteria) this;
        }

        public Criteria andChargerNotLike(String value) {
            addCriterion("Charger not like", value, "charger");
            return (Criteria) this;
        }

        public Criteria andChargerIn(List<String> values) {
            addCriterion("Charger in", values, "charger");
            return (Criteria) this;
        }

        public Criteria andChargerNotIn(List<String> values) {
            addCriterion("Charger not in", values, "charger");
            return (Criteria) this;
        }

        public Criteria andChargerBetween(String value1, String value2) {
            addCriterion("Charger between", value1, value2, "charger");
            return (Criteria) this;
        }

        public Criteria andChargerNotBetween(String value1, String value2) {
            addCriterion("Charger not between", value1, value2, "charger");
            return (Criteria) this;
        }

        public Criteria andChargedateIsNull() {
            addCriterion("ChargeDate is null");
            return (Criteria) this;
        }

        public Criteria andChargedateIsNotNull() {
            addCriterion("ChargeDate is not null");
            return (Criteria) this;
        }

        public Criteria andChargedateEqualTo(Date value) {
            addCriterion("ChargeDate =", value, "chargedate");
            return (Criteria) this;
        }

        public Criteria andChargedateNotEqualTo(Date value) {
            addCriterion("ChargeDate <>", value, "chargedate");
            return (Criteria) this;
        }

        public Criteria andChargedateGreaterThan(Date value) {
            addCriterion("ChargeDate >", value, "chargedate");
            return (Criteria) this;
        }

        public Criteria andChargedateGreaterThanOrEqualTo(Date value) {
            addCriterion("ChargeDate >=", value, "chargedate");
            return (Criteria) this;
        }

        public Criteria andChargedateLessThan(Date value) {
            addCriterion("ChargeDate <", value, "chargedate");
            return (Criteria) this;
        }

        public Criteria andChargedateLessThanOrEqualTo(Date value) {
            addCriterion("ChargeDate <=", value, "chargedate");
            return (Criteria) this;
        }

        public Criteria andChargedateIn(List<Date> values) {
            addCriterion("ChargeDate in", values, "chargedate");
            return (Criteria) this;
        }

        public Criteria andChargedateNotIn(List<Date> values) {
            addCriterion("ChargeDate not in", values, "chargedate");
            return (Criteria) this;
        }

        public Criteria andChargedateBetween(Date value1, Date value2) {
            addCriterion("ChargeDate between", value1, value2, "chargedate");
            return (Criteria) this;
        }

        public Criteria andChargedateNotBetween(Date value1, Date value2) {
            addCriterion("ChargeDate not between", value1, value2, "chargedate");
            return (Criteria) this;
        }

        public Criteria andRisrexamidIsNull() {
            addCriterion("RISRExamID is null");
            return (Criteria) this;
        }

        public Criteria andRisrexamidIsNotNull() {
            addCriterion("RISRExamID is not null");
            return (Criteria) this;
        }

        public Criteria andRisrexamidEqualTo(String value) {
            addCriterion("RISRExamID =", value, "risrexamid");
            return (Criteria) this;
        }

        public Criteria andRisrexamidNotEqualTo(String value) {
            addCriterion("RISRExamID <>", value, "risrexamid");
            return (Criteria) this;
        }

        public Criteria andRisrexamidGreaterThan(String value) {
            addCriterion("RISRExamID >", value, "risrexamid");
            return (Criteria) this;
        }

        public Criteria andRisrexamidGreaterThanOrEqualTo(String value) {
            addCriterion("RISRExamID >=", value, "risrexamid");
            return (Criteria) this;
        }

        public Criteria andRisrexamidLessThan(String value) {
            addCriterion("RISRExamID <", value, "risrexamid");
            return (Criteria) this;
        }

        public Criteria andRisrexamidLessThanOrEqualTo(String value) {
            addCriterion("RISRExamID <=", value, "risrexamid");
            return (Criteria) this;
        }

        public Criteria andRisrexamidLike(String value) {
            addCriterion("RISRExamID like", value, "risrexamid");
            return (Criteria) this;
        }

        public Criteria andRisrexamidNotLike(String value) {
            addCriterion("RISRExamID not like", value, "risrexamid");
            return (Criteria) this;
        }

        public Criteria andRisrexamidIn(List<String> values) {
            addCriterion("RISRExamID in", values, "risrexamid");
            return (Criteria) this;
        }

        public Criteria andRisrexamidNotIn(List<String> values) {
            addCriterion("RISRExamID not in", values, "risrexamid");
            return (Criteria) this;
        }

        public Criteria andRisrexamidBetween(String value1, String value2) {
            addCriterion("RISRExamID between", value1, value2, "risrexamid");
            return (Criteria) this;
        }

        public Criteria andRisrexamidNotBetween(String value1, String value2) {
            addCriterion("RISRExamID not between", value1, value2, "risrexamid");
            return (Criteria) this;
        }

        public Criteria andRisrarexareqsymIsNull() {
            addCriterion("RISRArExaReqSym is null");
            return (Criteria) this;
        }

        public Criteria andRisrarexareqsymIsNotNull() {
            addCriterion("RISRArExaReqSym is not null");
            return (Criteria) this;
        }

        public Criteria andRisrarexareqsymEqualTo(String value) {
            addCriterion("RISRArExaReqSym =", value, "risrarexareqsym");
            return (Criteria) this;
        }

        public Criteria andRisrarexareqsymNotEqualTo(String value) {
            addCriterion("RISRArExaReqSym <>", value, "risrarexareqsym");
            return (Criteria) this;
        }

        public Criteria andRisrarexareqsymGreaterThan(String value) {
            addCriterion("RISRArExaReqSym >", value, "risrarexareqsym");
            return (Criteria) this;
        }

        public Criteria andRisrarexareqsymGreaterThanOrEqualTo(String value) {
            addCriterion("RISRArExaReqSym >=", value, "risrarexareqsym");
            return (Criteria) this;
        }

        public Criteria andRisrarexareqsymLessThan(String value) {
            addCriterion("RISRArExaReqSym <", value, "risrarexareqsym");
            return (Criteria) this;
        }

        public Criteria andRisrarexareqsymLessThanOrEqualTo(String value) {
            addCriterion("RISRArExaReqSym <=", value, "risrarexareqsym");
            return (Criteria) this;
        }

        public Criteria andRisrarexareqsymLike(String value) {
            addCriterion("RISRArExaReqSym like", value, "risrarexareqsym");
            return (Criteria) this;
        }

        public Criteria andRisrarexareqsymNotLike(String value) {
            addCriterion("RISRArExaReqSym not like", value, "risrarexareqsym");
            return (Criteria) this;
        }

        public Criteria andRisrarexareqsymIn(List<String> values) {
            addCriterion("RISRArExaReqSym in", values, "risrarexareqsym");
            return (Criteria) this;
        }

        public Criteria andRisrarexareqsymNotIn(List<String> values) {
            addCriterion("RISRArExaReqSym not in", values, "risrarexareqsym");
            return (Criteria) this;
        }

        public Criteria andRisrarexareqsymBetween(String value1, String value2) {
            addCriterion("RISRArExaReqSym between", value1, value2, "risrarexareqsym");
            return (Criteria) this;
        }

        public Criteria andRisrarexareqsymNotBetween(String value1, String value2) {
            addCriterion("RISRArExaReqSym not between", value1, value2, "risrarexareqsym");
            return (Criteria) this;
        }

        public Criteria andRisrarpurposeIsNull() {
            addCriterion("RISRArPurpose is null");
            return (Criteria) this;
        }

        public Criteria andRisrarpurposeIsNotNull() {
            addCriterion("RISRArPurpose is not null");
            return (Criteria) this;
        }

        public Criteria andRisrarpurposeEqualTo(String value) {
            addCriterion("RISRArPurpose =", value, "risrarpurpose");
            return (Criteria) this;
        }

        public Criteria andRisrarpurposeNotEqualTo(String value) {
            addCriterion("RISRArPurpose <>", value, "risrarpurpose");
            return (Criteria) this;
        }

        public Criteria andRisrarpurposeGreaterThan(String value) {
            addCriterion("RISRArPurpose >", value, "risrarpurpose");
            return (Criteria) this;
        }

        public Criteria andRisrarpurposeGreaterThanOrEqualTo(String value) {
            addCriterion("RISRArPurpose >=", value, "risrarpurpose");
            return (Criteria) this;
        }

        public Criteria andRisrarpurposeLessThan(String value) {
            addCriterion("RISRArPurpose <", value, "risrarpurpose");
            return (Criteria) this;
        }

        public Criteria andRisrarpurposeLessThanOrEqualTo(String value) {
            addCriterion("RISRArPurpose <=", value, "risrarpurpose");
            return (Criteria) this;
        }

        public Criteria andRisrarpurposeLike(String value) {
            addCriterion("RISRArPurpose like", value, "risrarpurpose");
            return (Criteria) this;
        }

        public Criteria andRisrarpurposeNotLike(String value) {
            addCriterion("RISRArPurpose not like", value, "risrarpurpose");
            return (Criteria) this;
        }

        public Criteria andRisrarpurposeIn(List<String> values) {
            addCriterion("RISRArPurpose in", values, "risrarpurpose");
            return (Criteria) this;
        }

        public Criteria andRisrarpurposeNotIn(List<String> values) {
            addCriterion("RISRArPurpose not in", values, "risrarpurpose");
            return (Criteria) this;
        }

        public Criteria andRisrarpurposeBetween(String value1, String value2) {
            addCriterion("RISRArPurpose between", value1, value2, "risrarpurpose");
            return (Criteria) this;
        }

        public Criteria andRisrarpurposeNotBetween(String value1, String value2) {
            addCriterion("RISRArPurpose not between", value1, value2, "risrarpurpose");
            return (Criteria) this;
        }

        public Criteria andRisrsystemtypeIsNull() {
            addCriterion("RISRSystemType is null");
            return (Criteria) this;
        }

        public Criteria andRisrsystemtypeIsNotNull() {
            addCriterion("RISRSystemType is not null");
            return (Criteria) this;
        }

        public Criteria andRisrsystemtypeEqualTo(String value) {
            addCriterion("RISRSystemType =", value, "risrsystemtype");
            return (Criteria) this;
        }

        public Criteria andRisrsystemtypeNotEqualTo(String value) {
            addCriterion("RISRSystemType <>", value, "risrsystemtype");
            return (Criteria) this;
        }

        public Criteria andRisrsystemtypeGreaterThan(String value) {
            addCriterion("RISRSystemType >", value, "risrsystemtype");
            return (Criteria) this;
        }

        public Criteria andRisrsystemtypeGreaterThanOrEqualTo(String value) {
            addCriterion("RISRSystemType >=", value, "risrsystemtype");
            return (Criteria) this;
        }

        public Criteria andRisrsystemtypeLessThan(String value) {
            addCriterion("RISRSystemType <", value, "risrsystemtype");
            return (Criteria) this;
        }

        public Criteria andRisrsystemtypeLessThanOrEqualTo(String value) {
            addCriterion("RISRSystemType <=", value, "risrsystemtype");
            return (Criteria) this;
        }

        public Criteria andRisrsystemtypeLike(String value) {
            addCriterion("RISRSystemType like", value, "risrsystemtype");
            return (Criteria) this;
        }

        public Criteria andRisrsystemtypeNotLike(String value) {
            addCriterion("RISRSystemType not like", value, "risrsystemtype");
            return (Criteria) this;
        }

        public Criteria andRisrsystemtypeIn(List<String> values) {
            addCriterion("RISRSystemType in", values, "risrsystemtype");
            return (Criteria) this;
        }

        public Criteria andRisrsystemtypeNotIn(List<String> values) {
            addCriterion("RISRSystemType not in", values, "risrsystemtype");
            return (Criteria) this;
        }

        public Criteria andRisrsystemtypeBetween(String value1, String value2) {
            addCriterion("RISRSystemType between", value1, value2, "risrsystemtype");
            return (Criteria) this;
        }

        public Criteria andRisrsystemtypeNotBetween(String value1, String value2) {
            addCriterion("RISRSystemType not between", value1, value2, "risrsystemtype");
            return (Criteria) this;
        }

        public Criteria andRisrappnumIsNull() {
            addCriterion("RISRAppNum is null");
            return (Criteria) this;
        }

        public Criteria andRisrappnumIsNotNull() {
            addCriterion("RISRAppNum is not null");
            return (Criteria) this;
        }

        public Criteria andRisrappnumEqualTo(String value) {
            addCriterion("RISRAppNum =", value, "risrappnum");
            return (Criteria) this;
        }

        public Criteria andRisrappnumNotEqualTo(String value) {
            addCriterion("RISRAppNum <>", value, "risrappnum");
            return (Criteria) this;
        }

        public Criteria andRisrappnumGreaterThan(String value) {
            addCriterion("RISRAppNum >", value, "risrappnum");
            return (Criteria) this;
        }

        public Criteria andRisrappnumGreaterThanOrEqualTo(String value) {
            addCriterion("RISRAppNum >=", value, "risrappnum");
            return (Criteria) this;
        }

        public Criteria andRisrappnumLessThan(String value) {
            addCriterion("RISRAppNum <", value, "risrappnum");
            return (Criteria) this;
        }

        public Criteria andRisrappnumLessThanOrEqualTo(String value) {
            addCriterion("RISRAppNum <=", value, "risrappnum");
            return (Criteria) this;
        }

        public Criteria andRisrappnumLike(String value) {
            addCriterion("RISRAppNum like", value, "risrappnum");
            return (Criteria) this;
        }

        public Criteria andRisrappnumNotLike(String value) {
            addCriterion("RISRAppNum not like", value, "risrappnum");
            return (Criteria) this;
        }

        public Criteria andRisrappnumIn(List<String> values) {
            addCriterion("RISRAppNum in", values, "risrappnum");
            return (Criteria) this;
        }

        public Criteria andRisrappnumNotIn(List<String> values) {
            addCriterion("RISRAppNum not in", values, "risrappnum");
            return (Criteria) this;
        }

        public Criteria andRisrappnumBetween(String value1, String value2) {
            addCriterion("RISRAppNum between", value1, value2, "risrappnum");
            return (Criteria) this;
        }

        public Criteria andRisrappnumNotBetween(String value1, String value2) {
            addCriterion("RISRAppNum not between", value1, value2, "risrappnum");
            return (Criteria) this;
        }

        public Criteria andAppdeptcodeIsNull() {
            addCriterion("AppDeptCode is null");
            return (Criteria) this;
        }

        public Criteria andAppdeptcodeIsNotNull() {
            addCriterion("AppDeptCode is not null");
            return (Criteria) this;
        }

        public Criteria andAppdeptcodeEqualTo(String value) {
            addCriterion("AppDeptCode =", value, "appdeptcode");
            return (Criteria) this;
        }

        public Criteria andAppdeptcodeNotEqualTo(String value) {
            addCriterion("AppDeptCode <>", value, "appdeptcode");
            return (Criteria) this;
        }

        public Criteria andAppdeptcodeGreaterThan(String value) {
            addCriterion("AppDeptCode >", value, "appdeptcode");
            return (Criteria) this;
        }

        public Criteria andAppdeptcodeGreaterThanOrEqualTo(String value) {
            addCriterion("AppDeptCode >=", value, "appdeptcode");
            return (Criteria) this;
        }

        public Criteria andAppdeptcodeLessThan(String value) {
            addCriterion("AppDeptCode <", value, "appdeptcode");
            return (Criteria) this;
        }

        public Criteria andAppdeptcodeLessThanOrEqualTo(String value) {
            addCriterion("AppDeptCode <=", value, "appdeptcode");
            return (Criteria) this;
        }

        public Criteria andAppdeptcodeLike(String value) {
            addCriterion("AppDeptCode like", value, "appdeptcode");
            return (Criteria) this;
        }

        public Criteria andAppdeptcodeNotLike(String value) {
            addCriterion("AppDeptCode not like", value, "appdeptcode");
            return (Criteria) this;
        }

        public Criteria andAppdeptcodeIn(List<String> values) {
            addCriterion("AppDeptCode in", values, "appdeptcode");
            return (Criteria) this;
        }

        public Criteria andAppdeptcodeNotIn(List<String> values) {
            addCriterion("AppDeptCode not in", values, "appdeptcode");
            return (Criteria) this;
        }

        public Criteria andAppdeptcodeBetween(String value1, String value2) {
            addCriterion("AppDeptCode between", value1, value2, "appdeptcode");
            return (Criteria) this;
        }

        public Criteria andAppdeptcodeNotBetween(String value1, String value2) {
            addCriterion("AppDeptCode not between", value1, value2, "appdeptcode");
            return (Criteria) this;
        }

        public Criteria andAppdeptdescIsNull() {
            addCriterion("AppDeptDesc is null");
            return (Criteria) this;
        }

        public Criteria andAppdeptdescIsNotNull() {
            addCriterion("AppDeptDesc is not null");
            return (Criteria) this;
        }

        public Criteria andAppdeptdescEqualTo(String value) {
            addCriterion("AppDeptDesc =", value, "appdeptdesc");
            return (Criteria) this;
        }

        public Criteria andAppdeptdescNotEqualTo(String value) {
            addCriterion("AppDeptDesc <>", value, "appdeptdesc");
            return (Criteria) this;
        }

        public Criteria andAppdeptdescGreaterThan(String value) {
            addCriterion("AppDeptDesc >", value, "appdeptdesc");
            return (Criteria) this;
        }

        public Criteria andAppdeptdescGreaterThanOrEqualTo(String value) {
            addCriterion("AppDeptDesc >=", value, "appdeptdesc");
            return (Criteria) this;
        }

        public Criteria andAppdeptdescLessThan(String value) {
            addCriterion("AppDeptDesc <", value, "appdeptdesc");
            return (Criteria) this;
        }

        public Criteria andAppdeptdescLessThanOrEqualTo(String value) {
            addCriterion("AppDeptDesc <=", value, "appdeptdesc");
            return (Criteria) this;
        }

        public Criteria andAppdeptdescLike(String value) {
            addCriterion("AppDeptDesc like", value, "appdeptdesc");
            return (Criteria) this;
        }

        public Criteria andAppdeptdescNotLike(String value) {
            addCriterion("AppDeptDesc not like", value, "appdeptdesc");
            return (Criteria) this;
        }

        public Criteria andAppdeptdescIn(List<String> values) {
            addCriterion("AppDeptDesc in", values, "appdeptdesc");
            return (Criteria) this;
        }

        public Criteria andAppdeptdescNotIn(List<String> values) {
            addCriterion("AppDeptDesc not in", values, "appdeptdesc");
            return (Criteria) this;
        }

        public Criteria andAppdeptdescBetween(String value1, String value2) {
            addCriterion("AppDeptDesc between", value1, value2, "appdeptdesc");
            return (Criteria) this;
        }

        public Criteria andAppdeptdescNotBetween(String value1, String value2) {
            addCriterion("AppDeptDesc not between", value1, value2, "appdeptdesc");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdoccodeIsNull() {
            addCriterion("RISRSubmitDocCode is null");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdoccodeIsNotNull() {
            addCriterion("RISRSubmitDocCode is not null");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdoccodeEqualTo(String value) {
            addCriterion("RISRSubmitDocCode =", value, "risrsubmitdoccode");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdoccodeNotEqualTo(String value) {
            addCriterion("RISRSubmitDocCode <>", value, "risrsubmitdoccode");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdoccodeGreaterThan(String value) {
            addCriterion("RISRSubmitDocCode >", value, "risrsubmitdoccode");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdoccodeGreaterThanOrEqualTo(String value) {
            addCriterion("RISRSubmitDocCode >=", value, "risrsubmitdoccode");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdoccodeLessThan(String value) {
            addCriterion("RISRSubmitDocCode <", value, "risrsubmitdoccode");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdoccodeLessThanOrEqualTo(String value) {
            addCriterion("RISRSubmitDocCode <=", value, "risrsubmitdoccode");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdoccodeLike(String value) {
            addCriterion("RISRSubmitDocCode like", value, "risrsubmitdoccode");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdoccodeNotLike(String value) {
            addCriterion("RISRSubmitDocCode not like", value, "risrsubmitdoccode");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdoccodeIn(List<String> values) {
            addCriterion("RISRSubmitDocCode in", values, "risrsubmitdoccode");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdoccodeNotIn(List<String> values) {
            addCriterion("RISRSubmitDocCode not in", values, "risrsubmitdoccode");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdoccodeBetween(String value1, String value2) {
            addCriterion("RISRSubmitDocCode between", value1, value2, "risrsubmitdoccode");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdoccodeNotBetween(String value1, String value2) {
            addCriterion("RISRSubmitDocCode not between", value1, value2, "risrsubmitdoccode");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdocdescIsNull() {
            addCriterion("RISRSubmitDocDesc is null");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdocdescIsNotNull() {
            addCriterion("RISRSubmitDocDesc is not null");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdocdescEqualTo(String value) {
            addCriterion("RISRSubmitDocDesc =", value, "risrsubmitdocdesc");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdocdescNotEqualTo(String value) {
            addCriterion("RISRSubmitDocDesc <>", value, "risrsubmitdocdesc");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdocdescGreaterThan(String value) {
            addCriterion("RISRSubmitDocDesc >", value, "risrsubmitdocdesc");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdocdescGreaterThanOrEqualTo(String value) {
            addCriterion("RISRSubmitDocDesc >=", value, "risrsubmitdocdesc");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdocdescLessThan(String value) {
            addCriterion("RISRSubmitDocDesc <", value, "risrsubmitdocdesc");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdocdescLessThanOrEqualTo(String value) {
            addCriterion("RISRSubmitDocDesc <=", value, "risrsubmitdocdesc");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdocdescLike(String value) {
            addCriterion("RISRSubmitDocDesc like", value, "risrsubmitdocdesc");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdocdescNotLike(String value) {
            addCriterion("RISRSubmitDocDesc not like", value, "risrsubmitdocdesc");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdocdescIn(List<String> values) {
            addCriterion("RISRSubmitDocDesc in", values, "risrsubmitdocdesc");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdocdescNotIn(List<String> values) {
            addCriterion("RISRSubmitDocDesc not in", values, "risrsubmitdocdesc");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdocdescBetween(String value1, String value2) {
            addCriterion("RISRSubmitDocDesc between", value1, value2, "risrsubmitdocdesc");
            return (Criteria) this;
        }

        public Criteria andRisrsubmitdocdescNotBetween(String value1, String value2) {
            addCriterion("RISRSubmitDocDesc not between", value1, value2, "risrsubmitdocdesc");
            return (Criteria) this;
        }

        public Criteria andRisrsubmittimeIsNull() {
            addCriterion("RISRSubmitTime is null");
            return (Criteria) this;
        }

        public Criteria andRisrsubmittimeIsNotNull() {
            addCriterion("RISRSubmitTime is not null");
            return (Criteria) this;
        }

        public Criteria andRisrsubmittimeEqualTo(Date value) {
            addCriterion("RISRSubmitTime =", value, "risrsubmittime");
            return (Criteria) this;
        }

        public Criteria andRisrsubmittimeNotEqualTo(Date value) {
            addCriterion("RISRSubmitTime <>", value, "risrsubmittime");
            return (Criteria) this;
        }

        public Criteria andRisrsubmittimeGreaterThan(Date value) {
            addCriterion("RISRSubmitTime >", value, "risrsubmittime");
            return (Criteria) this;
        }

        public Criteria andRisrsubmittimeGreaterThanOrEqualTo(Date value) {
            addCriterion("RISRSubmitTime >=", value, "risrsubmittime");
            return (Criteria) this;
        }

        public Criteria andRisrsubmittimeLessThan(Date value) {
            addCriterion("RISRSubmitTime <", value, "risrsubmittime");
            return (Criteria) this;
        }

        public Criteria andRisrsubmittimeLessThanOrEqualTo(Date value) {
            addCriterion("RISRSubmitTime <=", value, "risrsubmittime");
            return (Criteria) this;
        }

        public Criteria andRisrsubmittimeIn(List<Date> values) {
            addCriterion("RISRSubmitTime in", values, "risrsubmittime");
            return (Criteria) this;
        }

        public Criteria andRisrsubmittimeNotIn(List<Date> values) {
            addCriterion("RISRSubmitTime not in", values, "risrsubmittime");
            return (Criteria) this;
        }

        public Criteria andRisrsubmittimeBetween(Date value1, Date value2) {
            addCriterion("RISRSubmitTime between", value1, value2, "risrsubmittime");
            return (Criteria) this;
        }

        public Criteria andRisrsubmittimeNotBetween(Date value1, Date value2) {
            addCriterion("RISRSubmitTime not between", value1, value2, "risrsubmittime");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptcodeIsNull() {
            addCriterion("RISRAcceptDeptCode is null");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptcodeIsNotNull() {
            addCriterion("RISRAcceptDeptCode is not null");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptcodeEqualTo(String value) {
            addCriterion("RISRAcceptDeptCode =", value, "risracceptdeptcode");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptcodeNotEqualTo(String value) {
            addCriterion("RISRAcceptDeptCode <>", value, "risracceptdeptcode");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptcodeGreaterThan(String value) {
            addCriterion("RISRAcceptDeptCode >", value, "risracceptdeptcode");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptcodeGreaterThanOrEqualTo(String value) {
            addCriterion("RISRAcceptDeptCode >=", value, "risracceptdeptcode");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptcodeLessThan(String value) {
            addCriterion("RISRAcceptDeptCode <", value, "risracceptdeptcode");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptcodeLessThanOrEqualTo(String value) {
            addCriterion("RISRAcceptDeptCode <=", value, "risracceptdeptcode");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptcodeLike(String value) {
            addCriterion("RISRAcceptDeptCode like", value, "risracceptdeptcode");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptcodeNotLike(String value) {
            addCriterion("RISRAcceptDeptCode not like", value, "risracceptdeptcode");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptcodeIn(List<String> values) {
            addCriterion("RISRAcceptDeptCode in", values, "risracceptdeptcode");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptcodeNotIn(List<String> values) {
            addCriterion("RISRAcceptDeptCode not in", values, "risracceptdeptcode");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptcodeBetween(String value1, String value2) {
            addCriterion("RISRAcceptDeptCode between", value1, value2, "risracceptdeptcode");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptcodeNotBetween(String value1, String value2) {
            addCriterion("RISRAcceptDeptCode not between", value1, value2, "risracceptdeptcode");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptdescIsNull() {
            addCriterion("RISRAcceptDeptDesc is null");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptdescIsNotNull() {
            addCriterion("RISRAcceptDeptDesc is not null");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptdescEqualTo(String value) {
            addCriterion("RISRAcceptDeptDesc =", value, "risracceptdeptdesc");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptdescNotEqualTo(String value) {
            addCriterion("RISRAcceptDeptDesc <>", value, "risracceptdeptdesc");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptdescGreaterThan(String value) {
            addCriterion("RISRAcceptDeptDesc >", value, "risracceptdeptdesc");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptdescGreaterThanOrEqualTo(String value) {
            addCriterion("RISRAcceptDeptDesc >=", value, "risracceptdeptdesc");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptdescLessThan(String value) {
            addCriterion("RISRAcceptDeptDesc <", value, "risracceptdeptdesc");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptdescLessThanOrEqualTo(String value) {
            addCriterion("RISRAcceptDeptDesc <=", value, "risracceptdeptdesc");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptdescLike(String value) {
            addCriterion("RISRAcceptDeptDesc like", value, "risracceptdeptdesc");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptdescNotLike(String value) {
            addCriterion("RISRAcceptDeptDesc not like", value, "risracceptdeptdesc");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptdescIn(List<String> values) {
            addCriterion("RISRAcceptDeptDesc in", values, "risracceptdeptdesc");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptdescNotIn(List<String> values) {
            addCriterion("RISRAcceptDeptDesc not in", values, "risracceptdeptdesc");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptdescBetween(String value1, String value2) {
            addCriterion("RISRAcceptDeptDesc between", value1, value2, "risracceptdeptdesc");
            return (Criteria) this;
        }

        public Criteria andRisracceptdeptdescNotBetween(String value1, String value2) {
            addCriterion("RISRAcceptDeptDesc not between", value1, value2, "risracceptdeptdesc");
            return (Criteria) this;
        }

        public Criteria andRisrdeptlocationIsNull() {
            addCriterion("RISRDeptLocation is null");
            return (Criteria) this;
        }

        public Criteria andRisrdeptlocationIsNotNull() {
            addCriterion("RISRDeptLocation is not null");
            return (Criteria) this;
        }

        public Criteria andRisrdeptlocationEqualTo(String value) {
            addCriterion("RISRDeptLocation =", value, "risrdeptlocation");
            return (Criteria) this;
        }

        public Criteria andRisrdeptlocationNotEqualTo(String value) {
            addCriterion("RISRDeptLocation <>", value, "risrdeptlocation");
            return (Criteria) this;
        }

        public Criteria andRisrdeptlocationGreaterThan(String value) {
            addCriterion("RISRDeptLocation >", value, "risrdeptlocation");
            return (Criteria) this;
        }

        public Criteria andRisrdeptlocationGreaterThanOrEqualTo(String value) {
            addCriterion("RISRDeptLocation >=", value, "risrdeptlocation");
            return (Criteria) this;
        }

        public Criteria andRisrdeptlocationLessThan(String value) {
            addCriterion("RISRDeptLocation <", value, "risrdeptlocation");
            return (Criteria) this;
        }

        public Criteria andRisrdeptlocationLessThanOrEqualTo(String value) {
            addCriterion("RISRDeptLocation <=", value, "risrdeptlocation");
            return (Criteria) this;
        }

        public Criteria andRisrdeptlocationLike(String value) {
            addCriterion("RISRDeptLocation like", value, "risrdeptlocation");
            return (Criteria) this;
        }

        public Criteria andRisrdeptlocationNotLike(String value) {
            addCriterion("RISRDeptLocation not like", value, "risrdeptlocation");
            return (Criteria) this;
        }

        public Criteria andRisrdeptlocationIn(List<String> values) {
            addCriterion("RISRDeptLocation in", values, "risrdeptlocation");
            return (Criteria) this;
        }

        public Criteria andRisrdeptlocationNotIn(List<String> values) {
            addCriterion("RISRDeptLocation not in", values, "risrdeptlocation");
            return (Criteria) this;
        }

        public Criteria andRisrdeptlocationBetween(String value1, String value2) {
            addCriterion("RISRDeptLocation between", value1, value2, "risrdeptlocation");
            return (Criteria) this;
        }

        public Criteria andRisrdeptlocationNotBetween(String value1, String value2) {
            addCriterion("RISRDeptLocation not between", value1, value2, "risrdeptlocation");
            return (Criteria) this;
        }

        public Criteria andRisrisemergencyIsNull() {
            addCriterion("RISRISEmergency is null");
            return (Criteria) this;
        }

        public Criteria andRisrisemergencyIsNotNull() {
            addCriterion("RISRISEmergency is not null");
            return (Criteria) this;
        }

        public Criteria andRisrisemergencyEqualTo(Integer value) {
            addCriterion("RISRISEmergency =", value, "risrisemergency");
            return (Criteria) this;
        }

        public Criteria andRisrisemergencyNotEqualTo(Integer value) {
            addCriterion("RISRISEmergency <>", value, "risrisemergency");
            return (Criteria) this;
        }

        public Criteria andRisrisemergencyGreaterThan(Integer value) {
            addCriterion("RISRISEmergency >", value, "risrisemergency");
            return (Criteria) this;
        }

        public Criteria andRisrisemergencyGreaterThanOrEqualTo(Integer value) {
            addCriterion("RISRISEmergency >=", value, "risrisemergency");
            return (Criteria) this;
        }

        public Criteria andRisrisemergencyLessThan(Integer value) {
            addCriterion("RISRISEmergency <", value, "risrisemergency");
            return (Criteria) this;
        }

        public Criteria andRisrisemergencyLessThanOrEqualTo(Integer value) {
            addCriterion("RISRISEmergency <=", value, "risrisemergency");
            return (Criteria) this;
        }

        public Criteria andRisrisemergencyIn(List<Integer> values) {
            addCriterion("RISRISEmergency in", values, "risrisemergency");
            return (Criteria) this;
        }

        public Criteria andRisrisemergencyNotIn(List<Integer> values) {
            addCriterion("RISRISEmergency not in", values, "risrisemergency");
            return (Criteria) this;
        }

        public Criteria andRisrisemergencyBetween(Integer value1, Integer value2) {
            addCriterion("RISRISEmergency between", value1, value2, "risrisemergency");
            return (Criteria) this;
        }

        public Criteria andRisrisemergencyNotBetween(Integer value1, Integer value2) {
            addCriterion("RISRISEmergency not between", value1, value2, "risrisemergency");
            return (Criteria) this;
        }

        public Criteria andOeoriorderitemidIsNull() {
            addCriterion("OEORIOrderItemID is null");
            return (Criteria) this;
        }

        public Criteria andOeoriorderitemidIsNotNull() {
            addCriterion("OEORIOrderItemID is not null");
            return (Criteria) this;
        }

        public Criteria andOeoriorderitemidEqualTo(String value) {
            addCriterion("OEORIOrderItemID =", value, "oeoriorderitemid");
            return (Criteria) this;
        }

        public Criteria andOeoriorderitemidNotEqualTo(String value) {
            addCriterion("OEORIOrderItemID <>", value, "oeoriorderitemid");
            return (Criteria) this;
        }

        public Criteria andOeoriorderitemidGreaterThan(String value) {
            addCriterion("OEORIOrderItemID >", value, "oeoriorderitemid");
            return (Criteria) this;
        }

        public Criteria andOeoriorderitemidGreaterThanOrEqualTo(String value) {
            addCriterion("OEORIOrderItemID >=", value, "oeoriorderitemid");
            return (Criteria) this;
        }

        public Criteria andOeoriorderitemidLessThan(String value) {
            addCriterion("OEORIOrderItemID <", value, "oeoriorderitemid");
            return (Criteria) this;
        }

        public Criteria andOeoriorderitemidLessThanOrEqualTo(String value) {
            addCriterion("OEORIOrderItemID <=", value, "oeoriorderitemid");
            return (Criteria) this;
        }

        public Criteria andOeoriorderitemidLike(String value) {
            addCriterion("OEORIOrderItemID like", value, "oeoriorderitemid");
            return (Criteria) this;
        }

        public Criteria andOeoriorderitemidNotLike(String value) {
            addCriterion("OEORIOrderItemID not like", value, "oeoriorderitemid");
            return (Criteria) this;
        }

        public Criteria andOeoriorderitemidIn(List<String> values) {
            addCriterion("OEORIOrderItemID in", values, "oeoriorderitemid");
            return (Criteria) this;
        }

        public Criteria andOeoriorderitemidNotIn(List<String> values) {
            addCriterion("OEORIOrderItemID not in", values, "oeoriorderitemid");
            return (Criteria) this;
        }

        public Criteria andOeoriorderitemidBetween(String value1, String value2) {
            addCriterion("OEORIOrderItemID between", value1, value2, "oeoriorderitemid");
            return (Criteria) this;
        }

        public Criteria andOeoriorderitemidNotBetween(String value1, String value2) {
            addCriterion("OEORIOrderItemID not between", value1, value2, "oeoriorderitemid");
            return (Criteria) this;
        }

        public Criteria andRisrpositioncodeIsNull() {
            addCriterion("RISRPositionCode is null");
            return (Criteria) this;
        }

        public Criteria andRisrpositioncodeIsNotNull() {
            addCriterion("RISRPositionCode is not null");
            return (Criteria) this;
        }

        public Criteria andRisrpositioncodeEqualTo(String value) {
            addCriterion("RISRPositionCode =", value, "risrpositioncode");
            return (Criteria) this;
        }

        public Criteria andRisrpositioncodeNotEqualTo(String value) {
            addCriterion("RISRPositionCode <>", value, "risrpositioncode");
            return (Criteria) this;
        }

        public Criteria andRisrpositioncodeGreaterThan(String value) {
            addCriterion("RISRPositionCode >", value, "risrpositioncode");
            return (Criteria) this;
        }

        public Criteria andRisrpositioncodeGreaterThanOrEqualTo(String value) {
            addCriterion("RISRPositionCode >=", value, "risrpositioncode");
            return (Criteria) this;
        }

        public Criteria andRisrpositioncodeLessThan(String value) {
            addCriterion("RISRPositionCode <", value, "risrpositioncode");
            return (Criteria) this;
        }

        public Criteria andRisrpositioncodeLessThanOrEqualTo(String value) {
            addCriterion("RISRPositionCode <=", value, "risrpositioncode");
            return (Criteria) this;
        }

        public Criteria andRisrpositioncodeLike(String value) {
            addCriterion("RISRPositionCode like", value, "risrpositioncode");
            return (Criteria) this;
        }

        public Criteria andRisrpositioncodeNotLike(String value) {
            addCriterion("RISRPositionCode not like", value, "risrpositioncode");
            return (Criteria) this;
        }

        public Criteria andRisrpositioncodeIn(List<String> values) {
            addCriterion("RISRPositionCode in", values, "risrpositioncode");
            return (Criteria) this;
        }

        public Criteria andRisrpositioncodeNotIn(List<String> values) {
            addCriterion("RISRPositionCode not in", values, "risrpositioncode");
            return (Criteria) this;
        }

        public Criteria andRisrpositioncodeBetween(String value1, String value2) {
            addCriterion("RISRPositionCode between", value1, value2, "risrpositioncode");
            return (Criteria) this;
        }

        public Criteria andRisrpositioncodeNotBetween(String value1, String value2) {
            addCriterion("RISRPositionCode not between", value1, value2, "risrpositioncode");
            return (Criteria) this;
        }

        public Criteria andRisrposturecodeIsNull() {
            addCriterion("RISRPostureCode is null");
            return (Criteria) this;
        }

        public Criteria andRisrposturecodeIsNotNull() {
            addCriterion("RISRPostureCode is not null");
            return (Criteria) this;
        }

        public Criteria andRisrposturecodeEqualTo(String value) {
            addCriterion("RISRPostureCode =", value, "risrposturecode");
            return (Criteria) this;
        }

        public Criteria andRisrposturecodeNotEqualTo(String value) {
            addCriterion("RISRPostureCode <>", value, "risrposturecode");
            return (Criteria) this;
        }

        public Criteria andRisrposturecodeGreaterThan(String value) {
            addCriterion("RISRPostureCode >", value, "risrposturecode");
            return (Criteria) this;
        }

        public Criteria andRisrposturecodeGreaterThanOrEqualTo(String value) {
            addCriterion("RISRPostureCode >=", value, "risrposturecode");
            return (Criteria) this;
        }

        public Criteria andRisrposturecodeLessThan(String value) {
            addCriterion("RISRPostureCode <", value, "risrposturecode");
            return (Criteria) this;
        }

        public Criteria andRisrposturecodeLessThanOrEqualTo(String value) {
            addCriterion("RISRPostureCode <=", value, "risrposturecode");
            return (Criteria) this;
        }

        public Criteria andRisrposturecodeLike(String value) {
            addCriterion("RISRPostureCode like", value, "risrposturecode");
            return (Criteria) this;
        }

        public Criteria andRisrposturecodeNotLike(String value) {
            addCriterion("RISRPostureCode not like", value, "risrposturecode");
            return (Criteria) this;
        }

        public Criteria andRisrposturecodeIn(List<String> values) {
            addCriterion("RISRPostureCode in", values, "risrposturecode");
            return (Criteria) this;
        }

        public Criteria andRisrposturecodeNotIn(List<String> values) {
            addCriterion("RISRPostureCode not in", values, "risrposturecode");
            return (Criteria) this;
        }

        public Criteria andRisrposturecodeBetween(String value1, String value2) {
            addCriterion("RISRPostureCode between", value1, value2, "risrposturecode");
            return (Criteria) this;
        }

        public Criteria andRisrposturecodeNotBetween(String value1, String value2) {
            addCriterion("RISRPostureCode not between", value1, value2, "risrposturecode");
            return (Criteria) this;
        }

        public Criteria andRisrcodeIsNull() {
            addCriterion("RISRCode is null");
            return (Criteria) this;
        }

        public Criteria andRisrcodeIsNotNull() {
            addCriterion("RISRCode is not null");
            return (Criteria) this;
        }

        public Criteria andRisrcodeEqualTo(String value) {
            addCriterion("RISRCode =", value, "risrcode");
            return (Criteria) this;
        }

        public Criteria andRisrcodeNotEqualTo(String value) {
            addCriterion("RISRCode <>", value, "risrcode");
            return (Criteria) this;
        }

        public Criteria andRisrcodeGreaterThan(String value) {
            addCriterion("RISRCode >", value, "risrcode");
            return (Criteria) this;
        }

        public Criteria andRisrcodeGreaterThanOrEqualTo(String value) {
            addCriterion("RISRCode >=", value, "risrcode");
            return (Criteria) this;
        }

        public Criteria andRisrcodeLessThan(String value) {
            addCriterion("RISRCode <", value, "risrcode");
            return (Criteria) this;
        }

        public Criteria andRisrcodeLessThanOrEqualTo(String value) {
            addCriterion("RISRCode <=", value, "risrcode");
            return (Criteria) this;
        }

        public Criteria andRisrcodeLike(String value) {
            addCriterion("RISRCode like", value, "risrcode");
            return (Criteria) this;
        }

        public Criteria andRisrcodeNotLike(String value) {
            addCriterion("RISRCode not like", value, "risrcode");
            return (Criteria) this;
        }

        public Criteria andRisrcodeIn(List<String> values) {
            addCriterion("RISRCode in", values, "risrcode");
            return (Criteria) this;
        }

        public Criteria andRisrcodeNotIn(List<String> values) {
            addCriterion("RISRCode not in", values, "risrcode");
            return (Criteria) this;
        }

        public Criteria andRisrcodeBetween(String value1, String value2) {
            addCriterion("RISRCode between", value1, value2, "risrcode");
            return (Criteria) this;
        }

        public Criteria andRisrcodeNotBetween(String value1, String value2) {
            addCriterion("RISRCode not between", value1, value2, "risrcode");
            return (Criteria) this;
        }

        public Criteria andRisrdescIsNull() {
            addCriterion("RISRDesc is null");
            return (Criteria) this;
        }

        public Criteria andRisrdescIsNotNull() {
            addCriterion("RISRDesc is not null");
            return (Criteria) this;
        }

        public Criteria andRisrdescEqualTo(String value) {
            addCriterion("RISRDesc =", value, "risrdesc");
            return (Criteria) this;
        }

        public Criteria andRisrdescNotEqualTo(String value) {
            addCriterion("RISRDesc <>", value, "risrdesc");
            return (Criteria) this;
        }

        public Criteria andRisrdescGreaterThan(String value) {
            addCriterion("RISRDesc >", value, "risrdesc");
            return (Criteria) this;
        }

        public Criteria andRisrdescGreaterThanOrEqualTo(String value) {
            addCriterion("RISRDesc >=", value, "risrdesc");
            return (Criteria) this;
        }

        public Criteria andRisrdescLessThan(String value) {
            addCriterion("RISRDesc <", value, "risrdesc");
            return (Criteria) this;
        }

        public Criteria andRisrdescLessThanOrEqualTo(String value) {
            addCriterion("RISRDesc <=", value, "risrdesc");
            return (Criteria) this;
        }

        public Criteria andRisrdescLike(String value) {
            addCriterion("RISRDesc like", value, "risrdesc");
            return (Criteria) this;
        }

        public Criteria andRisrdescNotLike(String value) {
            addCriterion("RISRDesc not like", value, "risrdesc");
            return (Criteria) this;
        }

        public Criteria andRisrdescIn(List<String> values) {
            addCriterion("RISRDesc in", values, "risrdesc");
            return (Criteria) this;
        }

        public Criteria andRisrdescNotIn(List<String> values) {
            addCriterion("RISRDesc not in", values, "risrdesc");
            return (Criteria) this;
        }

        public Criteria andRisrdescBetween(String value1, String value2) {
            addCriterion("RISRDesc between", value1, value2, "risrdesc");
            return (Criteria) this;
        }

        public Criteria andRisrdescNotBetween(String value1, String value2) {
            addCriterion("RISRDesc not between", value1, value2, "risrdesc");
            return (Criteria) this;
        }

        public Criteria andRisrpriceIsNull() {
            addCriterion("RISRPrice is null");
            return (Criteria) this;
        }

        public Criteria andRisrpriceIsNotNull() {
            addCriterion("RISRPrice is not null");
            return (Criteria) this;
        }

        public Criteria andRisrpriceEqualTo(BigDecimal value) {
            addCriterion("RISRPrice =", value, "risrprice");
            return (Criteria) this;
        }

        public Criteria andRisrpriceNotEqualTo(BigDecimal value) {
            addCriterion("RISRPrice <>", value, "risrprice");
            return (Criteria) this;
        }

        public Criteria andRisrpriceGreaterThan(BigDecimal value) {
            addCriterion("RISRPrice >", value, "risrprice");
            return (Criteria) this;
        }

        public Criteria andRisrpriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("RISRPrice >=", value, "risrprice");
            return (Criteria) this;
        }

        public Criteria andRisrpriceLessThan(BigDecimal value) {
            addCriterion("RISRPrice <", value, "risrprice");
            return (Criteria) this;
        }

        public Criteria andRisrpriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("RISRPrice <=", value, "risrprice");
            return (Criteria) this;
        }

        public Criteria andRisrpriceIn(List<BigDecimal> values) {
            addCriterion("RISRPrice in", values, "risrprice");
            return (Criteria) this;
        }

        public Criteria andRisrpriceNotIn(List<BigDecimal> values) {
            addCriterion("RISRPrice not in", values, "risrprice");
            return (Criteria) this;
        }

        public Criteria andRisrpriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("RISRPrice between", value1, value2, "risrprice");
            return (Criteria) this;
        }

        public Criteria andRisrpriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("RISRPrice not between", value1, value2, "risrprice");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatcodeIsNull() {
            addCriterion("OrdSubCatCode is null");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatcodeIsNotNull() {
            addCriterion("OrdSubCatCode is not null");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatcodeEqualTo(String value) {
            addCriterion("OrdSubCatCode =", value, "ordsubcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatcodeNotEqualTo(String value) {
            addCriterion("OrdSubCatCode <>", value, "ordsubcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatcodeGreaterThan(String value) {
            addCriterion("OrdSubCatCode >", value, "ordsubcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatcodeGreaterThanOrEqualTo(String value) {
            addCriterion("OrdSubCatCode >=", value, "ordsubcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatcodeLessThan(String value) {
            addCriterion("OrdSubCatCode <", value, "ordsubcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatcodeLessThanOrEqualTo(String value) {
            addCriterion("OrdSubCatCode <=", value, "ordsubcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatcodeLike(String value) {
            addCriterion("OrdSubCatCode like", value, "ordsubcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatcodeNotLike(String value) {
            addCriterion("OrdSubCatCode not like", value, "ordsubcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatcodeIn(List<String> values) {
            addCriterion("OrdSubCatCode in", values, "ordsubcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatcodeNotIn(List<String> values) {
            addCriterion("OrdSubCatCode not in", values, "ordsubcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatcodeBetween(String value1, String value2) {
            addCriterion("OrdSubCatCode between", value1, value2, "ordsubcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatcodeNotBetween(String value1, String value2) {
            addCriterion("OrdSubCatCode not between", value1, value2, "ordsubcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatdescIsNull() {
            addCriterion("OrdSubCatDesc is null");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatdescIsNotNull() {
            addCriterion("OrdSubCatDesc is not null");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatdescEqualTo(String value) {
            addCriterion("OrdSubCatDesc =", value, "ordsubcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatdescNotEqualTo(String value) {
            addCriterion("OrdSubCatDesc <>", value, "ordsubcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatdescGreaterThan(String value) {
            addCriterion("OrdSubCatDesc >", value, "ordsubcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatdescGreaterThanOrEqualTo(String value) {
            addCriterion("OrdSubCatDesc >=", value, "ordsubcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatdescLessThan(String value) {
            addCriterion("OrdSubCatDesc <", value, "ordsubcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatdescLessThanOrEqualTo(String value) {
            addCriterion("OrdSubCatDesc <=", value, "ordsubcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatdescLike(String value) {
            addCriterion("OrdSubCatDesc like", value, "ordsubcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatdescNotLike(String value) {
            addCriterion("OrdSubCatDesc not like", value, "ordsubcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatdescIn(List<String> values) {
            addCriterion("OrdSubCatDesc in", values, "ordsubcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatdescNotIn(List<String> values) {
            addCriterion("OrdSubCatDesc not in", values, "ordsubcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatdescBetween(String value1, String value2) {
            addCriterion("OrdSubCatDesc between", value1, value2, "ordsubcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdsubcatdescNotBetween(String value1, String value2) {
            addCriterion("OrdSubCatDesc not between", value1, value2, "ordsubcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdcatcodeIsNull() {
            addCriterion("OrdCatCode is null");
            return (Criteria) this;
        }

        public Criteria andOrdcatcodeIsNotNull() {
            addCriterion("OrdCatCode is not null");
            return (Criteria) this;
        }

        public Criteria andOrdcatcodeEqualTo(String value) {
            addCriterion("OrdCatCode =", value, "ordcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdcatcodeNotEqualTo(String value) {
            addCriterion("OrdCatCode <>", value, "ordcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdcatcodeGreaterThan(String value) {
            addCriterion("OrdCatCode >", value, "ordcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdcatcodeGreaterThanOrEqualTo(String value) {
            addCriterion("OrdCatCode >=", value, "ordcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdcatcodeLessThan(String value) {
            addCriterion("OrdCatCode <", value, "ordcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdcatcodeLessThanOrEqualTo(String value) {
            addCriterion("OrdCatCode <=", value, "ordcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdcatcodeLike(String value) {
            addCriterion("OrdCatCode like", value, "ordcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdcatcodeNotLike(String value) {
            addCriterion("OrdCatCode not like", value, "ordcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdcatcodeIn(List<String> values) {
            addCriterion("OrdCatCode in", values, "ordcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdcatcodeNotIn(List<String> values) {
            addCriterion("OrdCatCode not in", values, "ordcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdcatcodeBetween(String value1, String value2) {
            addCriterion("OrdCatCode between", value1, value2, "ordcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdcatcodeNotBetween(String value1, String value2) {
            addCriterion("OrdCatCode not between", value1, value2, "ordcatcode");
            return (Criteria) this;
        }

        public Criteria andOrdcatdescIsNull() {
            addCriterion("OrdCatDesc is null");
            return (Criteria) this;
        }

        public Criteria andOrdcatdescIsNotNull() {
            addCriterion("OrdCatDesc is not null");
            return (Criteria) this;
        }

        public Criteria andOrdcatdescEqualTo(String value) {
            addCriterion("OrdCatDesc =", value, "ordcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdcatdescNotEqualTo(String value) {
            addCriterion("OrdCatDesc <>", value, "ordcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdcatdescGreaterThan(String value) {
            addCriterion("OrdCatDesc >", value, "ordcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdcatdescGreaterThanOrEqualTo(String value) {
            addCriterion("OrdCatDesc >=", value, "ordcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdcatdescLessThan(String value) {
            addCriterion("OrdCatDesc <", value, "ordcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdcatdescLessThanOrEqualTo(String value) {
            addCriterion("OrdCatDesc <=", value, "ordcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdcatdescLike(String value) {
            addCriterion("OrdCatDesc like", value, "ordcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdcatdescNotLike(String value) {
            addCriterion("OrdCatDesc not like", value, "ordcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdcatdescIn(List<String> values) {
            addCriterion("OrdCatDesc in", values, "ordcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdcatdescNotIn(List<String> values) {
            addCriterion("OrdCatDesc not in", values, "ordcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdcatdescBetween(String value1, String value2) {
            addCriterion("OrdCatDesc between", value1, value2, "ordcatdesc");
            return (Criteria) this;
        }

        public Criteria andOrdcatdescNotBetween(String value1, String value2) {
            addCriterion("OrdCatDesc not between", value1, value2, "ordcatdesc");
            return (Criteria) this;
        }

        public Criteria andOeoristatuscodeIsNull() {
            addCriterion("OEORIStatusCode is null");
            return (Criteria) this;
        }

        public Criteria andOeoristatuscodeIsNotNull() {
            addCriterion("OEORIStatusCode is not null");
            return (Criteria) this;
        }

        public Criteria andOeoristatuscodeEqualTo(String value) {
            addCriterion("OEORIStatusCode =", value, "oeoristatuscode");
            return (Criteria) this;
        }

        public Criteria andOeoristatuscodeEqualToOrIsNull(String value) {
            addCriterion("(OEORIStatusCode = \""+value+"\" or OEORIStatusCode is null)");
            return (Criteria) this;
        }

        public Criteria andLimitOrdBillNumOrOeoristatuscodeEqualToOrIsNull(String value) {
            addCriterion("(OEORIStatusCode = \""+value+"\" or OEORIStatusCode is null or OrdBillNum > COALESCE(sub.examInfoCount, 0))");
            return (Criteria) this;
        }

        public Criteria andOeoristatuscodeNotEqualTo(String value) {
            addCriterion("OEORIStatusCode <>", value, "oeoristatuscode");
            return (Criteria) this;
        }

        public Criteria andOeoristatuscodeGreaterThan(String value) {
            addCriterion("OEORIStatusCode >", value, "oeoristatuscode");
            return (Criteria) this;
        }

        public Criteria andOeoristatuscodeGreaterThanOrEqualTo(String value) {
            addCriterion("OEORIStatusCode >=", value, "oeoristatuscode");
            return (Criteria) this;
        }

        public Criteria andOeoristatuscodeLessThan(String value) {
            addCriterion("OEORIStatusCode <", value, "oeoristatuscode");
            return (Criteria) this;
        }

        public Criteria andOeoristatuscodeLessThanOrEqualTo(String value) {
            addCriterion("OEORIStatusCode <=", value, "oeoristatuscode");
            return (Criteria) this;
        }

        public Criteria andOeoristatuscodeLike(String value) {
            addCriterion("OEORIStatusCode like", value, "oeoristatuscode");
            return (Criteria) this;
        }

        public Criteria andOeoristatuscodeNotLike(String value) {
            addCriterion("OEORIStatusCode not like", value, "oeoristatuscode");
            return (Criteria) this;
        }

        public Criteria andOeoristatuscodeIn(List<String> values) {
            addCriterion("OEORIStatusCode in", values, "oeoristatuscode");
            return (Criteria) this;
        }

        public Criteria andOeoristatuscodeNotIn(List<String> values) {
            addCriterion("OEORIStatusCode not in", values, "oeoristatuscode");
            return (Criteria) this;
        }

        public Criteria andOeoristatuscodeBetween(String value1, String value2) {
            addCriterion("OEORIStatusCode between", value1, value2, "oeoristatuscode");
            return (Criteria) this;
        }

        public Criteria andOeoristatuscodeNotBetween(String value1, String value2) {
            addCriterion("OEORIStatusCode not between", value1, value2, "oeoristatuscode");
            return (Criteria) this;
        }

        public Criteria andUpdateusercodeIsNull() {
            addCriterion("UpdateUserCode is null");
            return (Criteria) this;
        }

        public Criteria andUpdateusercodeIsNotNull() {
            addCriterion("UpdateUserCode is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateusercodeEqualTo(String value) {
            addCriterion("UpdateUserCode =", value, "updateusercode");
            return (Criteria) this;
        }

        public Criteria andUpdateusercodeNotEqualTo(String value) {
            addCriterion("UpdateUserCode <>", value, "updateusercode");
            return (Criteria) this;
        }

        public Criteria andUpdateusercodeGreaterThan(String value) {
            addCriterion("UpdateUserCode >", value, "updateusercode");
            return (Criteria) this;
        }

        public Criteria andUpdateusercodeGreaterThanOrEqualTo(String value) {
            addCriterion("UpdateUserCode >=", value, "updateusercode");
            return (Criteria) this;
        }

        public Criteria andUpdateusercodeLessThan(String value) {
            addCriterion("UpdateUserCode <", value, "updateusercode");
            return (Criteria) this;
        }

        public Criteria andUpdateusercodeLessThanOrEqualTo(String value) {
            addCriterion("UpdateUserCode <=", value, "updateusercode");
            return (Criteria) this;
        }

        public Criteria andUpdateusercodeLike(String value) {
            addCriterion("UpdateUserCode like", value, "updateusercode");
            return (Criteria) this;
        }

        public Criteria andUpdateusercodeNotLike(String value) {
            addCriterion("UpdateUserCode not like", value, "updateusercode");
            return (Criteria) this;
        }

        public Criteria andUpdateusercodeIn(List<String> values) {
            addCriterion("UpdateUserCode in", values, "updateusercode");
            return (Criteria) this;
        }

        public Criteria andUpdateusercodeNotIn(List<String> values) {
            addCriterion("UpdateUserCode not in", values, "updateusercode");
            return (Criteria) this;
        }

        public Criteria andUpdateusercodeBetween(String value1, String value2) {
            addCriterion("UpdateUserCode between", value1, value2, "updateusercode");
            return (Criteria) this;
        }

        public Criteria andUpdateusercodeNotBetween(String value1, String value2) {
            addCriterion("UpdateUserCode not between", value1, value2, "updateusercode");
            return (Criteria) this;
        }

        public Criteria andUpdateuserdescIsNull() {
            addCriterion("UpdateUserDesc is null");
            return (Criteria) this;
        }

        public Criteria andUpdateuserdescIsNotNull() {
            addCriterion("UpdateUserDesc is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateuserdescEqualTo(String value) {
            addCriterion("UpdateUserDesc =", value, "updateuserdesc");
            return (Criteria) this;
        }

        public Criteria andUpdateuserdescNotEqualTo(String value) {
            addCriterion("UpdateUserDesc <>", value, "updateuserdesc");
            return (Criteria) this;
        }

        public Criteria andUpdateuserdescGreaterThan(String value) {
            addCriterion("UpdateUserDesc >", value, "updateuserdesc");
            return (Criteria) this;
        }

        public Criteria andUpdateuserdescGreaterThanOrEqualTo(String value) {
            addCriterion("UpdateUserDesc >=", value, "updateuserdesc");
            return (Criteria) this;
        }

        public Criteria andUpdateuserdescLessThan(String value) {
            addCriterion("UpdateUserDesc <", value, "updateuserdesc");
            return (Criteria) this;
        }

        public Criteria andUpdateuserdescLessThanOrEqualTo(String value) {
            addCriterion("UpdateUserDesc <=", value, "updateuserdesc");
            return (Criteria) this;
        }

        public Criteria andUpdateuserdescLike(String value) {
            addCriterion("UpdateUserDesc like", value, "updateuserdesc");
            return (Criteria) this;
        }

        public Criteria andUpdateuserdescNotLike(String value) {
            addCriterion("UpdateUserDesc not like", value, "updateuserdesc");
            return (Criteria) this;
        }

        public Criteria andUpdateuserdescIn(List<String> values) {
            addCriterion("UpdateUserDesc in", values, "updateuserdesc");
            return (Criteria) this;
        }

        public Criteria andUpdateuserdescNotIn(List<String> values) {
            addCriterion("UpdateUserDesc not in", values, "updateuserdesc");
            return (Criteria) this;
        }

        public Criteria andUpdateuserdescBetween(String value1, String value2) {
            addCriterion("UpdateUserDesc between", value1, value2, "updateuserdesc");
            return (Criteria) this;
        }

        public Criteria andUpdateuserdescNotBetween(String value1, String value2) {
            addCriterion("UpdateUserDesc not between", value1, value2, "updateuserdesc");
            return (Criteria) this;
        }

        public Criteria andUpdatedateIsNull() {
            addCriterion("UpdateDate is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedateIsNotNull() {
            addCriterion("UpdateDate is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedateEqualTo(Date value) {
            addCriterionForJDBCDate("UpdateDate =", value, "updatedate");
            return (Criteria) this;
        }

        public Criteria andUpdatedateNotEqualTo(Date value) {
            addCriterionForJDBCDate("UpdateDate <>", value, "updatedate");
            return (Criteria) this;
        }

        public Criteria andUpdatedateGreaterThan(Date value) {
            addCriterionForJDBCDate("UpdateDate >", value, "updatedate");
            return (Criteria) this;
        }

        public Criteria andUpdatedateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("UpdateDate >=", value, "updatedate");
            return (Criteria) this;
        }

        public Criteria andUpdatedateLessThan(Date value) {
            addCriterionForJDBCDate("UpdateDate <", value, "updatedate");
            return (Criteria) this;
        }

        public Criteria andUpdatedateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("UpdateDate <=", value, "updatedate");
            return (Criteria) this;
        }

        public Criteria andUpdatedateIn(List<Date> values) {
            addCriterionForJDBCDate("UpdateDate in", values, "updatedate");
            return (Criteria) this;
        }

        public Criteria andUpdatedateNotIn(List<Date> values) {
            addCriterionForJDBCDate("UpdateDate not in", values, "updatedate");
            return (Criteria) this;
        }

        public Criteria andUpdatedateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("UpdateDate between", value1, value2, "updatedate");
            return (Criteria) this;
        }

        public Criteria andUpdatedateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("UpdateDate not between", value1, value2, "updatedate");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeIsNull() {
            addCriterion("UpdateTime is null");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeIsNotNull() {
            addCriterion("UpdateTime is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeEqualTo(Date value) {
            addCriterionForJDBCTime("UpdateTime =", value, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeNotEqualTo(Date value) {
            addCriterionForJDBCTime("UpdateTime <>", value, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeGreaterThan(Date value) {
            addCriterionForJDBCTime("UpdateTime >", value, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("UpdateTime >=", value, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeLessThan(Date value) {
            addCriterionForJDBCTime("UpdateTime <", value, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeLessThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("UpdateTime <=", value, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeIn(List<Date> values) {
            addCriterionForJDBCTime("UpdateTime in", values, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeNotIn(List<Date> values) {
            addCriterionForJDBCTime("UpdateTime not in", values, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("UpdateTime between", value1, value2, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeNotBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("UpdateTime not between", value1, value2, "updatetime");
            return (Criteria) this;
        }

        public Criteria andOrdbillstatusIsNull() {
            addCriterion("OrdBillStatus is null");
            return (Criteria) this;
        }

        public Criteria andOrdbillstatusIsNotNull() {
            addCriterion("OrdBillStatus is not null");
            return (Criteria) this;
        }

        public Criteria andOrdbillstatusEqualTo(String value) {
            addCriterion("OrdBillStatus =", value, "ordbillstatus");
            return (Criteria) this;
        }

        public Criteria andOrdbillstatusNotEqualTo(String value) {
            addCriterion("OrdBillStatus <>", value, "ordbillstatus");
            return (Criteria) this;
        }

        public Criteria andOrdbillstatusGreaterThan(String value) {
            addCriterion("OrdBillStatus >", value, "ordbillstatus");
            return (Criteria) this;
        }

        public Criteria andOrdbillstatusGreaterThanOrEqualTo(String value) {
            addCriterion("OrdBillStatus >=", value, "ordbillstatus");
            return (Criteria) this;
        }

        public Criteria andOrdbillstatusLessThan(String value) {
            addCriterion("OrdBillStatus <", value, "ordbillstatus");
            return (Criteria) this;
        }

        public Criteria andOrdbillstatusLessThanOrEqualTo(String value) {
            addCriterion("OrdBillStatus <=", value, "ordbillstatus");
            return (Criteria) this;
        }

        public Criteria andOrdbillstatusLike(String value) {
            addCriterion("OrdBillStatus like", value, "ordbillstatus");
            return (Criteria) this;
        }

        public Criteria andOrdbillstatusNotLike(String value) {
            addCriterion("OrdBillStatus not like", value, "ordbillstatus");
            return (Criteria) this;
        }

        public Criteria andOrdbillstatusIn(List<String> values) {
            addCriterion("OrdBillStatus in", values, "ordbillstatus");
            return (Criteria) this;
        }

        public Criteria andOrdbillstatusNotIn(List<String> values) {
            addCriterion("OrdBillStatus not in", values, "ordbillstatus");
            return (Criteria) this;
        }

        public Criteria andOrdbillstatusBetween(String value1, String value2) {
            addCriterion("OrdBillStatus between", value1, value2, "ordbillstatus");
            return (Criteria) this;
        }

        public Criteria andOrdbillstatusNotBetween(String value1, String value2) {
            addCriterion("OrdBillStatus not between", value1, value2, "ordbillstatus");
            return (Criteria) this;
        }

        public Criteria andCreatedonIsNull() {
            addCriterion("CreatedOn is null");
            return (Criteria) this;
        }

        public Criteria andCreatedonIsNotNull() {
            addCriterion("CreatedOn is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedonEqualTo(Date value) {
            addCriterion("CreatedOn =", value, "createdon");
            return (Criteria) this;
        }

        public Criteria andCreatedonNotEqualTo(Date value) {
            addCriterion("CreatedOn <>", value, "createdon");
            return (Criteria) this;
        }

        public Criteria andCreatedonGreaterThan(Date value) {
            addCriterion("CreatedOn >", value, "createdon");
            return (Criteria) this;
        }

        public Criteria andCreatedonGreaterThanOrEqualTo(Date value) {
            addCriterion("CreatedOn >=", value, "createdon");
            return (Criteria) this;
        }

        public Criteria andCreatedonLessThan(Date value) {
            addCriterion("CreatedOn <", value, "createdon");
            return (Criteria) this;
        }

        public Criteria andCreatedonLessThanOrEqualTo(Date value) {
            addCriterion("CreatedOn <=", value, "createdon");
            return (Criteria) this;
        }

        public Criteria andCreatedonIn(List<Date> values) {
            addCriterion("CreatedOn in", values, "createdon");
            return (Criteria) this;
        }

        public Criteria andCreatedonNotIn(List<Date> values) {
            addCriterion("CreatedOn not in", values, "createdon");
            return (Criteria) this;
        }

        public Criteria andCreatedonBetween(Date value1, Date value2) {
            addCriterion("CreatedOn between", value1, value2, "createdon");
            return (Criteria) this;
        }

        public Criteria andCreatedonNotBetween(Date value1, Date value2) {
            addCriterion("CreatedOn not between", value1, value2, "createdon");
            return (Criteria) this;
        }

        public Criteria andUpdatedonIsNull() {
            addCriterion("UpdatedOn is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedonIsNotNull() {
            addCriterion("UpdatedOn is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedonEqualTo(Date value) {
            addCriterion("UpdatedOn =", value, "updatedon");
            return (Criteria) this;
        }

        public Criteria andUpdatedonNotEqualTo(Date value) {
            addCriterion("UpdatedOn <>", value, "updatedon");
            return (Criteria) this;
        }

        public Criteria andUpdatedonGreaterThan(Date value) {
            addCriterion("UpdatedOn >", value, "updatedon");
            return (Criteria) this;
        }

        public Criteria andUpdatedonGreaterThanOrEqualTo(Date value) {
            addCriterion("UpdatedOn >=", value, "updatedon");
            return (Criteria) this;
        }

        public Criteria andUpdatedonLessThan(Date value) {
            addCriterion("UpdatedOn <", value, "updatedon");
            return (Criteria) this;
        }

        public Criteria andUpdatedonLessThanOrEqualTo(Date value) {
            addCriterion("UpdatedOn <=", value, "updatedon");
            return (Criteria) this;
        }

        public Criteria andUpdatedonIn(List<Date> values) {
            addCriterion("UpdatedOn in", values, "updatedon");
            return (Criteria) this;
        }

        public Criteria andUpdatedonNotIn(List<Date> values) {
            addCriterion("UpdatedOn not in", values, "updatedon");
            return (Criteria) this;
        }

        public Criteria andUpdatedonBetween(Date value1, Date value2) {
            addCriterion("UpdatedOn between", value1, value2, "updatedon");
            return (Criteria) this;
        }

        public Criteria andUpdatedonNotBetween(Date value1, Date value2) {
            addCriterion("UpdatedOn not between", value1, value2, "updatedon");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIsNull() {
            addCriterion("CreatedBy is null");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIsNotNull() {
            addCriterion("CreatedBy is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedbyEqualTo(String value) {
            addCriterion("CreatedBy =", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotEqualTo(String value) {
            addCriterion("CreatedBy <>", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyGreaterThan(String value) {
            addCriterion("CreatedBy >", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyGreaterThanOrEqualTo(String value) {
            addCriterion("CreatedBy >=", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLessThan(String value) {
            addCriterion("CreatedBy <", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLessThanOrEqualTo(String value) {
            addCriterion("CreatedBy <=", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLike(String value) {
            addCriterion("CreatedBy like", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotLike(String value) {
            addCriterion("CreatedBy not like", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIn(List<String> values) {
            addCriterion("CreatedBy in", values, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotIn(List<String> values) {
            addCriterion("CreatedBy not in", values, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyBetween(String value1, String value2) {
            addCriterion("CreatedBy between", value1, value2, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotBetween(String value1, String value2) {
            addCriterion("CreatedBy not between", value1, value2, "createdby");
            return (Criteria) this;
        }

        public Criteria andUpdatedbyIsNull() {
            addCriterion("UpdatedBy is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedbyIsNotNull() {
            addCriterion("UpdatedBy is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedbyEqualTo(String value) {
            addCriterion("UpdatedBy =", value, "updatedby");
            return (Criteria) this;
        }

        public Criteria andUpdatedbyNotEqualTo(String value) {
            addCriterion("UpdatedBy <>", value, "updatedby");
            return (Criteria) this;
        }

        public Criteria andUpdatedbyGreaterThan(String value) {
            addCriterion("UpdatedBy >", value, "updatedby");
            return (Criteria) this;
        }

        public Criteria andUpdatedbyGreaterThanOrEqualTo(String value) {
            addCriterion("UpdatedBy >=", value, "updatedby");
            return (Criteria) this;
        }

        public Criteria andUpdatedbyLessThan(String value) {
            addCriterion("UpdatedBy <", value, "updatedby");
            return (Criteria) this;
        }

        public Criteria andUpdatedbyLessThanOrEqualTo(String value) {
            addCriterion("UpdatedBy <=", value, "updatedby");
            return (Criteria) this;
        }

        public Criteria andUpdatedbyLike(String value) {
            addCriterion("UpdatedBy like", value, "updatedby");
            return (Criteria) this;
        }

        public Criteria andUpdatedbyNotLike(String value) {
            addCriterion("UpdatedBy not like", value, "updatedby");
            return (Criteria) this;
        }

        public Criteria andUpdatedbyIn(List<String> values) {
            addCriterion("UpdatedBy in", values, "updatedby");
            return (Criteria) this;
        }

        public Criteria andUpdatedbyNotIn(List<String> values) {
            addCriterion("UpdatedBy not in", values, "updatedby");
            return (Criteria) this;
        }

        public Criteria andUpdatedbyBetween(String value1, String value2) {
            addCriterion("UpdatedBy between", value1, value2, "updatedby");
            return (Criteria) this;
        }

        public Criteria andUpdatedbyNotBetween(String value1, String value2) {
            addCriterion("UpdatedBy not between", value1, value2, "updatedby");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table d_medical_orders
     *
     * @mbg.generated do_not_delete_during_merge Wed Dec 25 09:52:34 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table d_medical_orders
     *
     * @mbg.generated Wed Dec 25 09:52:34 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}