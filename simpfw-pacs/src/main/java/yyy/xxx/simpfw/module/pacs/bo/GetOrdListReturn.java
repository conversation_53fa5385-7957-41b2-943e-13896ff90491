package yyy.xxx.simpfw.module.pacs.bo;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlRootElement(name = "GetOrdListReturn")
public class GetOrdListReturn extends AbstractReturn {

    private List<PatList> patLists;

    public List<PatList> getPatLists() {
        return patLists;
    }

    @XmlElementWrapper(name = "PatLists")
    @XmlElement(name = "PatList")
    public void setPatLists(List<PatList> patLists) {
        this.patLists = patLists;
    }
}
