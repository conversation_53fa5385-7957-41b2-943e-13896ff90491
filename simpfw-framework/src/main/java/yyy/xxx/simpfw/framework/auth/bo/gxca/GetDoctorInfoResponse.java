package yyy.xxx.simpfw.framework.auth.bo.gxca;

import lombok.Data;

@Data
public class GetDoctorInfoResponse {

    private String doctorId;

    private String doctorNum;

    private String doctorJobNo;

    private String doctorName;

    private int doctorGender;

    private String doctorPhone;

    private String doctorIdType;

    private String doctorIdCard;

    private String departmentName;

    private String departmentNum;

    private int firstApprovalStatus;

    private String firstApprovalResult;

    private int secondApprovalStatus;

    private String secondApprovalResult;

    private String signImg;

}
