package yyy.xxx.simpfw.framework.auth.web;

import com.alibaba.fastjson.JSON;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.NumberUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import yyy.xxx.simpfw.common.core.domain.AjaxResult;
import yyy.xxx.simpfw.common.core.domain.model.LoginUser;
import yyy.xxx.simpfw.common.core.redis.RedisCache;
import yyy.xxx.simpfw.common.utils.SecurityUtils;
import yyy.xxx.simpfw.common.utils.StringUtils;
import yyy.xxx.simpfw.common.utils.file.ImageUtils;
import yyy.xxx.simpfw.framework.auth.QRAuthConst;
import yyy.xxx.simpfw.framework.auth.bo.CertAuthCode;
import yyy.xxx.simpfw.framework.auth.bo.CertAuthResponse;
import yyy.xxx.simpfw.framework.auth.bo.gxca.*;
import yyy.xxx.simpfw.framework.auth.constant.CAProvider;
import yyy.xxx.simpfw.framework.auth.entity.ExamCASignEntity;
import yyy.xxx.simpfw.framework.auth.entity.ExamCASignEntityWithBLOBs;
import yyy.xxx.simpfw.framework.auth.service.GXCAQRAuthService;

import javax.servlet.http.HttpServletRequest;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 易手签CA扫码认证
 */
@Controller
@RequestMapping(value = "/gxcaqrauth")
public class GXCAQRAuthController implements QRAuthConst {

    private static final Logger logger = LoggerFactory.getLogger(GXCAQRAuthController.class);

    private static final String paramSessionId = "sessionId";

    @Autowired
    private GXCAQRAuthService service;

    @Autowired
    protected RedisCache redisCache;


    /**
     * `获取二维码
     *
     * @param request 请求
     */
    @RequestMapping(value = "/code")
    @ResponseBody
    public AjaxResult code(HttpServletRequest request) {
        if (!service.checkConfig()) {
            return AjaxResult.error(500, "易手签接口配置错误，请联系管理员。");
        }

        String sessionId = request.getParameter(paramSessionId);
        if (StringUtils.isBlank(sessionId)) {
            logger.error("sessionId为空.");
            return AjaxResult.error(500, "sessionId状态异常，请重试。");
        }
        //获取二维码
        try {
            GetQRCodeResponse qrResp = service.getQRCode();

            //用于轮询扫码结果
            String codeId = qrResp.getNumber();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            long expireTime = sdf.parse(qrResp.getExpireTime()).getTime();
            CertAuthCode authCode = new CertAuthCode(codeId, null, expireTime);
            redisCache.setCacheObject(String.format(cacheKeyPattern, sessionId), authCode, expireTime - System.currentTimeMillis(), TimeUnit.MILLISECONDS);

            AjaxResult res = AjaxResult.success();
            String codeData = qrResp.getQrCode().split("base64,")[1];
            res.put(AjaxResult.DATA_TAG, Base64.decodeBase64(codeData));

            return res;
        } catch (HttpServerException e) {
            logger.error(e.getMessage(), e);
            return AjaxResult.error(500, e.getMessage());
        } catch (UnknownHostException e) {
            logger.error(e.getMessage(), e);
            return AjaxResult.error(500, "网络错误，获取二维码失败，请联系网络运维人员");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return AjaxResult.error(500, "获取二维码失败，请联系本系统技术支持人员");
        }
    }

    /**
     * `给定sessionId授权状态
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/authcheck")
    @ResponseBody
    public AjaxResult authcheck(HttpServletRequest request) {

        String sessionId = request.getParameter(paramSessionId);
        String forRel = request.getParameter("forRel");

        LoginUser luser = null;
        try {
            luser = service.authcheck(sessionId, "true".equals(forRel));
        } catch (HttpServerException caServerErr) {
            logger.warn(caServerErr.getMessage());
        } catch (Exception err) {
            logger.error(err.getMessage(), err);
            return AjaxResult.error(err.getMessage());
        }

        AjaxResult ret = AjaxResult.success();
        if (null != luser) {
            ret.put("token", luser.getToken());
        }
        return ret;
    }

    @RequestMapping(value = "/createSign")
    @ResponseBody
    public AjaxResult createSign(@RequestPart("signFile") MultipartFile signFile) {
        String signNum;
        try {
            String signContent = ImageUtils.imageToBase64(signFile.getInputStream());
            signNum = service.createSign(signContent);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success("创建签名", signNum);
    }

    @RequestMapping(value = "/getSignInfo")
    @ResponseBody
    public AjaxResult getSignInfo(@RequestPart("signNum") String signNum) {
        GetSignInfoResponse response;
        try {
            response = service.getSignInfo(signNum);
            redisCache.setCacheObject(String.format(cacheOutputDataPattern, SecurityUtils.getLoginUser().getToken()), response, expiredTime, TimeUnit.SECONDS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success(response);
    }

    @RequestMapping(value = "/saveSign")
    @ResponseBody
    public AjaxResult saveSign(@RequestPart("examUid") String examUid,
                               @RequestPart("signFilePath") String signFilePath) {
        try {
            String loginToken = SecurityUtils.getLoginUser().getToken();
            logger.info("登录用户token：{}", loginToken);
            GetSignInfoResponse signOutput = redisCache.getCacheObject(String.format(cacheOutputDataPattern, loginToken));
            CertAuthCode authCode = redisCache.getCacheObject(String.format(cacheKeyPattern, loginToken));
            GetDoctorInfoResponse doctorInfo = redisCache.getCacheObject(String.format(cacheTokenPattern, loginToken));

            if (null == authCode || null == doctorInfo) {
                return AjaxResult.error("登录信息已过期，请重新扫码登录");
            }
            if (null == signOutput) {
                return AjaxResult.error("签署信息已过期，请重新审核签署。");
            }

            service.dataVerify(signOutput.getSignContent(), signOutput.getSignResult(), signOutput.getCert());
            service.tssVerify(signOutput.getSignContent(), signOutput.getTssData());

            ExamCASignEntityWithBLOBs signEntity = new ExamCASignEntityWithBLOBs();
            signEntity.setExamuid(examUid);
            signEntity.setLoginUserId(SecurityUtils.getUserId());
            signEntity.setSignUserId(Long.valueOf(authCode.getUserUid()));
            signEntity.setCaProvider(CAProvider.GXCA.getCode());
            signEntity.setFilePath(signFilePath);
            signEntity.setSignInput("");
            signEntity.setSignOutput(JSON.toJSONString(signOutput));
            service.saveCASign(signEntity);

            //签署成功后自动续期auth状态
            service.renewAuth(authCode, doctorInfo);
            return AjaxResult.success("签署成功。");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * `检查配置
     *
     * @return
     */
    @RequestMapping(value = "/cfg")
    @ResponseBody
    public AjaxResult config(HttpServletRequest request) {
        try {
            if (!service.checkConfig()) {
                return AjaxResult.error();
            }

            return AjaxResult.success();
        } catch (Exception err) {
            return AjaxResult.error(err.getMessage());
        }
    }
}
