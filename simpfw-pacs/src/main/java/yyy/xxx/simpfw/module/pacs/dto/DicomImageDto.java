package yyy.xxx.simpfw.module.pacs.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import yyy.xxx.simpfw.module.pacs.Const;
import yyy.xxx.simpfw.module.pacs.entity.DicomImage;
import yyy.xxx.simpfw.module.pacs.entity.DicomSeries;
import yyy.xxx.simpfw.module.pacs.entity.DicomStudy;
import yyy.xxx.simpfw.module.pacs.entity.ExamInfo;

import java.util.Date;
import java.util.List;

public class DicomImageDto extends DicomImage {
    private ExamInfo examInfo;

    private DicomSeries dicomSeries;

    public DicomStudy getDicomStudy() {
        return dicomStudy;
    }

    public void setDicomStudy(DicomStudy dicomStudy) {
        this.dicomStudy = dicomStudy;
    }

    private DicomStudy dicomStudy;

    public DicomSeries getDicomSeries() {
        return dicomSeries;
    }

    public void setDicomSeries(DicomSeries dicomSeries) {
        this.dicomSeries = dicomSeries;
    }

    public ExamInfo getExamInfo() {
        return examInfo;
    }

    public void setExamInfo(ExamInfo examInfo) {
        this.examInfo = examInfo;
    }

    public Date getLastUploadTime() {
        return lastUploadTime;
    }

    public void setLastUploadTime(Date lastUploadTime) {
        this.lastUploadTime = lastUploadTime;
    }

    /**
     * 最后上传时间
     */
    private Date lastUploadTime;
}
