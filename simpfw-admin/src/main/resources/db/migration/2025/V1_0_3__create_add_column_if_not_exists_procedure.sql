USE mt_db_manager;
DROP PROCEDURE IF EXISTS add_column_if_not_exists;
DEL<PERSON><PERSON>ER $$

CREATE PROCEDURE add_column_if_not_exists(
    IN db_name VARCHAR(64),
    IN tbl_name VARCHAR(64),
    IN col_name VARCHAR(64),
    IN col_def  VARCHAR(255)
)
BEGIN
    DECLARE col_count INT DEFAULT 0;
    SELECT COUNT(*) INTO col_count
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = db_name
      AND TABLE_NAME = tbl_name
      AND COLUMN_NAME = col_name;

    IF col_count = 0 THEN
        SET @ddl = CONCAT('ALTER TABLE `', db_name, '`.`', tbl_name, '` ADD COLUMN ', col_name, ' ', col_def);
        PREPARE stmt FROM @ddl;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END$$

DELIMITER ;