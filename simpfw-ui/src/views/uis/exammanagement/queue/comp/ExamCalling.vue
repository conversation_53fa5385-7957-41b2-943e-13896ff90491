<template>
<el-dialog title="呼叫验证" :visible.sync="visible" class="popupdialog" width="600px" append-to-body>
  <el-form ref="validForm" :model="validForm" label-width="80px" class="tight-form">
    <div class="fieldset-legend">患者信息确认</div>
    <el-row>
      <el-col :span="12">
        <el-form-item label="病历号">
          <el-input v-model="validForm.medicalRecordNo" @change="doValid" />
        </el-form-item>
      </el-col>
      <el-col :span="12">&nbsp;</el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="患者姓名">
          {{validForm.name}}
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="出生日期">
          {{validForm.birthday? validForm.birthday.split(" ")[0] : ''}}
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="性别">
          {{validForm.gender? validForm.gender.dictLabel : ''}}
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>

  <el-form ref="examForm" :model="examForm" label-width="80px" class="tight-form">
    <div class="fieldset-legend">当前呼叫患者</div>
    <el-row>
      <el-col :span="12">
        <el-form-item label="患者姓名">
          {{examForm.patientInfo.name}}
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="年龄">
          {{examForm.patientInfo.age}}{{examForm.patientInfo.ageUnit? examForm.patientInfo.ageUnit.dictLabel : ''}}
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="性别">
          {{examForm.patientInfo.gender? examForm.patientInfo.gender.dictLabel : ''}}
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="出生日期">
          {{examForm.patientInfo.birthday? examForm.patientInfo.birthday.split(" ")[0] : ''}}
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="检查部位">
          {{examForm.examParts? examForm.examParts.map(p => p.partsName).join(',') : ''}}              
        </el-form-item>
      </el-col>

      <el-col :span="24">
        <el-form-item label="临床诊断">
          <el-input type="textarea" :rows="4" v-model="examForm.clinicDiagnosis" />              
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="过敏史">
          <el-input type="textarea" :rows="4" v-model="examForm.allergyHistory" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <div slot="footer" class="dialog-footer">
    <el-button type="primary" @click="examPrerequire">延迟检查</el-button>
    <el-button type="primary" @click="beforeCall" :loading="calling">{{buttonTextCall}}</el-button>
    <el-button :type="examPrerequireExists? 'warning' : 'primary'" @click="toExam">开始检查</el-button>
  </div>
</el-dialog>
</template>

<script>

import model from "@/assets/scripts/gis/exammanagement/queue/comp/ExamCalling";
export default model;
</script>
