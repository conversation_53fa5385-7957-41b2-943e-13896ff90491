package yyy.xxx.simpfw.framework.auth.bo;

/**
 * `请求accessToken接口返回数据
 *<pre>
 * {"status": "0","message": "success","data": {
 * "accessToken": "191dc067b9774747b3db1d8b485a477e_11"
 * }
 * }
 *</pre>
 */
public class QRAuthAccessToken {
	private String accessToken;

	public String getAccessToken() {
		return accessToken;
	}
	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}
	
}
