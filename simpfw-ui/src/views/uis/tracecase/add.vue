 
<template>

  <div style="height: 520px;">
    <el-form ref="form" :model="form" label-width="100px">

    <el-row :gutter="80">
     <el-col :span="3">
        <el-form-item label="患者姓名：">
          <div style="width: 100px;"><span>周杰伦</span></div>
        </el-form-item>
      </el-col>

      <el-col :span="3">
        <el-form-item label="性别：">
          <div style="width: 100px;"><span>男</span></div>
        </el-form-item>
      </el-col>

      <el-col :span="3">
        <el-form-item label="年龄：">
           <div style="width: 100px;"><span>41</span></div>
        </el-form-item>
       </el-col>

      <el-col :span="5">
        <el-form-item label="住院号：">
           <div style="width: 100px;"><span>123456</span></div>
        </el-form-item>
      </el-col>

      <el-col :span="10">
        <el-form-item label="检查号：">
           <div style="width: 100px;"><span>123456</span></div>
        </el-form-item>
      </el-col>
    </el-row>

   <el-row :gutter="80" style="margin-top: -500px;">
      <el-col :span="9">
        <el-form-item label="联系方式：">
           <div style="width: 300px;"><span>广西省南宁市</span></div>
        </el-form-item>
      </el-col>

      <el-col :span="5">
        <el-form-item label="检查医师：">
           <div style="width: 300px;"><span>周杰伦</span></div>
        </el-form-item>
      </el-col>

      <el-col :span="4">
        <el-form-item label="检查日期：">
           <div style="width: 300px;"><span>2022-12-1</span></div>
        </el-form-item>
      </el-col>
    </el-row>


    <el-row :gutter="80" style="float: left;">
        <el-form-item label="检查结论">
          <el-input type="textarea" v-model="form.desc" rows="2" style="width: 450px;"></el-input>
        </el-form-item>

        <el-form-item label="辅助检查">
          <el-input type="textarea" v-model="form.desc" rows="2" style="width: 450px;"></el-input>
        </el-form-item>

        <el-form-item label="手术结论">
          <el-input type="textarea" v-model="form.desc" rows="2" style="width: 450px;"></el-input>
        </el-form-item>

        <el-form-item label="病理结果">
          <el-input type="textarea" v-model="form.desc" rows="2" style="width: 450px;"></el-input>
        </el-form-item>

        <el-form-item label="追踪结论">
          <el-input type="textarea" v-model="form.desc" rows="2" style="width: 450px;"></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSubmit">保存</el-button>
          <el-button style="margin-left: 20px;">取消</el-button>
        </el-form-item>
    </el-row>


      <el-row :gutter="80" style="float: left; margin-left: 150px;">
          <el-form-item label="追踪状态" style="border: 1px solid gray; padding: 5px;">
            <el-radio-group v-model="form.resource" size="mini">
              <div><el-radio label="不追踪"></el-radio></div>
              <div style="margin-top: 10px"><el-radio label="计划追踪"></el-radio></div>
              <div style="margin-top: 10px"><el-radio label="正在追踪"></el-radio></div>
              <div style="margin-top: 10px"><el-radio label="追踪完成"></el-radio></div>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="符合情况" style="border: 1px solid gray; padding: 5px;">
            <el-radio-group v-model="form.resource">
              <div style="margin-top: 10px"><el-radio label="不符合"></el-radio></div>
              <div style="margin-top: 10px"><el-radio label="基本符合"></el-radio></div>
              <div style="margin-top: 10px"><el-radio label="符合"></el-radio></div>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="计划追踪日期">
              <el-date-picker type="date" placeholder="选择日期" v-model="form.date1" style="width: 150px;"></el-date-picker>
          </el-form-item>

          <el-form-item label="追踪日期">
              <el-date-picker type="date" placeholder="选择日期" v-model="form.date2" style="width: 150px;"></el-date-picker>
          </el-form-item>

          <el-form-item label="追踪医师">
              <el-input v-model="form.name" style="width: 150px;"></el-input>
          </el-form-item>
         
      </el-row>
    </el-form>
  </div>
</template>


<style scoped>
  .el-row {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .el-col {
    border-radius: 4px;
    height: 30px;
  }
  .bg-purple-dark {
    background: #99a9bf;
  }
  .bg-purple {
    background: #d3dce6;
  }
  .bg-purple-light {
    background: #e5e9f2;
  }
  .grid-content {
    border-radius: 4px;
    min-height: 36px;
  }
  .row-bg {
    padding: 10px 0;
    background-color: #f9fafc;
  }
  textarea {
    overflow-y: scroll;
  }
</style>


<script>
  export default {
    name: "traceCaseAdd",
    data() {
      return {
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        }
      }
    },
    methods: {
      onSubmit() {
        console.log('submit!');
      }
    }
  }
</script>