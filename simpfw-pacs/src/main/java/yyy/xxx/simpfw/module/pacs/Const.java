package yyy.xxx.simpfw.module.pacs;

import yyy.xxx.common.net.storage.Consts;
import yyy.xxx.common.net.storage.SessionOption;

public interface Const extends Consts {
	String SYS_MODALITY = "uis";
	
    String EMPTY = "", ZERO = "0", CHARSET_UTF8 = "UTF-8", CHARSET_ZH = "GBK", BLANK = " ", NVL = "null";
    String EMPTY_OBJECT = "{}";

    String SYMBOL_BRACE_OPEN = "{", SYMBOL_BRACE_CLOSE = "}", SYMBOL_COLON = ":", SYMBOL_SLASH = "/", SYMBOL_PLUS = "+"
            , SYMBOL_AMP = "&", SYMBOL_CAR = "\r", SYMBOL_NL = "\n";

    String DICT_TYPE_GENDER = "uis_gender_type", DICT_TYPE_MARITAL_STATUS = "comm_married_status"
            , DICT_TYPE_NATION = "comm_nation", DICT_TYPE_OCCUPATION = "comm_occupation"
            , DICT_TYPE_ADM_TYPE = "uis_inp_type", DICT_TYPE_COSTS_TYPE = "uis_exam_cost_type"
            , DICT_TYPE_INP_WARD = "uis_inp_ward", DICT_TYPE_INP_ROOM = "uis_inp_room"
            , DICT_TYPE_RESULT_STATUS = "uis_exam_result_status",DICT_TYPE_AGE_UNIT="uis_age_unit";

    String CACHE_KEY_FMT = "%s::%s", CACHE_KEY = String.format(CACHE_KEY_FMT, SYS_MODALITY, "%s");

    String SFMT_DATETIME = "%1$tY%1$tm%1$td%1$tH%1$tM%1$tS", SFMT_MIN = "%1$tY%1$tm%1$td%1$tH%1$tM", SFMT_DATE = "%1$tY%1$tm%1$td";

    SessionOption STORAGE_SESSION_OPTION = new SessionOption(true, 16, 4);

    String FILE_TYPE_DCM = "1", FILE_TYPE_NAME_DCM = "dcm", FILE_TYPE_JPG = "2", FILE_TYPE_NAME_JPG = "jpg", FILE_TYPE_VOD = "3", FILE_TYPE_NAME_VOD = "vod";
    String DICT_DEPT_CONFIG_KEY = "detpCustomizeConfig";
//    String DICT_DEPT_CONFIG_KEY_NEW_MODEL = "detpCustomizeConfigNewModel";

    /**
     * `加解密密钥
     */
    public static final String secretKey = "$,$$,$$$", percent = "%"
            , charset_GBK = "GBK", star = "*";

    String DATA_SOURCE_DICT_TYPE = "data_source";
}
