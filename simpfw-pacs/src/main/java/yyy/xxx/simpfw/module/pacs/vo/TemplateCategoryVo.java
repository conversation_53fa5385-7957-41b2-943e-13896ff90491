package yyy.xxx.simpfw.module.pacs.vo;

import yyy.xxx.simpfw.module.pacs.entity.TemplateCategory;
import yyy.xxx.simpfw.common.core.domain.TreeSelect;

public class TemplateCategoryVo extends TemplateCategory {
    public TreeSelect convTreeNode() {
        TreeSelect node = new TreeSelect();
        node.setId(getId());
        node.setLabel(getCateName());
        node.setData(this);

        return node;
    }
}
