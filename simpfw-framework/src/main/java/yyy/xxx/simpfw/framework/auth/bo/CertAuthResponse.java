package yyy.xxx.simpfw.framework.auth.bo;

import java.util.Map;

/**
 * `请求签名返回, 参照文档
 */
public class CertAuthResponse<T> {
	private CertAuthResponseResult responseResult;
	
	private Map<String, T> contents;

	public CertAuthResponseResult getResponseResult() {
		return responseResult;
	}
	public void setResponseResult(CertAuthResponseResult responseResult) {
		this.responseResult = responseResult;
	}

	public Map<String, T> getContents() {
		return contents;
	}
	public void setContents(Map<String, T> contents) {
		this.contents = contents;
	}
}
