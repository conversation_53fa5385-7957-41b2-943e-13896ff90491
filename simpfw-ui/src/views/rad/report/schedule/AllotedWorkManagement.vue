<template>
  <div class="app-container">
    <div style="height: 20%; width: 100%">
      <el-form ref="queryForm" size="small" :inline="true" label-width="88px">
        
        <el-row>
          <el-col :span="5">
            <el-date-picker
              v-model="searchForm.dateRange"
              style="width: 240px"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          
            </el-col>
          <el-col :span="5">
            <el-form-item label=" ">
              <el-select
              clearable
                v-model="editUserGroup.selectGroup"
                placeholder="请选择分组"
              >
                <el-option
                  v-for="item in editUserGroup.groupOptions"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
                >
                </el-option>
              </el-select>
        </el-form-item>
          </el-col>
              <el-button type="primary" @click="preview">预览</el-button>
              <el-button type="primary" @click="repeated">复用</el-button>
              <el-button type="primary" @click="test">分组</el-button>
              <el-button type="primary" @click="fetchData">导出</el-button>
        </el-row>
        
      </el-form>
      
      <el-footer height="unset">
                <div class="foot-tools" style="float: right">               
            <el-button type="primary" @click="search">查询</el-button>
            <!-- size="mini"  icon="el-icon-user-solid"  -->
            <el-button slot="primary"  @click="toAllotedWorkEdit()">添加</el-button>
            <!-- <el-button type="primary" @click="addAlloteWork">添加</el-button> -->
                </div>
              </el-footer>
      
    </div>

    <div style="height: 80%; width: 100%">
      <el-row style="height: 95%">
        <el-col style="height: 100%; width: 100%">
          <el-table
            v-loading="loading"
            :data="grid.data"
            row-key="id"
            height="100%"
            stripe
            highlight-current-row
            @cell-dblclick="cellDbClick"
          >
            
              <el-table-column prop="userName" label="医生" width="160" />
              <el-table-column v-for="(tableColumn) in tableColumns" :key="tableColumn.label" :prop="tableColumn.prop"  :label="tableColumn.label"/>
          </el-table>
        </el-col>
      </el-row>
    </div>
    <el-dialog title=""
    :visible="openPreview"
    style="height: 100%; width: 100%"
    width="1500px"
    @close="openPreview=false"
    >
      <el-row style="height: 100%">
        <el-col style="height: 100%; width: 100%">
          <el-table
            v-loading="loading"
            :data="grid.data"
            row-key="id"
            height="100%"
            stripe
            highlight-current-row
            @cell-dblclick="cellDbClick"
            :row-style="{height:'40px'}"
            
          >
            
              <el-table-column  prop="userName" label="医生" width="160" />
              <el-table-column v-for="(tableColumn) in tableColumns" :key="tableColumn.label" :prop="tableColumn.prop"  :label="tableColumn.label"/>
          </el-table>
        </el-col>
      </el-row>
    </el-dialog>

  <AllotedWorkEdit @search="search" ref="allotedWorkEdit"/>
  <GroupAllotedWork ref="groupAllotedWork"/>
  <Test ref="test"/>
  </div>
</template>
    
<style scoped src="@/assets/styles/pacs/exammanagement/patient/Index.css"></style>
<style scoped src="@/assets/styles/pacs/exammanagement/patient/exam-info-icon.css"></style>
<script>
import model from "@/assets/scripts/rad/report/schedule/AllotedWorkManagement";
export default model;
</script>
    