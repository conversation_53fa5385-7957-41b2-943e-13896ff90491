package yyy.xxx.simpfw.module.uis.entity;

import java.util.Date;
import java.util.Map;

public class TraceCase implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	
  	/** ID */
	private Long id;
		
  	/** 检查ID */
	private Long examInfoId;
		
  	/** 检查结论 */
	private String examConclusion;
		
  	/** 辅助检查 */
	private String auxiliaryExam;
		
  	/** 手术结论 */
	private String surgeryConclusion;
		
  	/** 病理结果 */
	private String pathologyResult;
		
  	/** 追踪结论 */
	private String traceConclusion;
		
  	/** 确认状态：0-符合，1-基本符合， 2-不符合 */
	private Integer confirmStatus;
		
  	/** 追踪状态：0-不追踪， 1-计划追踪， 2-正在追踪， 3-继续追踪, 4-追踪完成 */
	private Integer traceStatus;
		
  	/** 追踪日期 */
	private String traceDate;
		
  	/** 计划追踪日期 */
	private String planTraceDate;

	/** 追踪医生code */
    private String traceDoctorCode;

    /** 追踪医生姓名 */
    private String traceDoctorName;
    
  	/** 创建时间 */
	private Date createTime;
		
  	/** 修改时间 */
	private Date updateTime;
	
	// NOTE，这里使用 map
	private Map<String, Object> examInfo;
	
	// NOTE，这里使用 map
	private Map<String, Object> patientInfo;
	
	public Long getId() {
		return id;
	}
	
	public void setId(Long id) {
		this.id = id;
	}
		
	public Long getExamInfoId() {
		return examInfoId;
	}
	
	public void setExamInfoId(Long examInfoId) {
		this.examInfoId = examInfoId;
	}
		
	public String getExamConclusion() {
		return examConclusion;
	}
	
	public void setExamConclusion(String examConclusion) {
		this.examConclusion = examConclusion;
	}
		
	public String getAuxiliaryExam() {
		return auxiliaryExam;
	}
	
	public void setAuxiliaryExam(String auxiliaryExam) {
		this.auxiliaryExam = auxiliaryExam;
	}
		
	public String getSurgeryConclusion() {
		return surgeryConclusion;
	}
	
	public void setSurgeryConclusion(String surgeryConclusion) {
		this.surgeryConclusion = surgeryConclusion;
	}
		
	public String getPathologyResult() {
		return pathologyResult;
	}
	
	public void setPathologyResult(String pathologyResult) {
		this.pathologyResult = pathologyResult;
	}
		
	public String getTraceConclusion() {
		return traceConclusion;
	}
	
	public void setTraceConclusion(String traceConclusion) {
		this.traceConclusion = traceConclusion;
	}
		
	public Integer getConfirmStatus() {
		return confirmStatus;
	}
	
	public void setConfirmStatus(Integer confirmStatus) {
		this.confirmStatus = confirmStatus;
	}
		
	public Integer getTraceStatus() {
		return traceStatus;
	}
	
	public void setTraceStatus(Integer traceStatus) {
		this.traceStatus = traceStatus;
	}
		
	public String getTraceDate() {
		return traceDate;
	}

	public void setTraceDate(String traceDate) {
		this.traceDate = traceDate;
	}

	public String getPlanTraceDate() {
		return planTraceDate;
	}

	public void setPlanTraceDate(String planTraceDate) {
		this.planTraceDate = planTraceDate;
	}

	public Date getCreateTime() {
		return createTime;
	}
	
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
		
	public Date getUpdateTime() {
		return updateTime;
	}
	
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Map<String, Object> getExamInfo() {
		return examInfo;
	}

	public void setExamInfo(Map<String, Object> examInfo) {
		this.examInfo = examInfo;
	}

	public Map<String, Object> getPatientInfo() {
		return patientInfo;
	}

	public void setPatientInfo(Map<String, Object> patientInfo) {
		this.patientInfo = patientInfo;
	}

	public String getTraceDoctorCode() {
		return traceDoctorCode;
	}

	public void setTraceDoctorCode(String traceDoctorCode) {
		this.traceDoctorCode = traceDoctorCode;
	}

	public String getTraceDoctorName() {
		return traceDoctorName;
	}

	public void setTraceDoctorName(String traceDoctorName) {
		this.traceDoctorName = traceDoctorName;
	}

	
}
