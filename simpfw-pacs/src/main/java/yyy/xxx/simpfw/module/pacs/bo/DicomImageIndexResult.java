package yyy.xxx.simpfw.module.pacs.bo;

public class DicomImageIndexResult {
    private Integer numErrors, numIndexed, columns, rows, code;

    private String studyInstanceUID, message;

    public Integer getColumns() {
        return columns;
    }
    public void setColumns(Integer columns) {
        this.columns = columns;
    }

    public Integer getRows() {
        return rows;
    }
    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getNumErrors() {
        return numErrors;
    }
    public void setNumErrors(Integer numErrors) {
        this.numErrors = numErrors;
    }

    public Integer getNumIndexed() {
        return numIndexed;
    }
    public void setNumIndexed(Integer numIndexed) {
        this.numIndexed = numIndexed;
    }

    public String getStudyInstanceUID() {
        return studyInstanceUID;
    }
    public void setStudyInstanceUID(String studyInstanceUID) {
        this.studyInstanceUID = studyInstanceUID;
    }
    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * `上传结果是否有效: 有成功数和失败数或者有明确错误
     * @return
     */
    public boolean validate() {
        return null != numIndexed && null != numErrors || null != code;
    }

}
