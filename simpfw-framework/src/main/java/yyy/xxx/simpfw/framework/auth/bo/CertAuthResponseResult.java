package yyy.xxx.simpfw.framework.auth.bo;

public class CertAuthResponseResult {
    /**<pre>
     *  0	成功                          操作成功
     * -1	系统出错                      云密钥系统错误
     * -2	身份未认证或令牌userToken已失效  userToken失效或错误
     * -3	口令（PIN）错误                用户口令错误，解不开用户私钥
     * -4	账号被锁住，请联系管理人解锁      口令错误次数超过规定值，被锁
     * -5	口令（PIN）错误太频繁，被限制认证	口令错误太频繁被限制
     * 100	入参错误                      特殊参数校验不满足
     * </pre>*/

    public enum Status {
        SUCCESS(0)
        , SYSERR(-1)
        , TOKENERR(-2)
        , PINERR(-3)
        , LOCKED(-4)
        , PINERRS(-5)
        , PARAMSERR(100)
        , UN(-9999);

        private int code;
        public int getCode() { return code; }
        private Status(int code) {
            this.code = code;
        }
    };

    private Integer status = Status.SUCCESS.getCode();
    public Integer getStatus() {
        return status;
    }
    public CertAuthResponseResult setStatus(Integer status) {
        this.status = status;

        return this;
    }

    private String msg;
    public String getMsg() {
        return msg;
    }
    public CertAuthResponseResult setMsg(String msg) {
        this.msg = msg;

        return this;
    }
}
