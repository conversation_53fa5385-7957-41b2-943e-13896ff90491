package yyy.xxx.simpfw.module.pacs.bo;

import com.alibaba.fastjson.TypeReference;

/**
 * {"loc":"\\abc:def@ip\share","un":"un"}
 */
public class StorageParam {

    public static final TypeReference<StorageParam> TypeReference = new TypeReference<StorageParam>(){};
    private String loc, un;

    public String getLoc() {
        return loc;
    }
    public void setLoc(String loc) {
        this.loc = loc;
    }

    public String getUn() {
        return un;
    }
    public void setUn(String un) {
        this.un = un;
    }
}
