package yyy.xxx.simpfw.framework.auth.bo.gxca;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@EqualsAndHashCode(callSuper = true)
@Data
public class CreateSignRequest extends BaseRequest {

    private String doctorNum;

    private String signTitle;

    private String signDesc;

    @ToString.Exclude
    private String signContent;

    private String signType = "0" ; //默认是普通签名

    private String resultType;

    public CreateSignRequest() {
        this.setApiPath("/portal/doctor/sign/create");
    }
}
