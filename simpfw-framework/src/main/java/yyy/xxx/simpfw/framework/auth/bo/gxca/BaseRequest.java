package yyy.xxx.simpfw.framework.auth.bo.gxca;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.function.Consumer;

@Data
public class BaseRequest {

    @JSONField(serialize = false, deserialize = false)
    private String baseUrl;

    @JSONField(serialize = false, deserialize = false)
    private String apiPath;

    private String appId;

    // 受保护的构造函数供子类使用
    protected BaseRequest() {}

    // 静态工厂方法
    public static <R extends BaseRequest> Builder<R> builder(Class<R> requestClass) {
        try {
            R request = requestClass.getDeclaredConstructor().newInstance();
            return new Builder<>(request);
        } catch (Exception e) {
            throw new RuntimeException("Failed to create instance of " + requestClass, e);
        }
    }

    public static class Builder<R extends BaseRequest> {
        protected final R request;

        protected Builder(R request) {
            this.request = request;
        }

        public Builder<R> baseUrl(String baseUrl) {
            request.setBaseUrl(baseUrl);
            return this;
        }

        public Builder<R> appId(String appId) {
            request.setAppId(appId);
            return this;
        }

        public R build() {
            return request;
        }

        public Builder<R> with(Consumer<R> consumer) {
            consumer.accept(request);
            return this;
        }
    }

    @JSONField(serialize = false, deserialize = false)
    public String getRequestUrl() {
        return baseUrl + apiPath;
    }
}
