<template>
<el-dialog ref="reportViewerDialog" :visible.sync="opened"
 class="popupdialog" 
 width="826px" top="0px" 
 close-on-click-modal append-to-body modal-append-to-body destroy-on-close>
<div slot="title" class="dialog-slot-title dialog-slot-title-tools">
  <span>报告预览</span>
  <span v-if="null!=report" style="font-size: 80%;font-weight: normal; margin-left: 20px">检查项目：{{report.examItem.dictLabel}}</span>
  <span class="fr">
    <span v-if="extOperExportAsDoc">
      <el-button type="primary" size="mini" disabled>正在生成报告....</el-button>
    </span>
    <span v-else>
      <el-button type="primary" size="mini" @click="asReportTemplate" v-hasPermi="['exam-report:tpl']">制作模板</el-button>
      <el-button type="primary" size="mini" @click="audit" v-if="auditable" v-hasPermi="['exam-report:audit']">审核报告</el-button>
      <el-button type="primary" size="mini" @click="plint" v-hasPermi="['exam-report:print']">打印报告</el-button>
      <el-button type="primary" size="mini" @click="switchPageSize">{{pageFitted? '实际效果' : '适应页面'}}</el-button>
      <el-button type="primary" size="mini" @click="switchPdf">{{orgPdfReport? '签名报告' : '原始报告'}}</el-button>
    </span>
  </span>
</div>
<div class="report-previewer-wrap" v-loading="extOperExportAsDoc" @contextmenu.prevent="() => $event.preventDefault()">
  <div v-for="pn in numPdfPages" :data-page="'report-previewer-page-' + pn" class="report-previewer-page a4page">
    <canvas v-if="mime.isPDF" style="margin-top: 15px; margin-left: 15px; height: 1101px;width: 779px;" />
    <img v-if="mime.isJPG && !!imagesDoc" :src="'data:image/jpeg;base64,'+imagesDoc[pn - 1]" />
  </div>
</div>
</el-dialog>
</template>

<style scoped src="@/assets/styles/pacs/report/ReportPreviewer.css"></style>
<style scoped>
div.report-previewer-wrap.a4page{
  padding: 0;
}
div.report-previewer-page, div.report-previewer-page canvas, div.report-previewer-page img{
  width: 100%;
  height: 100%;
}
div.report-previewer-page canvas{
  margin: 0 auto;
}
</style>

<script>
export {default} from "@/assets/scripts/uis/report/comp/ReportViewer"
</script>
