package yyy.xxx.simpfw.module.uis.service;

import java.util.List;

import yyy.xxx.simpfw.module.pacs.entity.WorkReportInfo;
import yyy.xxx.simpfw.module.pacs.entity.WorkReportTotalInfo;
import yyy.xxx.simpfw.module.uis.vo.WorkReportVo;
import yyy.xxx.simpfw.module.pacs.entity.ExamInfo;
import yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo;

public interface ExamWorkReportInfoService {

    List<WorkReportInfo> selectDoctorWorkReportList(ExamInfo param);
    WorkReportTotalInfo selectDoctorWorkReportTotal(ExamInfoVo param);

    Object selectDoctorPartsReport(WorkReportVo param);
}
