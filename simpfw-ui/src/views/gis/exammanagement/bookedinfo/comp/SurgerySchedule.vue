<template>
  <el-dialog
    title="手术排班"
    :visible="opened"
    custom-class="popupdialog"
    append-to-body
    @close="close"
  >
    <div class="searchForm">
      <el-form
        ref="searchForm"
        :model="searchForm"
        size="small"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="排班日期" prop="appointExamDate">
          <el-date-picker
            v-model="searchForm.appointExamDate"
            :clearable="false"
            type="date"
            placeholder="选择日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click="search()"
          ></el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="schedule">
      <!-- <el-scrollbar class="bor" style="height:300px;"> -->
      <el-table
        :key="iteKey"
        ref="table"
        :data="tableData"
        border
        style="width: 100%; font-size: 16px"
        height="600"
        :show-header="false"
      >
        <el-table-column prop="row" label="时间" width="130" align="center" fixed>
          <template slot-scope="scope">
            <p>{{ getTime(scope.$index) }}</p>
          </template>
        </el-table-column>

        <el-table-column
          v-for="(item, index) in roomArr"
          :key="index"
          :label="item"
          align="center"
        >
          <template slot-scope="scope">
            <div
              @drop="drop($event, scope.$index, index)"
              @dragover="allowDrop"
            >
              <p
                :draggable="draggable(scope.$index, index)"
                class="contitem"
                @dragstart="drag($event, scope.$index, index)"
              >
                {{ getName(scope.$index, index) }}
              </p>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- </el-scrollbar> -->
      <div class="button-sc">
        <el-row :gutter="10">
          <el-col :span="2.5">
            <el-button type="primary" @click="submit">保存</el-button>
          </el-col>
          <el-col :span="2.5">
            <el-button type="primary" @click="close">取消</el-button>
          </el-col>
          <el-col :span="2.5">
            <el-button type="primary" @click="print">打印</el-button>
          </el-col>
        </el-row>
      </div>
    </div>


    <SurgerySchedulePrint ref="SurgerySchedulePrint"/>
  </el-dialog>
</template>

<script>
import model from "@/assets/scripts/gis/exammanagement/bookedinfo/comp/SurgerySchedule";
export default model;
</script>
<style lang='scss' scoped>
.button-sc {
  float: right;
  padding: 10px;
}

@media print{
  .searchForm
  {
    display:none;
  }

  .button-sc
  {
    display:none;
  }

  .schedule
  {
    display:none;
  }

}

//@import url(); 引入公共css类
.schedule {
  height: calc(100% - 50px);
  overflow: auto;
}
.option {
  display: flex;
  padding: 10px;
  align-items: center;
  justify-content: space-between;
  width: 350px;
  p {
  }
  p:nth-child(2) {
    text-align: center;
    width: 70px;
    cursor: pointer;
    color: #fff;
    border-radius: 5px;
    padding: 8px 10px;
    background: #cf70b39d;
  }
  p:nth-child(3) {
    text-align: center;
    width: 70px;
    cursor: pointer;
    color: #fff;
    border-radius: 5px;
    padding: 8px 10px;
    background: #93d59a;
  }
  p:nth-child(4) {
    text-align: center;
    width: 70px;
    cursor: pointer;
    color: #fff;
    border-radius: 5px;
    padding: 8px 10px;
    background: #65acff;
  }
  .activeNum {
  }
}
.contitem {
  text-align: center;
  height: 38px;
  line-height: 38px;
  border-radius: 5px;
  font-size: 14px;
  cursor: pointer;
}
>>> .el-table__body tr:hover > td,
>>> .el-table__body tr:hover > tr,
>>> .hover-row {
  background-color: transparent !important;
}
>>> .el-table__row {
  height: 55px;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  p {
    width: 100px;
  }
  >>> .el-checkbox-group {
    width: 110px;
  }
}

.tableBar >>> .el-button {
  margin-left: 4px;
  padding: 10px;
}
</style>
